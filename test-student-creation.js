const { Client, credentials } = require('@grpc/grpc-js');
const { loadPackageDefinition } = require('@grpc/grpc-js');
const { loadSync } = require('@grpc/proto-loader');
const path = require('path');

// Load the proto file
const PROTO_PATH = path.join(__dirname, 'libs/shared/dto/src/lib/auth/auth.proto');
const packageDefinition = loadSync(PROTO_PATH, {
  keepCase: true,
  longs: String,
  enums: String,
  defaults: true,
  oneofs: true
});

const authProto = loadPackageDefinition(packageDefinition);

// Create gRPC client with proper credentials
const client = new authProto.auth.AuthService(
  'localhost:50051',
  credentials.createInsecure()
);

// Test data
const testStudentData = {
  organizationId: 1,
  agencyId: null,
  firstName: "Test",
  lastName: "Student",
  nameInNative: "টেস্ট স্টুডেন্ট",
  email: "<EMAIL>",
  phone: "+1234567890",
  guardianPhone: "+1234567891",
  dateOfBirth: "1995-05-16",
  gender: "male",
  fatherName: "Test Father",
  motherName: "Test Mother",
  nid: "123456789015",
  passport: "A123456789",
  presentAddress: {
    address: "123 Test St.",
    country: "Bangladesh",
    state: "Dhaka",
    city: "Dhaka",
    postalCode: "1200"
  },
  permanentAddress: {
    address: "456 Test Home St.",
    country: "Bangladesh",
    state: "Dhaka",
    city: "Dhaka",
    postalCode: "1200"
  },
  maritalStatus: {
    status: "single",
    spouseName: "",
    spousePhone: "",
    spousePassport: ""
  },
  sponsor: {
    name: "Test Sponsor",
    relation: "father",
    phone: "+1234567891",
    email: "<EMAIL>"
  },
  emergencyContact: {
    lastName: "Contact",
    middleName: "",
    firstName: "Emergency",
    phoneHome: "+1234567892",
    phoneMobile: "+1234567893",
    relation: "mother"
  },
  preferredSubject: [
    "Computer Science",
    "Engineering"
  ],
  preferredCountry: [
    "USA",
    "Canada"
  ],
  socialLinks: [
    {
      platform: "linkedin",
      url: "https://linkedin.com/in/teststudent"
    }
  ],
  reference: "Test Reference",
  note: "Test Note"
};

async function testStudentCreation() {
  try {
    console.log('🧪 Starting student creation test...');
    
    // Step 1: Test user creation via auth service
    console.log('\n📝 Step 1: Testing user creation...');
    
    const registerRequest = {
      name: `${testStudentData.firstName} ${testStudentData.lastName}`,
      email: testStudentData.email,
      password: 'TempPassword123!',
      phone: testStudentData.phone,
      nationality: 'Bangladeshi',
      organizationName: 'ApplyGoal',
      roleName: 'Student',
      departmentName: 'Students',
      ipAddress: '127.0.0.1',
      userAgent: 'Test Script'
    };

    console.log('📤 Register request:', JSON.stringify(registerRequest, null, 2));

    // Test user registration
    client.register(registerRequest, (error, response) => {
      if (error) {
        console.error('❌ User registration failed:', error.message);
        return;
      }

      console.log('✅ User registration response:', JSON.stringify(response, null, 2));
      
      if (response.success && response.userId) {
        console.log(`🎉 User created successfully with ID: ${response.userId}`);
        console.log(`🔗 Student data will be linked with userId: ${response.userId}`);
      } else {
        console.log('⚠️ User creation response:', response.message);
      }
    });

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testStudentCreation();

// Keep the process alive for gRPC calls
setTimeout(() => {
  console.log('\n🏁 Test completed');
  process.exit(0);
}, 5000); 