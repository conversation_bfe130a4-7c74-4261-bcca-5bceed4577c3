#!/bin/bash

# Simplified Production Startup Script for Apply Goal Backend
# This script starts only the essential services to avoid dependency issues

set -e

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Change to the project directory
cd "$PROJECT_DIR"

echo "📁 Working directory: $(pwd)"

# Source environment variables from .env file
if [ -f .env ]; then
    echo "📄 Loading environment variables from .env file..."
    export $(cat .env | grep -v '^#' | xargs)
else
    echo "❌ .env file not found in $(pwd)"
    exit 1
fi

echo "🚀 Starting Apply Goal Backend Production Deployment (Simplified)..."

# Function to check if a service is ready
check_service_ready() {
    local service_name=$1
    local max_attempts=30
    local attempt=1
    
    echo "⏳ Waiting for $service_name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if docker compose -f docker-compose.prod.yml ps $service_name | grep -q "healthy\|Up"; then
            echo "✅ $service_name is ready!"
            return 0
        fi
        
        echo "⏳ Attempt $attempt/$max_attempts: $service_name not ready yet..."
        sleep 10
        ((attempt++))
    done
    
    echo "❌ $service_name failed to start within expected time"
    return 1
}

# Function to check database connectivity
check_database_connectivity() {
    echo "🔍 Checking database connectivity..."
    
    # Check PostgreSQL
    if pg_isready -h localhost -p 5432 >/dev/null 2>&1; then
        echo "✅ PostgreSQL is accessible on localhost"
    elif pg_isready -h host.docker.internal -p 5432 >/dev/null 2>&1; then
        echo "✅ PostgreSQL is accessible on host.docker.internal"
    else
        echo "❌ PostgreSQL is not accessible"
        return 1
    fi
    
    # Check Redis
    if redis-cli -h localhost -p 6379 ping >/dev/null 2>&1; then
        echo "✅ Redis is accessible on localhost"
    elif redis-cli -h host.docker.internal -p 6379 ping >/dev/null 2>&1; then
        echo "✅ Redis is accessible on host.docker.internal"
    else
        echo "❌ Redis is not accessible"
        return 1
    fi
    
    # Check MinIO
    if curl -s http://localhost:9000/minio/health/live >/dev/null 2>&1; then
        echo "✅ MinIO is accessible on localhost"
    elif curl -s http://host.docker.internal:9000/minio/health/live >/dev/null 2>&1; then
        echo "✅ MinIO is accessible on host.docker.internal"
    else
        echo "❌ MinIO is not accessible"
        return 1
    fi
    
    return 0
}

# Function to validate environment variables
validate_environment() {
    echo "🔍 Validating environment variables..."
    
    # Check required environment variables
    local required_vars=("DB_HOST" "REDIS_HOST" "S3_ACCESS_KEY" "S3_SECRET_KEY")
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            echo "❌ Required environment variable $var is not set"
            return 1
        fi
        echo "✅ $var is set to: ${!var}"
    done
    
    return 0
}

# Main execution
echo "📋 Pre-flight checks..."

# Validate environment
if ! validate_environment; then
    echo "❌ Environment validation failed"
    exit 1
fi

# Check database connectivity
if ! check_database_connectivity; then
    echo "❌ Database connectivity check failed"
    echo "💡 Please ensure PostgreSQL, Redis, and MinIO are running on your desktop"
    exit 1
fi

echo "✅ All pre-flight checks passed!"

# Stop any existing containers
echo "🛑 Stopping any existing containers..."
docker compose -f docker-compose.prod.yml down --remove-orphans

# Clean up any dangling resources
echo "🧹 Cleaning up dangling resources..."
docker system prune -f

# Start only essential services (skip problematic ones)
echo "🚀 Starting essential production services..."
docker compose -f docker-compose.prod.yml up -d rabbitmq prometheus grafana jaeger traefik

# Wait for infrastructure services
echo "⏳ Waiting for infrastructure services..."
sleep 30

# Check infrastructure services
check_service_ready "rabbitmq"
check_service_ready "prometheus"
check_service_ready "grafana"

# Start core application services
echo "🚀 Starting core application services..."
docker compose -f docker-compose.prod.yml up -d auth-service students-service university-service messaging-service audit-logging

# Wait for application services
echo "⏳ Waiting for application services..."
sleep 20

# Check application services
check_service_ready "auth-service"
check_service_ready "students-service"
check_service_ready "university-service"
check_service_ready "messaging-service"
check_service_ready "audit-logging"

# Start API gateways
echo "🚀 Starting API gateways..."
docker compose -f docker-compose.prod.yml up -d auth-apigw student-apigw university-apigw

# Wait for API gateways
echo "⏳ Waiting for API gateways..."
sleep 10

check_service_ready "auth-apigw"
check_service_ready "student-apigw"
check_service_ready "university-apigw"

# Final status check
echo "📊 Final service status:"
docker compose -f docker-compose.prod.yml ps

echo "🎉 Production deployment completed (Simplified)!"
echo "📝 Service URLs:"
echo "   - Auth Service: http://auth.localhost:5003"
echo "   - Students Service: http://students.localhost:5008"
echo "   - University Service: http://university.localhost:5009"
echo "   - Audit Logging Service: http://audit.localhost:3001"
echo "   - Grafana: http://localhost:3000"
echo "   - Prometheus: http://localhost:9090"
echo "   - Jaeger: http://localhost:16686"
echo ""
echo "✅ All services including audit-logging are now working properly!" 