#!/bin/bash

# Test script for Super Admin Student API Access
# This script tests all student APIs using a superadmin account

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BASE_URL="http://localhost:4007"
AUTH_URL="http://localhost:4006"

# Test data
SUPERADMIN_EMAIL="<EMAIL>"
SUPERADMIN_PASSWORD="superadmin123"

# Sample student data for testing
STUDENT_DATA='{
  "firstName": "John",
  "lastName": "Doe",
  "nameInNative": "জোন ডো",
  "email": "<EMAIL>",
  "phone": "+**********",
  "guardianPhone": "+**********",
  "dateOfBirth": "1995-05-15",
  "gender": "Male",
  "fatherName": "<PERSON>",
  "motherName": "<PERSON>",
  "nid": "****************",
  "passport": "A12345678",
  "presentAddress": {
    "address": "123 Main Street",
    "country": "Bangladesh",
    "state": "Dhaka",
    "city": "Dhaka",
    "postalCode": "1200"
  },
  "permanentAddress": {
    "address": "456 Old Street",
    "country": "Bangladesh",
    "state": "Dhaka",
    "city": "Dhaka",
    "postalCode": "1200"
  },
  "maritalStatus": {
    "status": "Single",
    "spouseName": "",
    "spousePhone": "",
    "spousePassport": ""
  },
  "sponsor": {
    "name": "Robert Doe",
    "relation": "Father",
    "phone": "+**********",
    "email": "<EMAIL>"
  },
  "emergencyContact": {
    "lastName": "Doe",
    "middleName": "",
    "firstName": "Jane",
    "phoneHome": "+1234567892",
    "phoneMobile": "+1234567893",
    "relation": "Mother"
  },
  "preferredSubject": ["Computer Science", "Engineering"],
  "preferredCountry": ["Canada", "Australia"],
  "socialLinks": [
    {
      "platform": "LinkedIn",
      "url": "https://linkedin.com/in/johndoe"
    }
  ],
  "reference": "Referred by university counselor",
  "note": "Excellent academic record"
}'

ACADEMIC_DATA='{
  "academic": [
    {
      "foreignDegree": false,
      "nameOfExam": "HSC",
      "institute": "Dhaka College",
      "subject": "Science",
      "board": "Dhaka",
      "grade": "A+",
      "passingYear": "2013"
    }
  ],
  "proficiency": [
    {
      "nameOfExam": "IELTS",
      "score": {
        "overall": 7.5,
        "R": 8.0,
        "L": 7.5,
        "W": 7.0,
        "S": 7.5
      },
      "examDate": "2023-06-15",
      "expiryDate": "2025-06-15",
      "note": "Good overall score"
    }
  ],
  "publications": [],
  "otherActivities": {
    "subject": "Programming",
    "certificationLink": "https://example.com/cert",
    "startDate": "2023-01-01",
    "endDate": "2023-12-31"
  }
}'

# Function to print test results
print_result() {
    local test_name="$1"
    local status="$2"
    local response="$3"
    
    if [ "$status" = "success" ]; then
        echo -e "${GREEN}✅ $test_name${NC}"
        echo -e "${BLUE}Response:${NC} $response"
    else
        echo -e "${RED}❌ $test_name${NC}"
        echo -e "${RED}Error:${NC} $response"
    fi
    echo ""
}

# Function to test an endpoint
test_endpoint() {
    local method="$1"
    local endpoint="$2"
    local token="$3"
    local data="$4"
    local test_name="$5"
    
    echo -e "${YELLOW}Testing: $test_name${NC}"
    
    local curl_cmd="curl -s -w '\nHTTP_STATUS:%{http_code}'"
    
    if [ -n "$token" ]; then
        curl_cmd="$curl_cmd -H 'Authorization: Bearer $token'"
    fi
    
    if [ -n "$data" ]; then
        curl_cmd="$curl_cmd -H 'Content-Type: application/json' -d '$data'"
    fi
    
    curl_cmd="$curl_cmd -X $method '$BASE_URL$endpoint'"
    
    local response=$(eval $curl_cmd)
    local http_status=$(echo "$response" | grep "HTTP_STATUS:" | cut -d':' -f2)
    local body=$(echo "$response" | sed '/HTTP_STATUS:/d')
    
    if [ "$http_status" -ge 200 ] && [ "$http_status" -lt 300 ]; then
        print_result "$test_name" "success" "$body"
    else
        print_result "$test_name" "error" "HTTP $http_status: $body"
    fi
}

echo -e "${BLUE}🚀 Starting Super Admin Student API Tests${NC}"
echo "=================================================="

# Step 1: Login as Super Admin
echo -e "${YELLOW}Step 1: Logging in as Super Admin${NC}"
LOGIN_RESPONSE=$(curl -s -X POST "$AUTH_URL/api/auth/login" \
  -H "Content-Type: application/json" \
  -d "{
    \"email\": \"$SUPERADMIN_EMAIL\",
    \"password\": \"$SUPERADMIN_PASSWORD\"
  }")

# Extract token from login response
TOKEN=$(echo "$LOGIN_RESPONSE" | grep -o '"accessToken":"[^"]*"' | cut -d'"' -f4)

if [ -z "$TOKEN" ]; then
    echo -e "${RED}❌ Failed to login as Super Admin${NC}"
    echo "Response: $LOGIN_RESPONSE"
    echo ""
    echo -e "${YELLOW}📝 Instructions:${NC}"
    echo "1. Make sure the auth service is running: docker compose up -d auth-service auth-apigw"
    echo "2. Ensure the superadmin account exists with email: $SUPERADMIN_EMAIL"
    echo "3. Check if the password is correct: $SUPERADMIN_PASSWORD"
    echo "4. Verify the auth service is accessible at: $AUTH_URL"
    exit 1
fi

echo -e "${GREEN}✅ Successfully logged in as Super Admin${NC}"
echo -e "${BLUE}Token:${NC} ${TOKEN:0:50}..."
echo ""

# Step 2: Test Public Registration (should work without token)
echo -e "${YELLOW}Step 2: Testing Public Registration${NC}"
test_endpoint "POST" "/api/student/register" "" "$STUDENT_DATA" "Public Student Registration"

# Step 3: Test Create Student (requires SPM_CREATE)
echo -e "${YELLOW}Step 3: Testing Create Student${NC}"
test_endpoint "POST" "/api/student" "$TOKEN" "$STUDENT_DATA" "Create Student (SPM_CREATE)"

# Step 4: Test List Students (requires SPM_VIEW)
echo -e "${YELLOW}Step 4: Testing List Students${NC}"
test_endpoint "GET" "/api/student" "$TOKEN" "" "List Students (SPM_VIEW)"

# Step 5: Test Get Student by ID (requires SPM_VIEW)
echo -e "${YELLOW}Step 5: Testing Get Student by ID${NC}"
test_endpoint "GET" "/api/student/1" "$TOKEN" "" "Get Student by ID (SPM_VIEW)"

# Step 6: Test Update Student (requires SPM_EDIT)
echo -e "${YELLOW}Step 6: Testing Update Student${NC}"
test_endpoint "PUT" "/api/student/1" "$TOKEN" "$STUDENT_DATA" "Update Student (SPM_EDIT)"

# Step 7: Test Academic Information (requires SPM_EDIT)
echo -e "${YELLOW}Step 7: Testing Academic Information${NC}"
test_endpoint "POST" "/api/student/1/academic" "$TOKEN" "$ACADEMIC_DATA" "Create/Update Academic Info (SPM_EDIT)"

# Step 8: Test Get Academic Information (requires SPM_VIEW)
echo -e "${YELLOW}Step 8: Testing Get Academic Information${NC}"
test_endpoint "GET" "/api/student/1/academic" "$TOKEN" "" "Get Academic Info (SPM_VIEW)"

# Step 9: Test Enrollments (requires SPM_EDIT)
echo -e "${YELLOW}Step 9: Testing Enrollments${NC}"
test_endpoint "POST" "/api/student/1/enrollments" "$TOKEN" '{"course_id": "CS101", "semester": "Fall2024"}' "Enroll in Course (SPM_EDIT)"

# Step 10: Test Get Enrollments (requires SPM_VIEW)
echo -e "${YELLOW}Step 10: Testing Get Enrollments${NC}"
test_endpoint "GET" "/api/student/1/enrollments" "$TOKEN" "" "Get Enrollments (SPM_VIEW)"

# Step 11: Test Academic Progress (requires SPM_VIEW)
echo -e "${YELLOW}Step 11: Testing Academic Progress${NC}"
test_endpoint "GET" "/api/student/1/academic-progress" "$TOKEN" "" "Get Academic Progress (SPM_VIEW)"

# Step 12: Test Transcript (requires SPM_VIEW)
echo -e "${YELLOW}Step 12: Testing Transcript${NC}"
test_endpoint "GET" "/api/student/1/transcript" "$TOKEN" "" "Get Transcript (SPM_VIEW)"

# Step 13: Test Universities (requires SPM_VIEW)
echo -e "${YELLOW}Step 13: Testing Universities${NC}"
test_endpoint "GET" "/api/student/universities" "$TOKEN" "" "List Universities (SPM_VIEW)"

# Step 14: Test Agencies (requires SPM_VIEW)
echo -e "${YELLOW}Step 14: Testing Agencies${NC}"
test_endpoint "GET" "/api/student/agencies" "$TOKEN" "" "List Agencies (SPM_VIEW)"

# Step 15: Test Delete Student (requires SPM_DELETE)
echo -e "${YELLOW}Step 15: Testing Delete Student${NC}"
test_endpoint "DELETE" "/api/student/1" "$TOKEN" "" "Delete Student (SPM_DELETE)"

# Step 16: Test Legacy Endpoints
echo -e "${YELLOW}Step 16: Testing Legacy Endpoints${NC}"
test_endpoint "POST" "/api/student/legacy" "$TOKEN" '{
  "first_name": "Jane",
  "last_name": "Smith",
  "email": "<EMAIL>",
  "phone": "+**********",
  "date_of_birth": {"year": 1995, "month": 6, "day": 15},
  "major": "Computer Science",
  "address": {
    "street": "123 Main St",
    "city": "Dhaka",
    "state": "Dhaka",
    "postal_code": "1200",
    "country": "Bangladesh"
  }
}' "Legacy Create Student (SPM_CREATE)"

echo -e "${GREEN}🎉 All Super Admin Student API Tests Completed!${NC}"
echo ""
echo -e "${BLUE}📊 Summary:${NC}"
echo "✅ Public registration should work without authentication"
echo "✅ All CRUD operations should work with Super Admin token"
echo "✅ Academic operations should work with Super Admin token"
echo "✅ University and Agency integrations should work"
echo ""
echo -e "${YELLOW}📝 Notes:${NC}"
echo "1. Super Admin has all permissions: SPM_VIEW, SPM_CREATE, SPM_EDIT, SPM_DELETE"
echo "2. If any test fails, check the service logs: docker compose logs student-apigw"
echo "3. Verify the student service is running: docker compose logs students-service"
echo "4. Check auth service logs: docker compose logs auth-service" 