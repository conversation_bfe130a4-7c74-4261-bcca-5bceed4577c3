# 🎓 Complete Application Process Guide
## From Student Registration to University Enrollment

This document provides a comprehensive guide to the complete application process in the ApplyGoal system, including all API endpoints and curl commands for each step.

---

## 📋 Table of Contents

1. [Prerequisites](#prerequisites)
2. [Phase 1: Student Registration](#phase-1-student-registration)
3. [Phase 2: Student Profile Completion](#phase-2-student-profile-completion)
4. [Phase 3: University Discovery](#phase-3-university-discovery)
5. [Phase 4: Application Creation](#phase-4-application-creation)
6. [Phase 5: Document Upload](#phase-5-document-upload)
7. [Phase 6: Application Management](#phase-6-application-management)
8. [Phase 7: Payment Processing](#phase-7-payment-processing)
9. [Phase 8: Enrollment](#phase-8-enrollment)
10. [API Reference](#api-reference)

---

## 🔧 Prerequisites

### System Setup
```bash
# Start all services
docker compose up -d

# Check service health
docker compose ps

# Get authentication token
curl -X POST "http://localhost:4006/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "superadmin123",
    "ipAddress": "127.0.0.1"
  }'
```

### Environment Variables
- **Student API Gateway**: `http://localhost:4007`
- **Auth API Gateway**: `http://localhost:4006`
- **University API Gateway**: `http://localhost:4008`
- **Payment API Gateway**: `http://localhost:4009`

---

## 📝 Phase 1: Student Registration

### Step 1.1: Public Student Registration

**Endpoint**: `POST /student/register`

**Description**: Initial student registration (public endpoint)

**Curl Command**:
```bash
curl -X POST "http://localhost:4007/api/student/register" \
  -H "Content-Type: application/json" \
  -d '{
    "firstName": "John",
    "lastName": "Doe",
    "nameInNative": "জোন ডো",
    "email": "<EMAIL>",
    "phone": "+**********",
    "guardianPhone": "+**********",
    "dateOfBirth": "1995-05-16",
    "gender": "male",
    "fatherName": "Robert Doe",
    "motherName": "Jane Doe",
    "nid": "**********123456",
    "passport": "A12345678",
    "presentAddress": {
      "address": "123 Main St.",
      "country": "Bangladesh",
      "state": "Dhaka",
      "city": "Dhaka",
      "postalCode": "1200"
    },
    "permanentAddress": {
      "address": "456 Home St.",
      "country": "Bangladesh",
      "state": "Dhaka",
      "city": "Dhaka",
      "postalCode": "1200"
    },
    "maritalStatus": {
      "status": "single",
      "spouseName": "",
      "spousePhone": "",
      "spousePassport": ""
    },
    "sponsor": {
      "name": "Robert Doe",
      "relation": "father",
      "phone": "+**********",
      "email": "<EMAIL>"
    },
    "emergencyContact": {
      "lastName": "Doe",
      "middleName": "",
      "firstName": "Jane",
      "phoneHome": "+1234567892",
      "phoneMobile": "+1234567893",
      "relation": "mother"
    },
    "preferredSubject": ["Computer Science", "Engineering"],
    "preferredCountry": ["USA", "Canada"],
    "socialLinks": [
      {
        "platform": "linkedin",
        "url": "https://linkedin.com/in/johndoe"
      }
    ],
    "reference": "University Professor",
    "note": "Excellent student with strong academic background"
  }'
```

**Expected Response**:
```json
{
  "success": true,
  "message": "Student created successfully",
  "data": {
    "student": {
      "studentId": "ST123456",
      "status": "active",
      "personalInfo": {
        "firstName": "John",
        "lastName": "Doe",
        "email": "<EMAIL>"
      }
    }
  }
}
```

---

## 👤 Phase 2: Student Profile Completion

### Step 2.1: Student Login

**Endpoint**: `POST /auth/login`

**Description**: Student authenticates to access protected endpoints

**Curl Command**:
```bash
curl -X POST "http://localhost:4006/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "DefaultPassword123!",
    "ipAddress": "127.0.0.1"
  }'
```

**Expected Response**:
```json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "roles": ["Student"]
  }
}
```

### Step 2.2: Update Student Profile

**Endpoint**: `POST /student`

**Description**: Update or complete student profile information

**Curl Command**:
```bash
curl -X POST "http://localhost:4007/api/student" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "studentId": "ST123456",
    "firstName": "John",
    "lastName": "Doe",
    "nameInNative": "জোন ডো",
    "email": "<EMAIL>",
    "phone": "+**********",
    "guardianPhone": "+**********",
    "dateOfBirth": "1995-05-16",
    "gender": "male",
    "fatherName": "Robert Doe",
    "motherName": "Jane Doe",
    "nid": "**********123456",
    "passport": "A12345678",
    "presentAddress": {
      "address": "123 Main St.",
      "country": "Bangladesh",
      "state": "Dhaka",
      "city": "Dhaka",
      "postalCode": "1200"
    },
    "permanentAddress": {
      "address": "456 Home St.",
      "country": "Bangladesh",
      "state": "Dhaka",
      "city": "Dhaka",
      "postalCode": "1200"
    },
    "maritalStatus": {
      "status": "single",
      "spouseName": "",
      "spousePhone": "",
      "spousePassport": ""
    },
    "sponsor": {
      "name": "Robert Doe",
      "relation": "father",
      "phone": "+**********",
      "email": "<EMAIL>"
    },
    "emergencyContact": {
      "lastName": "Doe",
      "middleName": "",
      "firstName": "Jane",
      "phoneHome": "+1234567892",
      "phoneMobile": "+1234567893",
      "relation": "mother"
    },
    "preferredSubject": ["Computer Science", "Engineering"],
    "preferredCountry": ["USA", "Canada"],
    "socialLinks": [
      {
        "platform": "linkedin",
        "url": "https://linkedin.com/in/johndoe"
      }
    ],
    "reference": "University Professor",
    "note": "Excellent student with strong academic background"
  }'
```

### Step 2.3: Add Academic Information

**Endpoint**: `POST /student/{studentId}/academic`

**Description**: Add academic background and qualifications

**Curl Command**:
```bash
curl -X POST "http://localhost:4007/api/student/ST123456/academic" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "academic": [
      {
        "foreignDegree": false,
        "nameOfExam": "SSC",
        "institute": "Dhaka High School",
        "subject": "Science",
        "board": "Dhaka",
        "grade": "A+",
        "passingYear": "2011"
      },
      {
        "foreignDegree": false,
        "nameOfExam": "HSC",
        "institute": "Dhaka College",
        "subject": "Science",
        "board": "Dhaka",
        "grade": "A+",
        "passingYear": "2013"
      },
      {
        "foreignDegree": false,
        "nameOfExam": "BSc",
        "institute": "University of Dhaka",
        "subject": "Computer Science and Engineering",
        "board": "University of Dhaka",
        "grade": "3.85",
        "passingYear": "2017"
      }
    ],
    "proficiency": [
      {
        "nameOfExam": "IELTS",
        "score": {
          "overall": 7.5,
          "R": 8.0,
          "L": 7.5,
          "W": 7.0,
          "S": 7.5
        },
        "examDate": "2023-06-15",
        "expiryDate": "2025-06-15",
        "note": "Academic module"
      }
    ],
    "publications": [
      {
        "subject": "Machine Learning",
        "journal": "IEEE Transactions",
        "publicationDate": "2023-03-15",
        "link": "https://ieee.org/paper123"
      }
    ],
    "otherActivities": [
      {
        "subject": "Web Development",
        "certificationLink": "https://udemy.com/certificate",
        "startDate": "2022-01-01",
        "endDate": "2022-03-01"
      }
    ]
  }'
```

---

## 🏫 Phase 3: University Discovery

### Step 3.1: Browse Universities

**Endpoint**: `GET /student/universities/application`

**Description**: Get list of universities available for applications

**Curl Command**:
```bash
curl -X GET "http://localhost:4007/api/student/universities/application" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

**Expected Response**:
```json
{
  "status": "success",
  "data": [
    {
      "id": 1,
      "name": "University of Toronto",
      "country": "Canada",
      "type": "public",
      "website": "https://utoronto.ca",
      "logo": "https://example.com/logo.png",
      "about": "Leading research university...",
      "courses": [
        {
          "id": 1,
          "title": "Bachelor of Computer Science",
          "duration": "4 years",
          "applicationFee": 150,
          "tuitionFee": 45000
        }
      ]
    }
  ]
}
```

### Step 3.2: Get University Details

**Endpoint**: `GET /student/universities/{universityId}`

**Description**: Get detailed information about a specific university

**Curl Command**:
```bash
curl -X GET "http://localhost:4007/api/student/universities/1" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### Step 3.3: Get University Courses

**Endpoint**: `GET /student/universities/{universityId}/courses`

**Description**: Get available courses for a university

**Curl Command**:
```bash
curl -X GET "http://localhost:4007/api/student/universities/1/courses" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### Step 3.4: Get University Intakes

**Endpoint**: `GET /student/universities/{universityId}/intakes`

**Description**: Get available intakes (application periods) for a university

**Curl Command**:
```bash
curl -X GET "http://localhost:4007/api/student/universities/1/intakes" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

---

## 📝 Phase 4: Application Creation

### Step 4.1: Create Application

**Endpoint**: `POST /student/applications`

**Description**: Create a new university application

**Curl Command**:
```bash
curl -X POST "http://localhost:4007/api/student/applications" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "studentId": "ST123456",
    "universityId": 1,
    "universityCountryId": 1,
    "universityCountryCampus": 1,
    "programId": 1,
    "intakeId": 1,
    "courseId": 1,
    "note": "Interested in research opportunities",
    "status": "draft",
    "paymentStatus": "pending",
    "applicationType": "undergraduate",
    "currentStage": "document_upload",
    "overallProgress": 25,
    "totalAmount": 150,
    "paidAmount": 0,
    "refundAmount": 0,
    "deliveryMethod": "online",
    "submissionDate": "2024-01-15",
    "deadlineDate": "2024-03-15"
  }'
```

**Expected Response**:
```json
{
  "status": "success",
  "data": {
    "id": 1,
    "applicationId": "APP2024001",
    "studentId": "ST123456",
    "universityId": 1,
    "status": "draft",
    "currentStage": "document_upload",
    "overallProgress": 25,
    "totalAmount": 150,
    "paidAmount": 0,
    "createdAt": "2024-01-15T10:00:00Z"
  },
  "message": "Application created successfully"
}
```

---

## 📄 Phase 5: Document Upload

### Step 5.1: Upload Documents

**Endpoint**: `POST /student/{studentId}/documents`

**Description**: Upload required documents for application

**Curl Command**:
```bash
curl -X POST "http://localhost:4007/api/student/ST123456/documents" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -F "sponsorName=Robert Doe" \
  -F "takeDependents=false" \
  -F "academicSections=[\"ssc\",\"hsc\",\"bachelor\"]" \
  -F "proficiencySections=[\"ielts\"]" \
  -F "sponsorMetadata={\"name\":\"Robert Doe\",\"relation\":\"father\",\"phone\":\"+**********\",\"email\":\"<EMAIL>\"}" \
  -F "photo=@/path/to/photo.jpg" \
  -F "passport=@/path/to/passport.pdf" \
  -F "ssc=@/path/to/ssc_certificate.pdf" \
  -F "hsc=@/path/to/hsc_certificate.pdf" \
  -F "bachelor=@/path/to/bachelor_certificate.pdf" \
  -F "ielts=@/path/to/ielts_certificate.pdf" \
  -F "sponsor_documents=@/path/to/sponsor_documents.pdf"
```

**Expected Response**:
```json
{
  "success": true,
  "message": "Documents uploaded successfully",
  "documents": [
    {
      "id": 1,
      "section": "profile",
      "field": "photo",
      "filename": "photo_123456.jpg",
      "url": "https://minio.example.com/bucket/photo_123456.jpg",
      "fileSize": 1024000,
      "mimeType": "image/jpeg"
    }
  ],
  "totalUploaded": 7,
  "failedUploads": 0,
  "errors": []
}
```

### Step 5.2: Verify Document Upload

**Endpoint**: `GET /student/{studentId}/documents`

**Description**: Check uploaded documents

**Curl Command**:
```bash
curl -X GET "http://localhost:4007/api/student/ST123456/documents" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

---

## 📊 Phase 6: Application Management

### Step 6.1: Get Application Details

**Endpoint**: `GET /student/applications/{applicationId}`

**Description**: View application details and progress

**Curl Command**:
```bash
curl -X GET "http://localhost:4007/api/student/applications/1" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

**Expected Response**:
```json
{
  "status": "success",
  "data": {
    "id": 1,
    "applicationId": "APP2024001",
    "studentId": "ST123456",
    "universityId": 1,
    "status": "under_review",
    "currentStage": "interview_scheduled",
    "overallProgress": 75,
    "totalAmount": 150,
    "paidAmount": 150,
    "documents": [
      {
        "section": "profile",
        "field": "photo",
        "url": "https://minio.example.com/bucket/photo_123456.jpg"
      }
    ],
    "timeline": [
      {
        "stage": "application_created",
        "date": "2024-01-15T10:00:00Z",
        "status": "completed"
      },
      {
        "stage": "documents_uploaded",
        "date": "2024-01-16T14:30:00Z",
        "status": "completed"
      },
      {
        "stage": "under_review",
        "date": "2024-01-17T09:15:00Z",
        "status": "completed"
      },
      {
        "stage": "interview_scheduled",
        "date": "2024-01-20T11:00:00Z",
        "status": "in_progress"
      }
    ]
  }
}
```

### Step 6.2: Update Application

**Endpoint**: `PUT /student/applications/{applicationId}`

**Description**: Update application information

**Curl Command**:
```bash
curl -X PUT "http://localhost:4007/api/student/applications/1" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "status": "interview_completed",
    "currentStage": "conditional_offer",
    "overallProgress": 85,
    "note": "Interview completed successfully"
  }'
```

### Step 6.3: Get Application Progress

**Endpoint**: `GET /student/applications/{applicationId}/progress`

**Description**: Get detailed application progress

**Curl Command**:
```bash
curl -X GET "http://localhost:4007/api/student/applications/1/progress" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### Step 6.4: List All Applications

**Endpoint**: `GET /student/applications`

**Description**: Get all applications for the student

**Curl Command**:
```bash
curl -X GET "http://localhost:4007/api/student/applications?page=1&limit=10" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

---

## 💳 Phase 7: Payment Processing

### Step 7.1: Get Application Fees

**Endpoint**: `GET /student/applications/{applicationId}/fees`

**Description**: Get payment details for application

**Curl Command**:
```bash
curl -X GET "http://localhost:4007/api/student/applications/1/fees" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### Step 7.2: Process Payment

**Endpoint**: `POST /student/applications/{applicationId}/payment`

**Description**: Process application fee payment

**Curl Command**:
```bash
curl -X POST "http://localhost:4007/api/student/applications/1/payment" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "paymentMethod": "credit_card",
    "amount": 150,
    "currency": "USD",
    "cardNumber": "****************",
    "expiryDate": "12/25",
    "cvv": "123"
  }'
```

**Expected Response**:
```json
{
  "status": "success",
  "data": {
    "paymentId": "PAY123456",
    "status": "completed",
    "amount": 150,
    "currency": "USD",
    "transactionId": "TXN789012",
    "paymentDate": "2024-01-15T10:30:00Z"
  },
  "message": "Payment processed successfully"
}
```

### Step 7.3: Verify Payment Status

**Endpoint**: `GET /student/applications/{applicationId}/payment/status`

**Description**: Check payment status

**Curl Command**:
```bash
curl -X GET "http://localhost:4007/api/student/applications/1/payment/status" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

---

## 🎓 Phase 8: Enrollment

### Step 8.1: Accept Offer

**Endpoint**: `PUT /student/applications/{applicationId}/accept`

**Description**: Accept university offer

**Curl Command**:
```bash
curl -X PUT "http://localhost:4007/api/student/applications/1/accept" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "acceptanceDate": "2024-02-15",
    "note": "Excited to join the program"
  }'
```

### Step 8.2: Submit Final Documents

**Endpoint**: `POST /student/{studentId}/documents/final`

**Description**: Upload final enrollment documents

**Curl Command**:
```bash
curl -X POST "http://localhost:4007/api/student/ST123456/documents/final" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -F "visa=@/path/to/visa.pdf" \
  -F "medical=@/path/to/medical_certificate.pdf" \
  -F "insurance=@/path/to/insurance.pdf"
```

### Step 8.3: Complete Enrollment

**Endpoint**: `PUT /student/applications/{applicationId}/enroll`

**Description**: Complete enrollment process

**Curl Command**:
```bash
curl -X PUT "http://localhost:4007/api/student/applications/1/enroll" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "enrollmentDate": "2024-09-01",
    "studentNumber": "2024001",
    "note": "Enrollment completed successfully"
  }'
```

**Expected Response**:
```json
{
  "status": "success",
  "data": {
    "id": 1,
    "applicationId": "APP2024001",
    "status": "enrolled",
    "enrollmentDate": "2024-09-01",
    "studentNumber": "2024001",
    "university": {
      "name": "University of Toronto",
      "campus": "St. George Campus"
    },
    "program": {
      "name": "Bachelor of Computer Science",
      "duration": "4 years"
    }
  },
  "message": "Enrollment completed successfully"
}
```

---

## 📚 API Reference

### Authentication Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `POST` | `/auth/login` | Student login |
| `POST` | `/auth/refresh` | Refresh access token |
| `POST` | `/auth/logout` | Student logout |

### Student Management Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `POST` | `/student/register` | Public student registration |
| `POST` | `/student` | Create/update student profile |
| `GET` | `/student/{id}` | Get student details |
| `PUT` | `/student/{id}` | Update student profile |
| `DELETE` | `/student/{id}` | Delete student |

### Academic Information Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `POST` | `/student/{id}/academic` | Add academic information |
| `GET` | `/student/{id}/academic` | Get academic information |
| `PUT` | `/student/{id}/academic` | Update academic information |

### Document Management Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `POST` | `/student/{id}/documents` | Upload documents |
| `GET` | `/student/{id}/documents` | Get uploaded documents |
| `DELETE` | `/student/{id}/documents/{docId}` | Delete document |

### University Discovery Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/student/universities/application` | Browse universities |
| `GET` | `/student/universities/{id}` | Get university details |
| `GET` | `/student/universities/{id}/courses` | Get university courses |
| `GET` | `/student/universities/{id}/intakes` | Get university intakes |

### Application Management Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `POST` | `/student/applications` | Create application |
| `GET` | `/student/applications/{id}` | Get application details |
| `PUT` | `/student/applications/{id}` | Update application |
| `DELETE` | `/student/applications/{id}` | Delete application |
| `GET` | `/student/applications` | List applications |
| `GET` | `/student/applications/{id}/progress` | Get application progress |

### Payment Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/student/applications/{id}/fees` | Get application fees |
| `POST` | `/student/applications/{id}/payment` | Process payment |
| `GET` | `/student/applications/{id}/payment/status` | Check payment status |

### Enrollment Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `PUT` | `/student/applications/{id}/accept` | Accept offer |
| `POST` | `/student/{id}/documents/final` | Upload final documents |
| `PUT` | `/student/applications/{id}/enroll` | Complete enrollment |

---

## 🔄 Application Status Flow

```
draft → submitted → under_review → interview_scheduled → 
interview_completed → documents_verified → conditional_offer → 
unconditional_offer → accepted → enrolled
```

### Status Descriptions

- **draft**: Application created but not submitted
- **submitted**: Application submitted for review
- **under_review**: Application being reviewed by university
- **interview_scheduled**: Interview scheduled with university
- **interview_completed**: Interview completed
- **documents_verified**: All documents verified
- **conditional_offer**: Conditional admission offer received
- **unconditional_offer**: Unconditional admission offer received
- **accepted**: Student accepted the offer
- **enrolled**: Student officially enrolled

---

## 📊 Progress Tracking

### Application Progress Calculation

- **Document Upload**: 25%
- **Application Review**: 25%
- **Interview Process**: 20%
- **Offer Processing**: 15%
- **Enrollment**: 15%

### Required Documents

1. **Profile Documents**
   - Passport photo
   - Passport copy
   - National ID

2. **Academic Documents**
   - SSC certificate
   - HSC certificate
   - Bachelor's certificate (if applicable)
   - Transcripts

3. **Language Proficiency**
   - IELTS certificate
   - TOEFL certificate (if applicable)

4. **Financial Documents**
   - Sponsor documents
   - Bank statements
   - Financial guarantee

5. **Final Documents** (for enrollment)
   - Visa
   - Medical certificate
   - Insurance

---

## 🚨 Error Handling

### Common Error Responses

```json
{
  "statusCode": 400,
  "message": "Validation error",
  "errors": [
    {
      "field": "email",
      "message": "Email is required"
    }
  ]
}
```

```json
{
  "statusCode": 401,
  "message": "Unauthorized",
  "error": "Invalid token"
}
```

```json
{
  "statusCode": 404,
  "message": "Application not found"
}
```

```json
{
  "statusCode": 409,
  "message": "Application already exists"
}
```

---

## 📞 Support

For technical support or questions about the application process:

- **Email**: <EMAIL>
- **Phone**: ******-0123
- **Documentation**: https://docs.applygoal.com
- **API Status**: https://status.applygoal.com

---

*This document is maintained by the ApplyGoal Development Team. Last updated: January 2024* 