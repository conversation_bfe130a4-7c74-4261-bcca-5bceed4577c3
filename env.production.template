# Production Environment Configuration for apply-goal-backend
# Copy this file to .env and update the values for your production environment

# =============================================================================
# EXTERNAL INFRASTRUCTURE SERVICES
# =============================================================================

# PostgreSQL Database (External - OS installed)
DB_HOST=localhost
DB_PORT=5432

# Redis (External - OS installed)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# MongoDB (External - OS installed or cloud service)
MONGO_URI=****************************************************************************************

# MinIO/S3 Storage (External - OS installed or cloud service)
S3_ENDPOINT=http://your_minio_host:9000
S3_PUBLIC_ENDPOINT=http://your_minio_host:9000
S3_BUCKET=applygoal-files
S3_ACCESS_KEY=your_s3_access_key
S3_SECRET_KEY=your_s3_secret_key
S3_REGION=us-east-1

# =============================================================================
# RABBITMQ CONFIGURATION (Containerized)
# =============================================================================

# RabbitMQ (Containerized - shared network)
RABBITMQ_USER=rabbitmq_user
RABBITMQ_PASS=rabbitmq_pass

# =============================================================================
# JWT & SECURITY
# =============================================================================

# JWT Secret (Change this in production!)
JWT_SECRET=your-super-secret-jwt-key-2024-production

# =============================================================================
# GOOGLE OAUTH (For Auth Service)
# =============================================================================

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# =============================================================================
# EMAIL CONFIGURATION (For Messaging Service)
# =============================================================================

# Gmail API Configuration
GMAIL_CLIENT_ID=your_gmail_client_id
GMAIL_CLIENT_SECRET=your_gmail_client_secret
GMAIL_REFRESH_TOKEN=your_gmail_refresh_token
GMAIL_SENDER=<EMAIL>

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================

# Grafana Admin Credentials
GRAFANA_ADMIN_USER=admin
GRAFANA_ADMIN_PASSWORD=your_grafana_admin_password

# =============================================================================
# SERVICE-SPECIFIC CONFIGURATIONS
# =============================================================================

# Auth Service Configuration
ACCESS_TOKEN_EXPIRY=1h
REFRESH_TOKEN_EXPIRY=7d
REFRESH_EXPIRY_MS=604800000
ACCESS_EXPIRY_MS=3600000

# Students Service Configuration
AUTO_SEED=false
SEED_ON_STARTUP=false
SEED_CLEAR_EXISTING=false
SEED_DEVELOPMENT_ONLY=false
SEED_LOG_LEVEL=warn

# =============================================================================
# NETWORK CONFIGURATION
# =============================================================================

# Traefik Configuration
TRAEFIK_DASHBOARD_HOST=traefik.yourdomain.com
TRAEFIK_METRICS_HOST=metrics.yourdomain.com

# Service Hosts (Update with your domain)
AUTH_API_HOST=auth-api.yourdomain.com
STUDENT_API_HOST=student-api.yourdomain.com
UNIVERSITY_API_HOST=university-api.yourdomain.com
AUDIT_HOST=audit.yourdomain.com
MESSAGING_HOST=messaging.yourdomain.com
AUTH_HOST=auth.yourdomain.com
STUDENTS_HOST=students.yourdomain.com
UNIVERSITY_HOST=university.yourdomain.com

# =============================================================================
# RESOURCE LIMITS (Optional - for Docker Swarm)
# =============================================================================

# Memory limits for services (in MB)
AUDIT_LOGGING_MEMORY_LIMIT=512
MESSAGING_SERVICE_MEMORY_LIMIT=512
AUTH_SERVICE_MEMORY_LIMIT=512
STUDENTS_SERVICE_MEMORY_LIMIT=1024
UNIVERSITY_SERVICE_MEMORY_LIMIT=512
AUTH_APIGW_MEMORY_LIMIT=512
STUDENT_APIGW_MEMORY_LIMIT=512
UNIVERSITY_APIGW_MEMORY_LIMIT=512
RABBITMQ_MEMORY_LIMIT=512
JAEGER_MEMORY_LIMIT=512
PROMETHEUS_MEMORY_LIMIT=512
GRAFANA_MEMORY_LIMIT=256
TRAEFIK_MEMORY_LIMIT=256

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log levels
LOG_LEVEL=info
NODE_ENV=production

# =============================================================================
# HEALTH CHECK CONFIGURATION
# =============================================================================

# Health check intervals (in seconds)
HEALTH_CHECK_INTERVAL=5
HEALTH_CHECK_TIMEOUT=10
HEALTH_CHECK_RETRIES=3
HEALTH_CHECK_START_PERIOD=30

# =============================================================================
# DEPLOYMENT NOTES
# =============================================================================

# 1. Update all passwords and secrets before deployment
# 2. Configure external PostgreSQL, Redis, MongoDB, and MinIO
# 3. Update domain names in service hosts
# 4. Set up SSL certificates for production
# 5. Configure backup strategies for external services
# 6. Set up monitoring and alerting
# 7. Test all service connections before deployment

# =============================================================================
# EXTERNAL SERVICE CONNECTION STRINGS
# =============================================================================

# PostgreSQL Connection Strings (for reference)
# audit_db: postgresql://audit_user:audit_pass@localhost:5432/audit_db
# auth_db: postgresql://auth_user:auth_pass@localhost:5432/auth_db
# students_db: postgresql://students_user:students_pass@localhost:5432/students_db
# university_db: postgresql://university_user:university_pass@localhost:5432/university_db

# Redis Connection
# redis://:your_redis_password@localhost:6379

# MongoDB Connection
# ****************************************************************************************

# RabbitMQ Connection (Containerized)
# amqp://rabbitmq_user:rabbitmq_pass@rabbitmq:5672 