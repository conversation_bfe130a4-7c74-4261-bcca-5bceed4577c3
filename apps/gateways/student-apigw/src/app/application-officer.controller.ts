import {
  Controller,
  Post,
  Body,
  HttpException,
  HttpStatus,
  Logger
} from '@nestjs/common';
import { StudentClientService } from './student.service';
import { Public, CurrentUser } from '@apply-goal-backend/auth';
import { CreateApplicationOfficerStudentDto } from '../../../../services/students-service/src/app/student/dto/application-officer-student.dto';

@Controller('application-officer')
export class ApplicationOfficerController {
  private readonly logger = new Logger(ApplicationOfficerController.name);

  constructor(private readonly studentService: StudentClientService) {}

  @Post('student')
  @Public()
  async createApplicationOfficerStudent(
    @Body() studentData: CreateApplicationOfficerStudentDto,
  ) {
    try {      
      const result = await this.studentService.createApplicationOfficerStudent(studentData);
      return {
        success: true,
        message: 'Student created successfully',
        data: result
      };
    } catch (error) {
      this.logger.error('Failed to create application officer student:', error);
      throw new HttpException(
        error.message || 'Failed to create application officer student',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}