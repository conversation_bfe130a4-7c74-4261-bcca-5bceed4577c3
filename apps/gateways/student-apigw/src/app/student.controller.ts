import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  Headers,
  // UseGuards,
  HttpException,
  HttpStatus,
  Logger,
  <PERSON><PERSON>
} from '@nestjs/common';
import { Response } from 'express';
import { StudentClientService } from './student.service';
// import {
//   CurrentUser,
//   Permissions,
//   JwtAuthGuard,
//   PermissionsGuard,
//   Public
// } from '@apply-goal-backend/auth';
import { UserPermissions } from '@apply-goal-backend/common';
import {
  CourseFilterDto,
  CourseFilterResponseDto
} from './dto/course-filter.dto';

// Create a simple IP decorator
import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { Public } from '@apply-goal-backend/auth';

const Ip = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): string => {
    const request = ctx.switchToHttp().getRequest();
    return (
      request.ip ||
      request.connection.remoteAddress ||
      request.socket.remoteAddress ||
      (request.connection.socket
        ? request.connection.socket.remoteAddress
        : null) ||
      request.headers['x-forwarded-for'] ||
      request.headers['x-real-ip'] ||
      'unknown'
    );
  }
);

// Define permission constants (these should match your auth service)
const StudentPermissions = {
  STUDENT_PROFILE_VIEW: 'StudentProfileManagement:View',
  STUDENT_PROFILE_EDIT: 'StudentProfileManagement:Edit',
  STUDENT_PROFILE_CREATE: 'StudentProfileManagement:Create',
  STUDENT_PROFILE_DELETE: 'StudentProfileManagement:Delete',
  STUDENT_ACADEMIC_VIEW: 'StudentAcademic:View',
  STUDENT_ACADEMIC_EDIT: 'StudentAcademic:Edit',
  STUDENT_ENROLLMENT_MANAGE: 'StudentEnrollment:Manage'
} as const;

// Define interfaces for the new API
interface StudentPersonalInfoRequest {
  studentId: string;
  organizationId?: number;
  agencyId?: string | null;
  firstName: string;
  lastName: string;
  nameInNative: string;
  middleName?: string;
  email: string;
  phone: string;
  guardianPhone: string;
  dateOfBirth: string;
  countryOfBirth?: string;
  cityOfBirth?: string;
  gender: string;
  fatherName: string;
  motherName: string;
  nid: string;
  passport: string;
  ethnicSurvey?: string;
  englishProficiency?: string;
  sevisId?: string;
  previousStudentId?: string;
  referenceSource?: string;
  identity?: Record<string, any>;
  dependentsInfo?: Record<string, any>;
  presentAddress: {
    address: string;
    country: string;
    state: string;
    city: string;
    postalCode: string;
  };
  permanentAddress: {
    address: string;
    country: string;
    state: string;
    city: string;
    postalCode: string;
  };
  maritalStatus: {
    status: string;
    spouseName: string;
    spousePhone: string;
    spousePassport: string;
  };
  sponsor: {
    name: string;
    relation: string;
    phone: string;
    nid: string;
  };
  emergencyContact: {
    lastName: string;
    middleName: string;
    firstName: string;
    phoneHome: string;
    phoneMobile: string;
    relation: string;
  };
  preferredSubject: string[];
  preferredCountry: string[];
  socialLinks: Array<{
    platform: string;
    url: string;
  }>;
  reference: string;
  note: string;
}

interface StudentAcademicRequest {
  academic: Array<{
    foreignDegree: boolean;
    nameOfExam: string;
    institute: string;
    subject: string;
    board: string;
    grade: string;
    passingYear: string;
  }>;
  proficiency: Array<{
    nameOfExam: string;
    score: {
      overall: number;
      R: number;
      L: number;
      W: number;
      S: number;
    };
    examDate: string;
    expiryDate: string;
    note: string;
  }>;
  publications: Array<{
    subject: string;
    journal: string;
    publicationDate: string;
    link: string;
  }>;
  otherActivities: Array<{
    subject: string;
    certificationLink: string;
    startDate: string;
    endDate: string;
  }>;
}

@Controller('student')
export class StudentController {
  private readonly logger = new Logger(StudentController.name);

  constructor(private readonly studentService: StudentClientService) {}

  // Get all course data (courses, intakes, tuition fees, program levels, fields of study)
  @Public()
  @Get('test-course-data')
  async getAllCourseData(@Query('universityId') universityId: string) {
    this.logger.log('=== TEST-COURSE-DATA ENDPOINT CALLED ===');
    this.logger.log(`Query params: ${JSON.stringify({ universityId })}`);

    try {
      if (!universityId) {
        throw new HttpException(
          'universityId is required',
          HttpStatus.BAD_REQUEST
        );
      }

      const universityIdNum = parseInt(universityId, 10);
      if (isNaN(universityIdNum)) {
        throw new HttpException(
          'universityId must be a valid number',
          HttpStatus.BAD_REQUEST
        );
      }

      this.logger.log(
        `Getting all course data for university ${universityIdNum}`
      );

      const result = await this.studentService.getAllCourseData(
        universityIdNum
      );

      return result;
    } catch (error) {
      this.logger.error('Failed to get all course data:', error);
      throw new HttpException(
        error.message || 'Failed to get course data',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  private convertIdToNumber(id: any): number | bigint {
    if (id === null || id === undefined) {
      throw new Error('ID cannot be null or undefined');
    }

    if (typeof id === 'object' && 'low' in id) {
      // This is a gRPC Long object
      return BigInt(id.low);
    } else if (typeof id === 'string') {
      const parsed = parseInt(id, 10);
      if (isNaN(parsed)) {
        throw new Error(`Invalid ID string: ${id}`);
      }
      return parsed;
    } else if (typeof id === 'number') {
      return id;
    } else if (typeof id === 'bigint') {
      return id;
    }

    throw new Error(`Invalid ID type: ${typeof id}`);
  }

  // Generate ADM document endpoint
  @Post(':studentId/adm-document')
  @Public()
  async generateAdmDocument(
    @Param('studentId') studentId: string,
    @Body() body: { applicationId: number; templateType?: 'pdf' | 'html' }
  ) {
    try {
      this.logger.log(
        `Generating ADM document for student ${studentId} and application ${body.applicationId}`
      );
      const templateType = body.templateType || 'html';

      const result = await this.studentService.generateAdmDocument(
        studentId,
        body.applicationId,
        templateType
      );

      return {
        success: true,
        message: 'ADM document generated successfully',
        data: result
      };
      // return result;
    } catch (error) {
      this.logger.error('Failed to generate ADM document:', error);
      throw new HttpException(
        error.message || 'Failed to generate ADM document',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // Generate Admission Portfolio endpoint
  @Post(':studentId/admission-portfolio')
  @Public()
  async generateAdmissionPortfolio(
    @Param('studentId') studentId: string,
    @Body() body: { applicationId: number; templateType?: 'html' | 'pdf' }
  ) {
    try {
      this.logger.log(
        `Generating admission portfolio for student ${studentId} and application ${body.applicationId}`
      );
      const templateType = body.templateType || 'html';

      const result = await this.studentService.generateAdmissionPortfolio(
        studentId,
        body.applicationId,
        templateType
      );

      return {
        success: true,
        message: 'Admission portfolio generated successfully',
        data: result
      };
      // return result;
    } catch (error) {
      this.logger.error('Failed to generate admission portfolio:', error);
      throw new HttpException(
        error.message || 'Failed to generate admission portfolio',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // Generate CAL document endpoint
  @Post('applications/:applicationId/cal-document')
  @Public()
  async generateCalDocument(
    @Param('applicationId') applicationId: string,
    @Body() body: { templateType?: 'pdf' | 'html' }
  ) {
    try {
      this.logger.log(
        `Generating CAL document for application ${applicationId}`
      );
      const templateType = body.templateType || 'html';

      const result = await this.studentService.generateCalDocument(
        applicationId,
        { templateType }
      );

      return {
        success: true,
        message: 'CAL document generated successfully',
        data: result
      };
      // return result;
    } catch (error) {
      this.logger.error('Failed to generate CAL document:', error);
      throw new HttpException(
        error.message || 'Failed to generate CAL document',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // --------------- student info -------------------
  // New Student Personal Info API
  @Post()
  // @Permissions(UserPermissions.SPM_CREATE)
  @Public()
  async createStudentPersonalInfo(
    // @CurrentUser('id') userId: number,
    // @CurrentUser('organizationId') userOrganizationId: string,
    // @CurrentUser('roles') roles: string[],
    // @Ip() ipAddress: string,
    // @Headers('user-agent') userAgent: string,
    @Body() studentData: StudentPersonalInfoRequest
  ) {
    try {
      const userId = 1;
      const userOrganizationId = '1';
      const roles = ['Admin'];
      this.logger.log(
        `User ${userId} creating/updating student with studentId: ${studentData.studentId}`
      );

      // Preprocess JSON fields to ensure they're in the correct format
      if (studentData.identity && typeof studentData.identity === 'object') {
        // Convert object to JSON string for gRPC transmission
        (studentData as any).identity = JSON.stringify(studentData.identity);
        this.logger.log('API Gateway - Converted identity to JSON string');
      }
      // Remove dependentsInfo conversion - it should remain as object for protobuf
      // if (studentData.dependentsInfo && typeof studentData.dependentsInfo === 'object') {
      //   // Convert object to JSON string for gRPC transmission
      //   (studentData as any).dependentsInfo = JSON.stringify(studentData.dependentsInfo);
      //   this.logger.log('API Gateway - Converted dependentsInfo to JSON string');
      // }

      // Check if student exists by studentId
      let existingStudent = null;
      try {
        existingStudent = await this.studentService.getStudent(
          studentData.studentId
        );
        this.logger.log(`Student ${studentData.studentId} found, will update`);
      } catch (error) {
        // Student doesn't exist, will create new one
        this.logger.log(
          `Student ${studentData.studentId} not found, will create new student`
        );
      }

      let result;
      if (
        existingStudent &&
        (existingStudent.data || existingStudent.student)
      ) {
        // Student exists, update it
        this.logger.log(`Student ${studentData.studentId} exists, updating...`);
        result = await this.studentService.updateStudentPersonalInfo(
          studentData.studentId,
          studentData,
          userOrganizationId
        );
      } else {
        // Student doesn't exist, create new one
        this.logger.log(
          `Student ${studentData.studentId} doesn't exist, creating new student...`
        );
        result = await this.studentService.createStudentPersonalInfo(
          studentData
        );
      }

      // Return the full student data
      return {
        success: true,
        message: existingStudent
          ? 'Student updated successfully'
          : 'Student created successfully',
        data: result.data || result
      };
    } catch (error) {
      this.logger.error(
        'Failed to create/update student personal info:',
        error
      );
      throw new HttpException(
        error.message || 'Failed to create/update student personal info',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // Public Student Registration API (for self-registration)
  @Post('register')
  @Public()
  async registerStudent(
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string,
    @Body() studentData: StudentPersonalInfoRequest
  ) {
    try {
      // For public registration, we'll use a default organization or create one
      const defaultOrganizationId = 'default-org';
      return await this.studentService.createStudentPersonalInfo(studentData);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to register student',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // Legacy Student CRUD Operations (keeping for backward compatibility)
  @Post('legacy')
  // @Permissions(UserPermissions.SPM_CREATE)
  @Public()
  async createStudent(
    // @CurrentUser('id') userId: number,
    // @CurrentUser('roles') roles: string[],
    // @Ip() ipAddress: string,
    // @Headers('user-agent') userAgent: string,
    @Body()
    createStudentData: {
      first_name: string;
      last_name: string;
      email: string;
      phone: string;
      date_of_birth: {
        year: number;
        month: number;
        day: number;
      };
      major: string;
      minor?: string;
      address: {
        street: string;
        city: string;
        state: string;
        postal_code: string;
        country: string;
      };
      emergency_contacts?: Array<{
        name: string;
        relationship: string;
        phone: string;
        email: string;
      }>;
      metadata?: Record<string, string>;
    }
  ) {
    try {
      return await this.studentService.createStudent(createStudentData);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to create student',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // Update a student's application type on the student model
  @Put(':studentId/application-type')
  @Public() // TODO: protect with appropriate guard/permission when ready
  async updateStudentApplicationType(
    @Param('studentId') studentId: string,
    @Body() body: { applicationType?: string }
  ) {
    try {
      if (!body?.applicationType || typeof body.applicationType !== 'string') {
        throw new HttpException(
          'applicationType is required',
          HttpStatus.BAD_REQUEST
        );
      }
      const result = await this.studentService.updateStudentApplicationType(
        studentId,
        body.applicationType
      );
      return {
        status: 'success',
        data: result?.data ?? result,
        message:
          result?.message ?? 'Student application type updated successfully'
      };
    } catch (error) {
      this.logger.error('Failed to update student application type:', error);
      throw new HttpException(
        error.message || 'Failed to update student application type',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Delete(':id')
  // @Permissions(UserPermissions.SPM_DELETE)
  @Public()
  async deleteStudent(
    @Param('id') id: string
    // @CurrentUser('id') userId: number,
    // @CurrentUser('roles') roles: string[],
    // @Ip() ipAddress: string,
    // @Headers('user-agent') userAgent: string
  ) {
    try {
      return await this.studentService.deleteStudent(id);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to delete student',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get()
  // @Permissions(UserPermissions.SPM_VIEW)
  @Public()
  async listStudents(
    // @CurrentUser('id') userId: number,
    // @CurrentUser('organizationId') userOrganizationId: string,
    // @CurrentUser('roles') roles: string[],
    // @Ip() ipAddress: string,
    // @Headers('user-agent') userAgent: string,
    @Query('page_size') pageSize?: number,
    @Query('page_token') pageToken?: string,
    @Query('filter') filter?: string,
    @Query('order_by') orderBy?: string
  ) {
    try {
      const userOrganizationId = '1';
      const filters = {
        organizationId: userOrganizationId,
        page_size: pageSize || 10,
        page_token: pageToken || '',
        filter: filter || '',
        order_by: orderBy || 'created_at desc'
      };
      return await this.studentService.listStudents(filters);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to list students',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
  // #endregion

  // Academic Operations
  @Post(':studentId/enrollments')
  // @Permissions(UserPermissions.SPM_EDIT)
  @Public()
  async enrollInCourse(
    @Param('studentId') studentId: string,
    // @CurrentUser('id') userId: number,
    // @CurrentUser('roles') roles: string[],
    // @Ip() ipAddress: string,
    // @Headers('user-agent') userAgent: string,
    @Body()
    enrollmentData: {
      course_id: string;
      semester: string;
    }
  ) {
    try {
      return await this.studentService.enrollInCourse(
        studentId,
        enrollmentData.course_id,
        enrollmentData.semester
      );
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to enroll in course',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Delete(':studentId/enrollments/:courseId')
  // @Permissions(UserPermissions.SPM_EDIT)
  @Public()
  async dropCourse(
    @Param('studentId') studentId: string,
    @Param('courseId') courseId: string,
    // @CurrentUser('id') userId: number,
    // @CurrentUser('roles') roles: string[],
    // @Ip() ipAddress: string,
    // @Headers('user-agent') userAgent: string,
    @Query('semester') semester: string
  ) {
    try {
      return await this.studentService.dropCourse(
        studentId,
        courseId,
        semester
      );
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to drop course',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get(':studentId/enrollments')
  // @Permissions(UserPermissions.SPM_VIEW)
  @Public()
  async getEnrollments(
    @Param('studentId') studentId: string,
    // @CurrentUser('id') userId: number,
    // @CurrentUser('roles') roles: string[],
    // @Ip() ipAddress: string,
    // @Headers('user-agent') userAgent: string,
    @Query('semester') semester?: string
  ) {
    try {
      return await this.studentService.getEnrollments(studentId, semester);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to get enrollments',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Put(':studentId/grades')
  // @Permissions(UserPermissions.SPM_EDIT)
  @Public()
  async updateGrades(
    @Param('studentId') studentId: string,
    // @CurrentUser('id') userId: number,
    // @CurrentUser('roles') roles: string[],
    // @Ip() ipAddress: string,
    // @Headers('user-agent') userAgent: string,
    @Body()
    gradeData: {
      course_id: string;
      grade: string;
    }
  ) {
    try {
      return await this.studentService.updateGrades(
        studentId,
        gradeData.course_id,
        gradeData.grade
      );
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to update grades',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get(':studentId/academic-progress')
  // @Permissions(UserPermissions.SPM_VIEW)
  @Public()
  async getAcademicProgress(
    @Param('studentId') studentId: string
    // @CurrentUser('id') userId: number,
    // @CurrentUser('roles') roles: string[],
    // @Ip() ipAddress: string,
    // @Headers('user-agent') userAgent: string
  ) {
    try {
      return await this.studentService.getAcademicProgress(studentId);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to get academic progress',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get(':studentId/transcript')
  // @Permissions(UserPermissions.SPM_VIEW)
  @Public()
  async getTranscript(
    @Param('studentId') studentId: string
    // @CurrentUser('id') userId: number,
    // @CurrentUser('roles') roles: string[],
    // @Ip() ipAddress: string,
    // @Headers('user-agent') userAgent: string
  ) {
    try {
      return await this.studentService.getTranscript(studentId);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to get transcript',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // Student Academic Information API
  @Post(':id/academic')
  // @Permissions(UserPermissions.SPM_EDIT)
  @Public()
  async createOrUpdateStudentAcademic(
    @Param('id') studentId: string,
    // @CurrentUser('id') userId: number,
    // @CurrentUser('organizationId') organizationId: string,
    // @CurrentUser('roles') roles: string[],
    // @Ip() ipAddress: string,
    // @Headers('user-agent') userAgent: string,
    @Body() academicData: StudentAcademicRequest
  ) {
    try {
      this.logger.log(
        'API Gateway received academic data:',
        JSON.stringify(academicData, null, 2)
      );
      const result = await this.studentService.createOrUpdateStudentAcademic(
        studentId,
        academicData
      );
      this.logger.log('API Gateway result:', JSON.stringify(result, null, 2));
      return {
        success: true,
        message: 'Student academic information saved successfully',
        data: result
      };
    } catch (error) {
      this.logger.error('API Gateway error:', error);
      throw new HttpException(
        error.message || 'Failed to create/update student academic info',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get(':id/academic')
  // @Permissions(UserPermissions.SPM_VIEW)
  @Public()
  async getStudentAcademic(
    @Param('id') studentId: string
    // @CurrentUser('id') userId: number,
    // @CurrentUser('roles') roles: string[],
    // @Ip() ipAddress: string,
    // @Headers('user-agent') userAgent: string
  ) {
    try {
      const result = await this.studentService.getStudentAcademic(studentId);
      return {
        success: true,
        message: 'Student academic information retrieved successfully',
        ...result
      };
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to get student academic info',
        error.status || HttpStatus.NOT_FOUND
      );
    }
  }

  // University Integration Endpoints
  @Get('universities/application')
  @Public()
  // @Permissions(UserPermissions.SPM_VIEW)  // Temporarily commented for testing
  async universityListForStudentApplication() { // @Headers('user-agent') userAgent: string // @Ip() ipAddress: string, // @CurrentUser('roles') roles: string[], // @CurrentUser('id') userId: number
    try {
      this.logger.log(
        `User ${1} requested university list for student application from }`
      );

      const result =
        await this.studentService.universityListForStudentApplication();
      console.log('universityListForApplications', result);

      return {
        status: 'success',
        data: result,
        message: result.message
      };
    } catch (error) {
      this.logger.error(
        'Failed to get university list for student application:',
        error
      );
      throw new HttpException(
        error.message ||
          'Failed to get university list for student application',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('universities/:universityId')
  @Public()
  // @Permissions(UserPermissions.SPM_VIEW)  // Temporarily commented for testing
  async getUniversity(
    @Param('universityId') universityId: string
    // @CurrentUser('id') userId: number,
    // @CurrentUser('roles') roles: string[],
    // @Ip() ipAddress: string,
    // @Headers('user-agent') userAgent: string
  ) {
    try {
      return await this.studentService.getUniversity(universityId);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to get university',
        error.status || HttpStatus.NOT_FOUND
      );
    }
  }

  @Get('universities/:universityId/program-levels')
  @Public()
  async getProgramLevels(@Param('universityId') universityId: string) {
    try {
      this.logger.log(
        `User requested program levels for university: ${universityId}`
      );

      const result = await this.studentService.getProgramLevelsByUniversity(
        universityId
      );

      return {
        success: true,
        data: result.data || [],
        message: 'Program levels retrieved successfully'
      };
    } catch (error) {
      this.logger.error(
        `Failed to get program levels: ${error.message}`,
        error.stack
      );
      throw new HttpException(
        'Failed to retrieve program levels',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('universities/:universityId/fields-of-study')
  @Public()
  async getFieldsOfStudy(@Param('universityId') universityId: string) {
    try {
      this.logger.log(
        `User requested fields of study for university: ${universityId}`
      );

      const result = await this.studentService.getFieldsOfStudyByUniversity(
        universityId
      );

      return {
        success: true,
        data: result.data || [],
        message: 'Fields of study retrieved successfully'
      };
    } catch (error) {
      this.logger.error(
        `Failed to get fields of study: ${error.message}`,
        error.stack
      );
      throw new HttpException(
        'Failed to retrieve fields of study',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('universities/:universityId/courses')
  @Public()
  async getCourses(
    @Param('universityId') universityId: string,
    @Query() filters: any
  ) {
    try {
      this.logger.log(`User requested courses for university: ${universityId}`);

      const result = await this.studentService.listCoursesByUniversity(
        universityId,
        filters
      );

      return {
        success: true,
        data: result.data || [],
        message: 'Courses retrieved successfully'
      };
    } catch (error) {
      this.logger.error(`Failed to get courses: ${error.message}`, error.stack);
      throw new HttpException(
        'Failed to retrieve courses',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('universities/:universityId/intakes')
  @Public()
  async getIntakes(@Param('universityId') universityId: string) {
    try {
      this.logger.log(`User requested intakes for university: ${universityId}`);

      const result = await this.studentService.getIntakesByUniversity(
        universityId
      );

      return {
        success: true,
        data: result.data || [],
        message: 'Intakes retrieved successfully'
      };
    } catch (error) {
      this.logger.error(`Failed to get intakes: ${error.message}`, error.stack);
      throw new HttpException(
        'Failed to retrieve intakes',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('universities/:universityId/tuition-fees')
  @Public()
  async getTuitionFees(@Param('universityId') universityId: string) {
    try {
      this.logger.log(
        `User requested tuition fees for university: ${universityId}`
      );

      const result = await this.studentService.getFeesByUniversity(
        universityId
      );

      return {
        success: true,
        data: result.data || [],
        message: 'Tuition fees retrieved successfully'
      };
    } catch (error) {
      this.logger.error(
        `Failed to get tuition fees: ${error.message}`,
        error.stack
      );
      throw new HttpException(
        'Failed to retrieve tuition fees',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('universities/:universityId/comprehensive-data')
  @Public()
  async getComprehensiveUniversityData(
    @Param('universityId') universityId: string
  ) {
    try {
      this.logger.log(
        `User requested comprehensive data for university: ${universityId}`
      );

      const result = await this.studentService.getComprehensiveUniversityData(
        universityId
      );

      return {
        success: true,
        data: result,
        message: 'Comprehensive university data retrieved successfully'
      };
    } catch (error) {
      this.logger.error(
        `Failed to get comprehensive data: ${error.message}`,
        error.stack
      );
      throw new HttpException(
        'Failed to retrieve comprehensive university data',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Get list of universities with comprehensive filtering options
   *
   * Query Parameters:
   * - page: Page number for pagination (default: 1)
   * - limit: Number of universities per page (default: 10)
   * - isActive: Filter by active status ('true' or 'false')
   * - search: Search universities by name
   * - type: Filter by university type
   * - country: Filter by country
   * - state: Filter by state/province
   * - city: Filter by city
   * - sortBy: Sort field (default: 'createdAt')
   * - sortOrder: Sort order ('asc' or 'desc', default: 'desc')
   *
   * Example: GET /api/students/universities?page=1&limit=20&search=MIT&country=USA&type=public&sortBy=name&sortOrder=asc
   */
  @Get('universities')
  @Public()
  // @Permissions(UserPermissions.SPM_VIEW)  // Temporarily commented for testing
  async listUniversities(
    // @CurrentUser('id') userId: number,
    // @CurrentUser('roles') roles: string[],
    // @Ip() ipAddress: string,
    // @Headers('user-agent') userAgent: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('isActive') isActive?: string,
    @Query('search') search?: string,
    @Query('type') type?: string,
    @Query('country') country?: string,
    @Query('state') state?: string,
    @Query('city') city?: string,
    @Query('sortBy') sortBy?: string,
    @Query('sortOrder') sortOrder?: 'asc' | 'desc'
  ) {
    try {
      this.logger.log(`User ${1} requested university list from ${'unknown'}`);

      const filters = {
        page: page || 1,
        limit: limit || 10,
        isActive,
        search,
        type,
        country,
        state,
        city,
        sortBy,
        sortOrder: sortOrder || 'desc'
      };

      const result = await this.studentService.listUniversities(filters);

      return {
        status: 'success',
        data: result.data,
        message: result.message
      };
    } catch (error) {
      this.logger.error('Failed to list universities:', error);
      throw new HttpException(
        error.message || 'Failed to list universities',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // Agency Integration Endpoints
  @Get('agencies/:agencyId')
  // @Permissions(UserPermissions.SPM_VIEW)
  @Public()
  async getAgency(
    @Param('agencyId') agencyId: string
    // @CurrentUser('id') userId: number,
    // @CurrentUser('roles') roles: string[],
    // @Ip() ipAddress: string,
    // @Headers('user-agent') userAgent: string
  ) {
    try {
      return await this.studentService.getAgency(agencyId);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to get agency',
        error.status || HttpStatus.NOT_FOUND
      );
    }
  }

  @Get('agencies')
  // @Permissions(UserPermissions.SPM_VIEW)
  @Public()
  async listAgencies(
    // @CurrentUser('id') userId: number,
    // @CurrentUser('roles') roles: string[],
    // @Ip() ipAddress: string,
    // @Headers('user-agent') userAgent: string,
    @Query('page_size') pageSize?: number,
    @Query('page_token') pageToken?: string,
    @Query('filter') filter?: string
  ) {
    try {
      const filters = {
        page_size: pageSize || 10,
        page_token: pageToken || '',
        filter: filter || ''
      };
      return await this.studentService.listAgencies(filters);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to list agencies',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // Application Endpoints
  @Post('applications')
  @Public() // Temporarily public for testing
  // @Permissions(UserPermissions.SPM_VIEW)  // Temporarily commented for testing
  async createApplication(
    // @CurrentUser('id') userId: number,
    // @CurrentUser('roles') roles: string[],
    // @Ip() ipAddress: string,
    // @Headers('user-agent') userAgent: string,
    @Body()
    applications: {
      studentId: string;
      universityId: number;
      universityCountryId: number;
      universityCountryCampus: number;
      programId: number;
      intakeId: number;
      courseId: number;
      note?: string;
      status?: string;
      paymentStatus?: string;
      applicationType?: string;
      // Enhanced fields
      applicationId?: string;
      currentStage?: string;
      overallProgress?: number;
      totalAmount?: number;
      paidAmount?: number;
      refundAmount?: number;
      deliveryMethod?: string;
      submissionDate?: string;
      deadlineDate?: string;
    }
  ) {
    try {
      this.logger.log(
        `User ${1} creating application for student ${
          applications[0].studentId
        } from ${'unknown'}`
      );
      const grpcPayload = {
        applications: applications
      };

      const result = await this.studentService.createApplication(grpcPayload);

      return {
        status: 'success',
        data: result.data,
        message: result.message
      };
    } catch (error) {
      this.logger.error('Failed to create application:', error);
      throw new HttpException(
        error.message || 'Failed to create application',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('applications/student')
  @Public()
  // @Permissions(UserPermissions.SPM_VIEW)  // Temporarily commented for testing
  async getApplicationsByStudent() // @Headers('user-agent') userAgent: string // @Ip() ipAddress: string, // @CurrentUser('roles') roles: string[], // @CurrentUser('id') userId: number, // @Param('studentId') studentId: string,
  {
    try {
      this.logger.log(
        `User ${1} requested applications for student ${'unknown'} from ${'unknown'}`
      );

      // Get applications and university data
      const [applicationsResult, universityData] = await Promise.all([
        this.studentService.getApplicationsByStudent('unknown'),
        this.studentService.universityListForStudentApplication()
      ]);

      // Extract university data for mapping
      const universities = universityData?.data?.universities || [];

      // Create a mapping function to find names by IDs
      const findNameById = (
        universityId: number,
        countryId: number,
        campusId: number,
        programId: number,
        intakeId: number,
        courseId: number
      ) => {
        const university = universities.find((u) => u.id === universityId);
        if (!university)
          return {
            universityName: '',
            countryName: '',
            campusName: '',
            programLevelName: '',
            intakeName: '',
            courseName: ''
          };

        const country = university.countries?.find((c) => c.id === countryId);
        if (!country)
          return {
            universityName: university.title,
            countryName: '',
            campusName: '',
            programLevelName: '',
            intakeName: '',
            courseName: ''
          };

        const campus = country.campuses?.find((c) => c.id === campusId);
        if (!campus)
          return {
            universityName: university.title,
            countryName: country.title,
            campusName: '',
            programLevelName: '',
            intakeName: '',
            courseName: ''
          };

        const programLevel = campus.programLevels?.find(
          (p) => p.id === programId
        );
        if (!programLevel)
          return {
            universityName: university.title,
            countryName: country.title,
            campusName: campus.title,
            programLevelName: '',
            intakeName: '',
            courseName: ''
          };

        const intake = programLevel.intakes?.find((i) => i.id === intakeId);
        if (!intake)
          return {
            universityName: university.title,
            countryName: country.title,
            campusName: campus.title,
            programLevelName: programLevel.title,
            intakeName: '',
            courseName: ''
          };

        const course = intake.courses?.find((c) => c.id === courseId);
        if (!course)
          return {
            universityName: university.title,
            countryName: country.title,
            campusName: campus.title,
            programLevelName: programLevel.title,
            intakeName: intake.title,
            courseName: ''
          };

        return {
          universityName: university.title,
          countryName: country.title,
          campusName: campus.title,
          programLevelName: programLevel.title,
          intakeName: intake.title,
          courseName: course.title
        };
      };

      // Map applications with names
      const mappedApplications = applicationsResult.data.applications.map(
        (app) => {
          const names = findNameById(
            app.universityId,
            app.universityCountryId,
            app.universityCountryCampus,
            app.programId,
            app.intakeId,
            app.courseId
          );

          return {
            ...app,
            // Add mapped names
            universityName: names.universityName,
            countryName: names.countryName,
            campusName: names.campusName,
            programLevelName: names.programLevelName,
            intakeName: names.intakeName,
            courseName: names.courseName
          };
        }
      );

      return {
        status: 'success',
        data: {
          ...applicationsResult.data,
          applications: mappedApplications
        },
        message: applicationsResult.message
      };
    } catch (error) {
      this.logger.error('Failed to get applications by student:', error);
      throw new HttpException(
        error.message || 'Failed to get applications by student',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('applications/officer/:officerId')
  @Public()
  async getApplicationsByOfficer(
    @Param('officerId') officerId: string
  ) {
    try {
      const numericOfficerId = parseInt(officerId, 10);
      if (isNaN(numericOfficerId)) {
        throw new HttpException(
          'Invalid officer ID',
          HttpStatus.BAD_REQUEST
        );
      }

      this.logger.log(
        `Getting applications for officer ${numericOfficerId}`
      );

      const result = await this.studentService.getApplicationsByOfficer(numericOfficerId);

      return {
        status: 'success',
        data: result.data,
        message: result.message
      };
    } catch (error) {
      this.logger.error('Failed to get applications by officer:', error);
      throw new HttpException(
        error.message || 'Failed to get applications by officer',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('applications/:id')
  // @Public() // Temporarily public for testing
  // @Permissions(UserPermissions.SPM_VIEW)  // Temporarily commented for testing
  async getApplication(
    @Param('id') id: string
    // @CurrentUser('id') userId: number,
    // @CurrentUser('roles') roles: string[],
    // @Ip() ipAddress: string,
    // @Headers('user-agent') userAgent: string
  ) {
    try {
      this.logger.log(
        `User ${1} requested application ${id} from ${'unknown'}`
      );

      // Get application and university data
      const [applicationResult, universityData] = await Promise.all([
        this.studentService.getApplication(id),
        this.studentService.universityListForStudentApplication()
      ]);

      // Extract university data for mapping
      const universities = universityData?.data?.universities || [];

      // Create a mapping function to find names by IDs
      const findNameById = (
        universityId: number,
        countryId: number,
        campusId: number,
        programId: number,
        intakeId: number,
        courseId: number
      ) => {
        const university = universities.find((u) => u.id === universityId);
        if (!university)
          return {
            universityName: '',
            countryName: '',
            campusName: '',
            programLevelName: '',
            intakeName: '',
            courseName: ''
          };

        const country = university.countries?.find((c) => c.id === countryId);
        if (!country)
          return {
            universityName: university.title,
            countryName: '',
            campusName: '',
            programLevelName: '',
            intakeName: '',
            courseName: ''
          };

        const campus = country.campuses?.find((c) => c.id === campusId);
        if (!campus)
          return {
            universityName: university.title,
            countryName: country.title,
            campusName: '',
            programLevelName: '',
            intakeName: '',
            courseName: ''
          };

        const programLevel = campus.programLevels?.find(
          (p) => p.id === programId
        );
        if (!programLevel)
          return {
            universityName: university.title,
            countryName: country.title,
            campusName: campus.title,
            programLevelName: '',
            intakeName: '',
            courseName: ''
          };

        const intake = programLevel.intakes?.find((i) => i.id === intakeId);
        if (!intake)
          return {
            universityName: university.title,
            countryName: country.title,
            campusName: campus.title,
            programLevelName: programLevel.title,
            intakeName: '',
            courseName: ''
          };

        const course = intake.courses?.find((c) => c.id === courseId);
        if (!course)
          return {
            universityName: university.title,
            countryName: country.title,
            campusName: campus.title,
            programLevelName: programLevel.title,
            intakeName: intake.title,
            courseName: ''
          };

        return {
          universityName: university.title,
          countryName: country.title,
          campusName: campus.title,
          programLevelName: programLevel.title,
          intakeName: intake.title,
          courseName: course.title
        };
      };

      // Map application with names
      const application = applicationResult.data[0]; // Get first application from array
      if (application) {
        const names = findNameById(
          application.universityId,
          application.universityCountryId,
          application.universityCountryCampus,
          application.programId,
          application.intakeId,
          application.courseId
        );

        const mappedApplication = {
          ...application,
          // Add mapped names
          universityName: names.universityName,
          countryName: names.countryName,
          campusName: names.campusName,
          programLevelName: names.programLevelName,
          intakeName: names.intakeName,
          courseName: names.courseName
        };

        return {
          status: 'success',
          data: [mappedApplication],
          message: 'Application retrieved successfully'
        };
      }

      return {
        status: 'success',
        data: applicationResult.data,
        message: applicationResult.message
      };
    } catch (error) {
      this.logger.error('Failed to get application:', error);
      throw new HttpException(
        error.message || 'Failed to get application',
        error.status || HttpStatus.NOT_FOUND
      );
    }
  }

  @Get('applications')
  @Public()
  // @Permissions(UserPermissions.SPM_VIEW)  // Temporarily commented for testing
  async listApplications(
    // @CurrentUser('id') userId: number,
    // @CurrentUser('roles') roles: string[],
    // @Ip() ipAddress: string,
    // @Headers('user-agent') userAgent: string,
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('studentId') studentId?: string,
    @Query('status') status?: string,
    @Query('paymentStatus') paymentStatus?: string,
    @Query('applicationType') applicationType?: string,
    @Query('sortBy') sortBy?: string,
    @Query('sortOrder') sortOrder?: 'asc' | 'desc',
    @Query('universityId') universityId?: string,
    @Query('programId') programId?: string,
    @Query('intakeId') intakeId?: string,
    @Query('courseId') courseId?: string,
    @Query('campusId') campusId?: string
  ) {
    try {
      this.logger.log(
        `User ${1} requested applications list from ${'unknown'}`
      );

      const filters = {
        page: page ? parseInt(page) : 1,
        limit: limit ? parseInt(limit) : 10,
        studentId,
        status,
        paymentStatus,
        applicationType,
        universityId: universityId ? parseInt(universityId, 10) : undefined,
        programId: programId ? parseInt(programId, 10) : undefined,
        intakeId: intakeId ? parseInt(intakeId, 10) : undefined,
        courseId: courseId ? parseInt(courseId, 10) : undefined,
        campusId: campusId ? parseInt(campusId, 10) : undefined,
        sortBy: sortBy || 'createdAt',
        sortOrder: sortOrder || 'desc'
      };

      // Get applications and university data
      const [applicationsResult, universityData] = await Promise.all([
        this.studentService.listApplications(filters),
        this.studentService.universityListForStudentApplication()
      ]);

      // Check if applicationsResult has the expected structure
      if (
        !applicationsResult ||
        !applicationsResult.data ||
        !applicationsResult.data.applications
      ) {
        this.logger.warn('No applications data found in response');
        // ✅ Return in the same format as filterApplications method returns
        return {
          status: 'success',
          data: [], // Empty array like filterApplications returns
          message: 'No applications found'
        };
      }

      console.log('application list=>', applicationsResult.data.applications);
      console.log(
        'UniversityList for Application=>>>>>>>>>>>>>>>',
        universityData
      );
      console.log(
        'University list for application=>',
        JSON.stringify(universityData?.universities)
      );
      const result = await filterApplications(
        applicationsResult.data.applications || [],
        universityData?.universities || [],
        this.studentService
      );

      return {
        status: 'success',
        data: result,
        message: applicationsResult.message
      };
    } catch (error) {
      this.logger.error('Failed to list applications:', error);
      throw new HttpException(
        error.message || 'Failed to list applications',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('dashboard/campus/:campusId')
  @Public()
  async getDashboardDataByCampus(@Param('campusId') campusId: string) {
    try {
      this.logger.log(
        `User ${1} requested dashboard data for campus ${campusId} from ${'unknown'}`
      );

      const numericCampusId = parseInt(campusId, 10);
      if (isNaN(numericCampusId)) {
        throw new HttpException(
          'Invalid campus ID. Must be a valid number.',
          HttpStatus.BAD_REQUEST
        );
      }

      const dashboardData = await this.studentService.getDashboardDataByCampus(
        numericCampusId
      );

      // Ensure campus ID is a number in the response
      const responseData = {
        ...dashboardData.data,
        campusId: numericCampusId
      };

      return {
        status: 'success',
        data: responseData,
        message: dashboardData.message
      };
    } catch (error) {
      this.logger.error('Failed to get dashboard data by campus:', error);
      throw new HttpException(
        error.message || 'Failed to get dashboard data by campus',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Put('applications/:id')
  @Public() // Temporarily public for testing
  // @Permissions(UserPermissions.SPM_VIEW)  // Temporarily commented for testing
  async updateApplication(
    @Param('id') id: string,
    // @CurrentUser('id') userId: number,
    // @CurrentUser('roles') roles: string[],
    // @Ip() ipAddress: string,
    // @Headers('user-agent') userAgent: string,
    @Body()
    applicationData: {
      studentId?: string;
      universityId?: number;
      universityCountryId?: number;
      universityCountryCampus?: number;
      programId?: number;
      intakeId?: number;
      courseId?: number;
      note?: string;
      status?: string;
      paymentStatus?: string;
      applicationType?: string;
      // Enhanced fields
      applicationId?: string;
      currentStage?: string;
      overallProgress?: number;
      totalAmount?: number;
      paidAmount?: number;
      refundAmount?: number;
      deliveryMethod?: string;
      submissionDate?: string;
      deadlineDate?: string;
      // Payment-related fields
      paymentInvoiceUrl?: string;
      paymentMethod?: string;
      // Immigration document fields
      savisId?: number;
      i20Url?: string;
      studentI20Url?: string;
      i94Url?: string;
      i797cUrl?: string;
      calUrl?: string;
      admUrl?: string;
      apUrl?: string;
      // Document status fields
      studentI20UrlStatus?: string;
      i94UrlStatus?: string;
      i797cUrlStatus?: string;
    }
  ) {
    try {
      this.logger.log(`User ${1} updating application ${id} from ${'unknown'}`);

      const result = await this.studentService.updateApplication(
        id,
        applicationData
      );

      return {
        status: 'success',
        data: result.data,
        message: result.message
      };
    } catch (error) {
      this.logger.error('Failed to update application:', error);
      throw new HttpException(
        error.message || 'Failed to update application',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Delete('applications/:id')
  @Public()
  // @Permissions(UserPermissions.SPM_VIEW)  // Temporarily commented for testing
  async deleteApplication(
    @Param('id') id: string
    // @CurrentUser('id') userId: number,
    // @CurrentUser('roles') roles: string[],
    // @Ip() ipAddress: string,
    // @Headers('user-agent') userAgent: string
  ) {
    try {
      this.logger.log(`User ${1} deleting application ${id} from ${'unknown'}`);

      const result = await this.studentService.deleteApplication(id);

      return {
        status: 'success',
        data: result.data,
        message: result.message
      };
    } catch (error) {
      this.logger.error('Failed to delete application:', error);
      throw new HttpException(
        error.message || 'Failed to delete application',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('applications/:id/progress')
  @Public() // Temporarily public for testing
  // @Permissions(UserPermissions.SPM_VIEW)  // Temporarily commented for testing
  async getApplicationProgress(
    @Param('id') id: string
    // @CurrentUser('id') userId: number,
    // @CurrentUser('roles') roles: string[],
    // @Ip() ipAddress: string,
    // @Headers('user-agent') userAgent: string
  ) {
    try {
      this.logger.log(
        `User ${1} requested application progress for ${id} from ${'unknown'}`
      );

      const result = await this.studentService.getApplicationProgress(id);

      return {
        status: 'success',
        data: result.data,
        message: result.message
      };
    } catch (error) {
      this.logger.error('Failed to get application progress:', error);
      throw new HttpException(
        error.message || 'Failed to get application progress',
        error.status || HttpStatus.NOT_FOUND
      );
    }
  }

  // Get students by application officer
  @Get('application-officer/:applicationOfficerId')
  @Public()
  // @Permissions(UserPermissions.SPM_VIEW)  // Temporarily commented for testing
  async getStudentsByApplicationOfficer(
    @Param('applicationOfficerId') applicationOfficerId: string
    // @CurrentUser('id') userId: number,
    // @CurrentUser('roles') roles: string[],
    // @Ip() ipAddress: string,
    // @Headers('user-agent') userAgent: string
  ) {
    try {
      const numericApplicationOfficerId = parseInt(applicationOfficerId, 10);
      if (isNaN(numericApplicationOfficerId)) {
        throw new HttpException(
          'Invalid application officer ID',
          HttpStatus.BAD_REQUEST
        );
      }

      this.logger.log(
        `User ${1} requested students for application officer ${numericApplicationOfficerId} from ${'unknown'}`
      );

      const result = await this.studentService.getStudentsByApplicationOfficer(numericApplicationOfficerId);
      return {
        status: 'success',
        data: result,
        message: 'Students retrieved successfully'
      };
    } catch (error) {
      this.logger.error('Error getting students by application officer:', error);
      throw new HttpException(
        error.message || 'Failed to get students by application officer',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // Student Endpoints (moved after specific routes)
  @Get(':id')
  @Public()
  // @Permissions(UserPermissions.SPM_VIEW)  // Temporarily commented for testing
  async getStudent(
    @Param('id') id: string
    // @CurrentUser('id') userId: number,
    // @CurrentUser('organizationId') userOrganizationId: string,
    // @CurrentUser('roles') roles: string[],
    // @Ip() ipAddress: string,
    // @Headers('user-agent') userAgent: string
  ) {
    try {
      this.logger.log(`User ${1} requested student ${id} from ${'unknown'}`);

      const result = await this.studentService.getStudent(id);

      return {
        status: 'success',
        data: result.student,
        message: 'Student retrieved successfully'
      };
    } catch (error) {
      this.logger.error('Failed to get student:', error);
      throw new HttpException(
        error.message || 'Failed to get student',
        error.status || HttpStatus.NOT_FOUND
      );
    }
  }

  // ==================== APPLICATION DOCUMENT REQUIREMENTS ====================

  @Get('applications/:applicationId/requirements')
  @Public() // Temporarily public for testing
  // @Permissions(StudentPermissions.STUDENT_PROFILE_VIEW)
  async getApplicationRequirements(
    @Param('applicationId') applicationId: string
  ) {
    try {
      this.logger.log(
        `Getting application requirements for application: ${applicationId}`
      );

      const result = await this.studentService.getApplicationRequirements(
        parseInt(applicationId)
      );

      return {
        status: 'success',
        data: result.data ? result.data : [],
        message: result.message
      };
    } catch (error) {
      this.logger.error('Failed to get application requirements:', error);
      throw new HttpException(
        error.message || 'Failed to get application requirements',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Put('applications/requirements/:requirementId')
  // @Permissions(StudentPermissions.STUDENT_PROFILE_EDIT)
  @Public()
  async updateDocumentRequirementStatus(
    @Param('requirementId') requirementId: string,
    @Body()
    updateData: {
      acceptedByApplyGoal?: boolean | null;
      acceptedByUniversity?: boolean;
      rejectionReason?: string;
      status?: string;
      applicationId?: number;
      url?: string;
    }
    // @CurrentUser('id') userId: number,
    // @CurrentUser('roles') roles: string[]
  ) {
    try {
      this.logger.log(`Updating document requirement status: ${requirementId}`);

      const result = await this.studentService.updateApplicationRequirement(
        parseInt(requirementId),
        {
          ...updateData,
          applicationId: updateData?.['applicationId']
            ? Number(updateData['applicationId'])
            : undefined,
          acceptedByApplyGoalUserId: updateData.acceptedByApplyGoal
            ? 1 // Placeholder for userId
            : undefined,
          acceptedByUniversityUserId: updateData.acceptedByUniversity
            ? 1 // Placeholder for userId
            : undefined
        }
      );

      return {
        status: 'success',
        data: result.data,
        message: result.message
      };
    } catch (error) {
      this.logger.error('Failed to update document requirement status:', error);
      throw new HttpException(
        error.message || 'Failed to update document requirement status',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Put('applications/:applicationId/stages/:stageId')
  @Public() // Temporarily public for testing
  // @Permissions(StudentPermissions.STUDENT_PROFILE_EDIT)
  async updateApplicationStage(
    @Param('applicationId') applicationId: string,
    @Param('stageId') stageId: string,
    @Body() updateData: { status: string }
  ) {
    try {
      this.logger.log(
        `Updating application stage: ${stageId} for application: ${applicationId} to status: ${updateData.status}`
      );

      // Validate status
      const validStatuses = ['pending', 'in_progress', 'completed', 'failed'];
      if (!validStatuses.includes(updateData.status)) {
        throw new HttpException(
          `Invalid status. Must be one of: ${validStatuses.join(', ')}`,
          HttpStatus.BAD_REQUEST
        );
      }

      const result = await this.studentService.updateApplicationStage(
        parseInt(applicationId),
        parseInt(stageId),
        updateData.status
      );

      return {
        status: 'success',
        data: result.data,
        message: result.message
      };
    } catch (error) {
      this.logger.error('Failed to update application stage:', error);
      throw new HttpException(
        error.message || 'Failed to update application stage',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // ==================== APPLICATION NOTES ====================

  @Post('applications/:applicationId/notes')
  @Public() // Temporarily public for testing
  // @Permissions(StudentPermissions.STUDENT_PROFILE_EDIT)
  async createApplicationNote(
    @Param('applicationId') applicationId: string,
    @Body()
    noteData: {
      noteType: string;
      title?: string;
      content: string;
      createdBy: number;
      isPrivate?: boolean;
      userInfo?: any;
      replyId?: number;
    }
  ) {
    try {
      this.logger.log(
        `Creating application note for application: ${applicationId}`
      );

      // Validate noteType
      const validNoteTypes = ['internal', 'student', 'university'];
      if (!validNoteTypes.includes(noteData.noteType)) {
        throw new HttpException(
          `Invalid noteType. Must be one of: ${validNoteTypes.join(', ')}`,
          HttpStatus.BAD_REQUEST
        );
      }

      // Validate required fields
      if (!noteData.content || !noteData.createdBy) {
        throw new HttpException(
          'Content and createdBy are required fields',
          HttpStatus.BAD_REQUEST
        );
      }

      const result = await this.studentService.createApplicationNote(
        parseInt(applicationId),
        noteData
      );

      return {
        status: 'success',
        data: result.data,
        message: result.message
      };
    } catch (error) {
      this.logger.error('Failed to create application note:', error);
      throw new HttpException(
        error.message || 'Failed to create application note',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('applications/:applicationId/notes')
  @Public() // Temporarily public for testing
  // @Permissions(StudentPermissions.STUDENT_PROFILE_VIEW)
  async getApplicationNotes(
    @Param('applicationId') applicationId: string,
    @Query('noteType') noteType?: string
  ) {
    try {
      this.logger.log(
        `Getting application notes for application: ${applicationId}${
          noteType ? `, noteType: ${noteType}` : ''
        }`
      );

      // Validate noteType if provided
      if (noteType) {
        const validNoteTypes = ['internal', 'student', 'university'];
        if (!validNoteTypes.includes(noteType)) {
          throw new HttpException(
            `Invalid noteType. Must be one of: ${validNoteTypes.join(', ')}`,
            HttpStatus.BAD_REQUEST
          );
        }
      }

      const result = await this.studentService.getApplicationNotes(
        parseInt(applicationId),
        noteType
      );

      return {
        status: 'success',
        data: result.data,
        message: result.message
      };
    } catch (error) {
      this.logger.error('Failed to get application notes:', error);
      throw new HttpException(
        error.message || 'Failed to get application notes',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // ==================== APPLICATION PROGRESS RECORDS ====================

  @Get('applications/:applicationId/progress-records')
  @Public() // Temporarily public for testing
  // @Permissions(StudentPermissions.STUDENT_PROFILE_VIEW)
  async getApplicationProgressRecords(
    @Param('applicationId') applicationId: string,
    @Query('recordType') recordType?: string
  ) {
    try {
      this.logger.log(
        `Getting application progress records for application: ${applicationId}`
      );

      const result = await this.studentService.getApplicationProgressRecords(
        parseInt(applicationId),
        recordType
      );

      return {
        status: 'success',
        data: result.data,
        message: result.message
      };
    } catch (error) {
      this.logger.error('Failed to get application progress records:', error);
      throw new HttpException(
        error.message || 'Failed to get application progress records',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('applications/:applicationId/progress-records')
  @Public() // Temporarily public for testing
  // @Permissions(StudentPermissions.STUDENT_PROFILE_EDIT)
  async createApplicationProgressRecord(
    @Param('applicationId') applicationId: string,
    @Body()
    recordData: {
      recordType: string;
      title: string;
      description: string;
      status?: string;
      recordDate?: string;
      amount?: number;
      currency?: string;
      proofLinks?: any[];
      attachments?: any[];
      createdBy: number;
      applicationStage?: string;
      applicationStep?: string;
    }
  ) {
    try {
      this.logger.log(
        `Creating application progress record for application: ${applicationId}`
      );

      // Validate required fields
      if (
        !recordData.recordType ||
        !recordData.title ||
        !recordData.description ||
        !recordData.createdBy
      ) {
        throw new HttpException(
          'recordType, title, description, and createdBy are required fields',
          HttpStatus.BAD_REQUEST
        );
      }

      // Validate status if provided
      if (recordData.status) {
        const validStatuses = ['completed', 'in_progress', 'failed'];
        if (!validStatuses.includes(recordData.status)) {
          throw new HttpException(
            `Invalid status. Must be one of: ${validStatuses.join(', ')}`,
            HttpStatus.BAD_REQUEST
          );
        }
      }

      const result = await this.studentService.createApplicationProgressRecord(
        parseInt(applicationId),
        recordData
      );

      return {
        status: 'success',
        data: result.data,
        message: result.message
      };
    } catch (error) {
      this.logger.error('Failed to create application progress record:', error);
      throw new HttpException(
        error.message || 'Failed to create application progress record',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('applications/requirements')
  // @Permissions(StudentPermissions.STUDENT_PROFILE_EDIT)
  @Public()
  async createApplicationRequirements(
    @Body()
    body: {
      applicationId: number;
      studentId?: string;
      items: Array<{
        documentTitle: string;
        documentName: string;
        url?: string;
        documentDescription?: string;
        isRequired?: boolean;
        allowedFormats?: string[];
        requiredByDate?: string;
        isNew?: boolean;
      }>;
    }
    // @CurrentUser('id') userId: number,
    // @CurrentUser('roles') roles: string[]
  ) {
    try {
      this.logger.log(
        `Creating application requirements for application: ${body?.applicationId}`
      );
      const result = await this.studentService.createApplicationRequirements(
        Number(body.applicationId),
        body.items || [],
        body.studentId
      );
      return {
        status: 'success',
        data: result.data || [],
        message: result.message
      };
    } catch (error) {
      this.logger.error('Failed to create application requirements:', error);
      throw new HttpException(
        error.message || 'Failed to create application requirements',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // Debug endpoint to check university data structure
  @Get('debug/university-data')
  @Public()
  async debugUniversityData() {
    try {
      const universityData =
        await this.studentService.universityListForStudentApplication();

      // Analyze what's missing
      const analysis = {
        totalUniversities: universityData?.data?.universities?.length || 0,
        universitiesWithData: 0,
        universitiesWithoutData: 0,
        missingDataDetails: []
      };

      if (universityData?.data?.universities) {
        universityData.data.universities.forEach((uni) => {
          const hasCountries = uni.countries && uni.countries.length > 0;
          const hasCampuses = uni.countries?.some(
            (c) => c.campuses && c.campuses.length > 0
          );
          const hasPrograms = uni.countries?.some((c) =>
            c.campuses?.some(
              (campus) =>
                campus.programLevels && campus.programLevels.length > 0
            )
          );

          if (hasCountries && hasCampuses && hasPrograms) {
            analysis.universitiesWithData++;
          } else {
            analysis.universitiesWithoutData++;
            analysis.missingDataDetails.push({
              universityId: uni.id,
              universityTitle: uni.title,
              hasCountries,
              hasCampuses,
              hasPrograms,
              countriesCount: uni.countries?.length || 0,
              campusesCount:
                uni.countries?.reduce(
                  (total, c) => total + (c.campuses?.length || 0),
                  0
                ) || 0,
              programsCount:
                uni.countries?.reduce(
                  (total, c) =>
                    total +
                    (c.campuses?.reduce(
                      (cTotal, campus) =>
                        cTotal + (campus.programLevels?.length || 0),
                      0
                    ) || 0),
                  0
                ) || 0
            });
          }
        });
      }

      return {
        status: 'success',
        data: {
          universityData,
          analysis,
          message: 'University data analysis for debugging'
        }
      };
    } catch (error) {
      this.logger.error('Failed to get university data for debugging:', error);
      throw new HttpException(
        error.message || 'Failed to get university data for debugging',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // Get filtered courses based on multiple criteria
  @Post('filter-courses')
  @Public()
  async getFilteredCourses(@Body() filters: CourseFilterDto) {
    try {
      this.logger.log(
        `Getting filtered courses with filters: ${JSON.stringify(filters)}`
      );

      const result = await this.studentService.getFilteredCourses(filters);

      return result;
    } catch (error) {
      this.logger.error('Failed to get filtered courses:', error);
      throw new HttpException(
        error.message || 'Failed to get filtered courses',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}

// const filterApplications = async (
//   applicationList: any[],
//   universityList: any[],
//   studentService: any
// ) => {
//   // Get unique student IDs to avoid duplicate API calls
//   const uniqueStudentIds = [
//     ...new Set(applicationList.map((app) => app.studentId))
//   ];
//   console.log('uniqueStudentIds=>', uniqueStudentIds);

//   // Fetch all student information in parallel
//   // Create a map for quick student lookup
//   const studentMap = new Map();
//   // Fetch all student information in parallel
//   for (const studentId of uniqueStudentIds) {
//     try {
//       console.log(`Fetching student: ${studentId}`);
//       const studentResponse = await studentService.getStudent(studentId);
//       console.log(`Student ${studentId} response:`, studentResponse);
//       studentMap.set(studentId, {
//         studentId,
//         data: studentResponse
//       });
//     } catch (error) {
//       console.error(`Failed to fetch student ${studentId}:`, error.message);
//       studentMap.set(studentId, {
//         studentId,
//         data: null,
//         error: error.message
//       });
//     }
//   }

//   console.log('studentMap=>', Object.fromEntries(studentMap));

//   // const studentPromises = uniqueStudentIds.map(async (studentId) => {
//   //   try {
//   //     const studentResponse = await studentService.getStudent(studentId);

//   //     console.log("Student api Response using studentId Response", studentResponse);
//   //     return {
//   //       studentId,
//   //       data: studentResponse
//   //     };
//   //   } catch (error) {
//   //     console.error(`Failed to fetch student ${studentId}:`, error.message);
//   //     return {
//   //       studentId,
//   //       data: null,
//   //       error: error.message
//   //     };
//   //   }
//   // });
//   // console.log('studentPromises=>', studentPromises);

//   // const studentResults = await Promise.all(studentPromises);

//   // Create a map for quick student lookup
//   // const studentMap = new Map();
//   // studentResults.forEach((result) => {
//   //   // ✅ Store both successful and failed results for better error handling
//   //   studentMap.set(result.studentId, result.data ? result : { error: result.error || 'Student not found' });
//   // });

//   return applicationList.map((application) => {
//     // Find the university
//     // const university = universityList?.find(
//     //   (uni) => uni.id === application.universityId
//     // ) || { id: application.universityId, title: 'Unknown University' };

//     // // Find the country within the university
//     // const country = university?.countries?.find(
//     //   (country) => country.id === application.universityCountryId
//     // ) || { id: application.universityCountryId, title: 'Unknown Country' };

//     // // Find the campus within the country
//     // const campus = country?.campuses?.find(
//     //   (campus) => campus.id === application.universityCountryCampus
//     // ) || { id: application.universityCountryCampus, title: 'Unknown Campus' };

//     // // Find the program level within the campus
//     // const programLevel = campus?.programLevels?.find(
//     //   (program) => program.id === application.programId
//     // ) || { id: application.programId, title: 'Unknown Program' };

//     // // Find the intake within the program level
//     // const intake = programLevel?.intakes?.find(
//     //   (intake) => intake.id === application.intakeId
//     // ) || { id: application.intakeId, title: 'Unknown Intake' };

//     // // Find the course within the intake
//     // const course = intake?.courses?.find(
//     //   (course) => course.id === application.courseId
//     // ) || { id: application.courseId, title: 'Unknown Course' };

//         // Find the university
//     const university = universityList?.find(
//       (uni) => uni.id === application.universityId
//     ) || { id: application.universityId, title: 'Unknown University' };
//     console.log('Found university:', university);

//     // Find the country within the university
//     const country = university?.countries?.find(
//       (country) => country.id === application.universityCountryId
//     ) || { id: application.universityCountryId, title: 'Unknown Country' };
//     console.log('Found country:', country);

//     // Find the campus within the country
//     const campus = country?.campuses?.find(
//       (campus) => campus.id === application.universityCountryCampus
//     ) || { id: application.universityCountryCampus, title: 'Unknown Campus' };
//     console.log('Found campus:', campus);

//     // Find the program level within the campus
//     const programLevel = campus?.programLevels?.find(
//       (program) => program.id === application.programId
//     ) || { id: application.programId, title: 'Unknown Program' };
//     console.log('Found programLevel:', programLevel);

//     // Find the intake within the program level
//     const intake = programLevel?.intakes?.find(
//       (intake) => intake.id === application.intakeId
//     ) || { id: application.intakeId, title: 'Unknown Intake' };
//     console.log('Found intake:', intake);

//     // Find the course within the intake
//     const course = intake?.courses?.find(
//       (course) => course.id === application.courseId
//     ) || { id: application.courseId, title: 'Unknown Course' };
//     console.log('Found course:', course);

//     // Get all intakes for the program level
//     const allIntakes = programLevel?.intakes?.map((intake) => ({
//       id: intake.id,
//       title: intake.title,
//       name: intake.name,
//       startDate: intake.startDate,
//       endDate: intake.endDate,
//       classStartDate: intake.classStartDate,
//       enrollementDate: intake.enrollementDate,
//       applicationTypes: intake.applicationTypes,
//       applicationProcessDuration: intake.applicationProcessDuration
//     }));

//     // Get student information
//     const studentResponse = studentMap.get(application.studentId);
//     const studentData = studentResponse?.data?.student;
//     const studentError = studentResponse?.error;
//     const student = studentData
//       ? {
//           id: studentData.id,
//           studentId: studentData.studentId,
//           firstName: studentData.personalInfo?.firstName || '',
//           lastName: studentData.personalInfo?.lastName || '',
//           email: studentData.personalInfo?.email || null,
//           phone: studentData.personalInfo?.phone || null,
//           dateOfBirth: studentData.personalInfo?.dateOfBirth || null,
//           gender: studentData.personalInfo?.gender || null,
//           nationality: studentData.personalInfo?.nationality || null,
//           passportNumber: studentData.personalInfo?.passportNumber || null,
//           profilePicture: studentData.personalInfo?.profilePicture || null
//         }
//       : {
//           studentId: application.studentId,
//           firstName: 'Unknown',
//           lastName: 'Student',
//           email: null,
//           phone: null,
//           error: studentError
//         };

//     // Return the application with nested objects including student info
//     return {
//       ...application,
//       student,
//       university: {
//         id: university.id,
//         title: university.title
//       },
//       country: {
//         id: country.id,
//         title: country.title
//       },
//       campus: {
//         id: campus.id,
//         title: campus.title
//       },
//       program: {
//         id: programLevel.id,
//         title: programLevel.title
//       },
//       intake: {
//         id: intake.id,
//         title: intake.title,
//         name: intake.name,
//         startDate: intake.startDate,
//         endDate: intake.endDate,
//         classStartDate: intake.classStartDate,
//         enrollementDate: intake.enrollementDate,
//         applicationTypes: intake.applicationTypes,
//         applicationProcessDuration: intake.applicationProcessDuration
//       },
//       course: {
//         id: course.id,
//         title: course.title,
//         lastAcademic: course.lastAcademic
//       },
//       allIntakes
//     };
//   });
// };



const filterApplications = async (
  applicationList: any[],
  universityList: any[],
  studentService: any
) => {
  // Get unique student IDs to avoid duplicate API calls
  const uniqueStudentIds = [
    ...new Set(applicationList.map((app) => app.studentId))
  ];
  console.log('uniqueStudentIds=>', uniqueStudentIds);

  // Fetch all student information in parallel
  const studentMap = new Map();
  for (const studentId of uniqueStudentIds) {
    try {
      console.log(`Fetching student: ${studentId}`);
      const studentResponse = await studentService.getStudent(studentId);
      console.log(`Student ${studentId} response:`, studentResponse);
      studentMap.set(studentId, {
        studentId,
        data: studentResponse
      });
    } catch (error) {
      console.error(`Failed to fetch student ${studentId}:`, error.message);
      studentMap.set(studentId, {
        studentId,
        data: null,
        error: error.message
      });
    }
  }

  console.log('studentMap=>', Object.fromEntries(studentMap));

  return applicationList.map((application) => {
    console.log(`Processing application ${application.id}:`, {
      universityId: application.universityId,
      universityCountryId: application.universityCountryId,
      universityCountryCampus: application.universityCountryCampus,
      programId: application.programId,
      intakeId: application.intakeId,
      courseId: application.courseId
    });

    // Find the university
    const university = universityList?.find(
      (uni) => uni.id === application.universityId
    ) || { id: application.universityId, title: 'Unknown University' };
    console.log('Found university:', university);

    // Find the country within the university
    const country = university?.countries?.find(
      (country) => country.id === application.universityCountryId
    ) || { id: application.universityCountryId, title: 'Unknown Country' };
    console.log('Found country:', country);

    // Find the campus within the country
    const campus = country?.campuses?.find(
      (campus) => campus.id === application.universityCountryCampus
    ) || { id: application.universityCountryCampus, title: 'Unknown Campus' };
    console.log('Found campus:', campus);

    // Find the program level within the campus
    let programLevel = campus?.programLevels?.find(
      (program) => program.id === application.programId
    );

    // If program level not found in the specific campus, search across all campuses
    if (!programLevel) {
      console.log(`Program level ${application.programId} not found in campus ${campus.id}, searching across all campuses...`);
      for (const countryItem of university?.countries || []) {
        for (const campusItem of countryItem?.campuses || []) {
          const foundProgram = campusItem?.programLevels?.find(
            (program) => program.id === application.programId
          );
          if (foundProgram) {
            programLevel = foundProgram;
            console.log(`Found program level ${application.programId} in campus ${campusItem.id}:`, foundProgram);
            break;
          }
        }
        if (programLevel) break;
      }
    }

    programLevel = programLevel || { id: application.programId, title: 'Unknown Program' };
    console.log('Found programLevel:', programLevel);

    // Find the intake within the program level
    const intake = programLevel?.intakes?.find(
      (intake) => intake.id === application.intakeId
    ) || { id: application.intakeId, title: 'Unknown Intake' };
    console.log('Found intake:', intake);

    // Find the course within the intake
    const course = intake?.courses?.find(
      (course) => course.id === application.courseId
    ) || { id: application.courseId, title: 'Unknown Course' };
    console.log('Found course:', course);

    // Get all intakes for the program level
    const allIntakes = programLevel?.intakes?.map((intake) => ({
      id: intake.id,
      title: intake.title,
      name: intake.name,
      startDate: intake.startDate,
      endDate: intake.endDate,
      classStartDate: intake.classStartDate,
      enrollementDate: intake.enrollementDate,
      applicationTypes: intake.applicationTypes,
      applicationProcessDuration: intake.applicationProcessDuration
    }));

    // Get student information
    const studentResponse = studentMap.get(application.studentId);
    const studentData = studentResponse?.data?.student;
    const studentError = studentResponse?.error;
    const student = studentData
      ? {
          id: studentData.id,
          studentId: studentData.studentId,
          firstName: studentData.personalInfo?.firstName || '',
          lastName: studentData.personalInfo?.lastName || '',
          email: studentData.personalInfo?.email || null,
          phone: studentData.personalInfo?.phone || null,
          dateOfBirth: studentData.personalInfo?.dateOfBirth || null,
          gender: studentData.personalInfo?.gender || null,
          nationality: studentData.personalInfo?.nationality || null,
          passportNumber: studentData.personalInfo?.passportNumber || null,
          profilePicture: studentData.personalInfo?.profilePicture || null
        }
      : {
          studentId: application.studentId,
          firstName: 'Unknown',
          lastName: 'Student',
          email: null,
          phone: null,
          error: studentError
        };

    // Return the application with nested objects including student info
    return {
      ...application,
      student,
      university: {
        id: university.id,
        title: university.title
      },
      country: {
        id: country.id,
        title: country.title
      },
      campus: {
        id: campus.id,
        title: campus.title
      },
      program: {
        id: programLevel.id,
        title: programLevel.title
      },
      intake: {
        id: intake.id,
        title: intake.title,
        name: intake.name,
        startDate: intake.startDate,
        endDate: intake.endDate,
        classStartDate: intake.classStartDate,
        enrollementDate: intake.enrollementDate,
        applicationTypes: intake.applicationTypes,
        applicationProcessDuration: intake.applicationProcessDuration
      },
      course: {
        id: course.id,
        title: course.title,
        lastAcademic: course.lastAcademic
      },
      allIntakes
    };
  });
};
