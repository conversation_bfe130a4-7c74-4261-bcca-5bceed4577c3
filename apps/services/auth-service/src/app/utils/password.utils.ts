import { Logger } from '@nestjs/common';
import { Op } from 'sequelize';

// ─── Helper Methods ─────────────────────────────────────────────────
export function generateSecureToken(): string {
  const crypto = require('crypto');
  return crypto.randomBytes(32).toString('hex');
}

export function isValidPassword(password: string): boolean {
  // Password must be at least 8 characters long and contain:
  // - At least one uppercase letter
  // - At least one lowercase letter
  // - At least one number
  // - At least one special character
  const passwordRegex =
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
  return passwordRegex.test(password);
}

// These functions are now handled by private methods in auth.service.ts
// Keeping the function signatures for backward compatibility but they're deprecated
export async function sendPasswordResetEmail(
  email: string,
  name: string,
  resetToken: string
): Promise<void> {
  Logger.warn('sendPasswordResetEmail is deprecated. Use auth.service.ts private method instead.');
  // This function is kept for backward compatibility but should not be used
}

export async function sendPasswordChangeConfirmationEmail(
  email: string,
  name: string
): Promise<void> {
  Logger.warn('sendPasswordChangeConfirmationEmail is deprecated. Use auth.service.ts private method instead.');
  // This function is kept for backward compatibility but should not be used
}

// ─── Cleanup Methods ─────────────────────────────────────────────────
export async function cleanupExpiredResetTokens(): Promise<void> {
  try {
    const result = await this.passwordResetTokenModel.destroy({
      where: {
        expiresAt: {
          [Op.lt]: new Date()
        }
      }
    });
    Logger.log(`Cleaned up ${result} expired password reset tokens`);
  } catch (error) {
    Logger.error('Failed to cleanup expired reset tokens:', error);
  }
}
