'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // Add isLoginApplicable column to users table
    await queryInterface.addColumn('users', 'isLoginApplicable', {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Indicates whether the user can login to the system'
    });

    console.log('✅ Successfully added isLoginApplicable column to users table');
  },

  async down(queryInterface, Sequelize) {
    // Remove the isLoginApplicable column
    await queryInterface.removeColumn('users', 'isLoginApplicable');
    console.log('✅ Successfully removed isLoginApplicable column from users table');
  }
};