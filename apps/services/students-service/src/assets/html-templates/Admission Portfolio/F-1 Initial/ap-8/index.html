<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>School Performance Fact Sheet — {{universityName}}</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    
     body { 
        background: #ffffff;
        font-family: "Times New Roman", Times, serif; 
    }
    
  </style>

  <div style="display: flex; align-items: center; text-align: center; flex-direction: column;">
                    <h1 class="text-[24px] font-bold tracking-wide">{{universityName}}</h1>
        <p class="text-sm">{{universityAddress}}, {{universityPostalCode}}</p>
        <p class="text-sm">Tel: {{universityContact}} | Fax: (************* | <a href="{{universityWebsite}}" class="underline">{{universityWebsite}}</a></p>
            <h2 style="font-weight: 700; font-size: 20px; line-height: 15px; color: black; border-bottom: 1px solid black; margin-top: 32px;">Salary and Wage Information <span class="italic">(includes data for the two calendar years prior to reporting)</span></h2>
        </div>
</head>
<body class="antialiased text-neutral-900 ">
  <main class="p-6">
    
      <!-- Self-Employed / Freelance Positions -->
      
        <!-- Institutional Employment -->
         

      

      <!-- Gainfully Employed Categories -->
      <section class="mt-8">
        <h4 class="mt-6 text-xl font-bold text-center">Annual salary and wages reported for graduates employed in the field.</h4>

        <table class="mt-6 w-full border border-neutral-400 text-sm">
          <thead class="">
            <tr>
              <th class="border border-neutral-400 px-2 py-1">Calendar Year</th>
              <th class="border border-neutral-400 px-2 py-1">Graduates
Available for
Employment</th>
              <th class="border border-neutral-400 px-2 py-1">Graduates
Employed in
the Field</th>
              <th class="border border-neutral-400 px-2 py-1">$0,000 -
$5,000</th>
              <th class="border border-neutral-400 px-2 py-1">$20,001-
$25,000</th>
              <th class="border border-neutral-400 px-2 py-1">$25,001-
$30,000</th>
<th class="border border-neutral-400 px-2 py-1">$30,001-
$35,000</th>
<th class="border border-neutral-400 px-2 py-1">$35,001-
$40,000</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="border border-neutral-400 px-2 py-1 text-center">2025</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">12</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">4</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">0</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">0</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">0</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">2</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">0</td>
            </tr>
            <tr>
              <td class="border border-neutral-400 px-2 py-1 text-center">2026</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">12</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">9</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">1</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">1</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">1</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">1</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">1</td>
            </tr>
          </tbody>
        </table>

        <table class="mt-6 w-full border border-neutral-400 text-sm">
          <thead class="">
            <tr>
              <th class="border border-neutral-400 px-2 py-1">Calendar Year</th>
              <th class="border border-neutral-400 px-2 py-1">Graduates
Available for
Employment</th>
              <th class="border border-neutral-400 px-2 py-1">Graduates
Employed in
the Field</th>
              <th class="border border-neutral-400 px-2 py-1">$0,000 -
$5,000</th>
              <th class="border border-neutral-400 px-2 py-1">$20,001-
$25,000</th>
              <th class="border border-neutral-400 px-2 py-1">$25,001-
$30,000</th>
<th class="border border-neutral-400 px-2 py-1">$30,001-
$35,000</th>
<th class="border border-neutral-400 px-2 py-1">$35,001-
$40,000</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="border border-neutral-400 px-2 py-1 text-center">2025</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">12</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">4</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">1</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">0</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">0</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">0</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">1</td>
            </tr>
            <tr>
              <td class="border border-neutral-400 px-2 py-1 text-center">2026</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">12</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">9</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">0</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">1</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">1</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">1</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">1</td>
            </tr>
          </tbody>
        </table>

        <p class="mt-8 text-xm">A list of sources used to substantiate salary disclosures is available from International American
University. Please call {{universityContact}} or email {{universityEmail}}..</p>

        
      </section>

      <div class="mt-8 text-sm">
          <p><span class="font-regular">Student's Initials </span><span class="bg-neutral-200">{{studentShortName}}</span>  / <span class="font-regular">Date </span><span class="bg-neutral-200">{{apDate}}</span></p>
          <p class="text-[12px] font-bold">Initial Only after you have had sufficient time to read and understand the information.</p>
        </div>
        
      <!-- <div class="absolute bottom-3.5 left-1/2 transform -translate-x-1/2 text-center text-sm text-gray-600">
        <em>Page 4 of 7</em>
      </div> -->
    </section>
  </main>
</body>
</html>
