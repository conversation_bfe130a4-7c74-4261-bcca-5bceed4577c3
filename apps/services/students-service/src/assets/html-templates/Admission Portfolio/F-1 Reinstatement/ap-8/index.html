<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>School Performance Fact Sheet — {{universityName}}</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    
     body { 
        background: #ffffff;
        font-family: "Times New Roman", Times, serif; 
    }
    
  </style>

  
</head>
<body class="antialiased text-neutral-900 ">
  <main class="p-6">
    <div style="display: flex; align-items: center; text-align: center; flex-direction: column;">
            <h1 style="font-weight: 700; font-size: 28px; color: black;">{{universityName}}</h1>
            <p style="font-weight: 400; font-size: 16px; line-height: 100%; color: black;">{{universityAddress}} | {{universityCity}}, {{universityState}} {{universityPostalCode}}</p>
            <p style="font-weight: 400; font-size: 16px; line-height: 100%; color: black;">Tel: {{universityContact}} | {{universityWebsite}}</p>
            <h2 style="font-weight: 700; font-size: 20px; line-height: 15px; color: black; border-bottom: 1px solid black; margin-top: 32px;">Salary and Wage Information <span class="italic">(includes data for the two calendar years prior to reporting)</span></h2>
        </div>
      <!-- Self-Employed / Freelance Positions -->
      
        <!-- Institutional Employment -->
         

      

      <!-- Gainfully Employed Categories -->
      <section class="mt-8">
        <h4 class="mt-6 text-xl font-bold text-center">Annual salary and wages reported for graduates employed in the field.</h4>

        <table class="mt-6 w-full border border-neutral-400 text-xm">
          <thead class="">
            <tr>
              <th class="border border-neutral-400 px-2 py-1">Calendar Year</th>
              <th class="border border-neutral-400 px-2 py-1">Graduates
Available for
Employment</th>
              <th class="border border-neutral-400 px-2 py-1">Graduates
Employed in
the Field</th>
              <th class="border border-neutral-400 px-2 py-1">10,000 -
$15,000</th>
              <th class="border border-neutral-400 px-2 py-1">20,001-
$25,000</th>
              <th class="border border-neutral-400 px-2 py-1">$25,001-
$30,000</th>
<th class="border border-neutral-400 px-2 py-1">135,001-
140,000</th>
<th class="border border-neutral-400 px-2 py-1">Student Not Reporting Salary</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="border border-neutral-400 px-2 py-1 text-center">2022</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">N/A</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">N/A</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">N/A</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">N/A</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">N/A</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">N/A</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">N/A</td>
            </tr>
            <tr>
              <td class="border border-neutral-400 px-2 py-1 text-center">2023</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">0</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">0</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">0</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">0</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">0</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">0</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">0</td>
            </tr>
          </tbody>
        </table>

        

        <p class="mt-8 text-xl">A list of sources used to substantiate salary disclosures is available from International American
University. Please call {{universityContact}} or email {{universityEmail}}..</p>

        
      </section>

      <div class="mt-8 text-xl">
          <p><span class="font-regular">Student's Initials </span><span class="bg-neutral-200">____</span>  / <span class="font-regular">Date </span><span class="bg-neutral-200">_______</span></p>
          <p class="text-[14px] font-bold">Initial Only after you have had sufficient time to read and understand the information.</p>
        </div>
        
      <div class="absolute bottom-3.5 left-1/2 transform -translate-x-1/2 text-center text-sm text-gray-600">
        <em>Page 4 of 7</em>
      </div>
    </section>
  </main>
</body>
</html>
