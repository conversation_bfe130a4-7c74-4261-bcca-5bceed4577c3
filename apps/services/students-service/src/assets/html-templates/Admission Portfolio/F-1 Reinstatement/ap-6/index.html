<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>School Performance Fact Sheet — {{universityName}}</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    
     body { 
        background: #ffffff;
        font-family: "Times New Roman", Times, serif; 
    }
    
  </style>

  
</head>
<body class="antialiased text-neutral-900 ">
  <main class="p-6">
    
    <div style="display: flex; align-items: center; text-align: center; flex-direction: column;">
            <h1 style="font-weight: 700; font-size: 28px; color: black;">{{universityName}}</h1>
            <p style="font-weight: 400; font-size: 16px; line-height: 100%; color: black;">{{universityAddress}} | {{universityCity}}, {{universityState}} {{universityPostalCode}}</p>
            <p style="font-weight: 400; font-size: 16px; line-height: 100%; color: black;">Tel: {{universityContact}} | {{universityWebsite}}</p>
            <h2 style="font-weight: 700; font-size: 20px; line-height: 15px; color: black; border-bottom: 1px solid black; margin-top: 32px;">JOB PLACEMENT RATES <span class="italic normal-case">(includes data for the two calendar years prior to reporting)</span></h2>
        </div>

      <!-- Job Placement Rates -->
      
    
        <table class="mt-6 w-full border border-neutral-400 text-xm">
          <thead class="">
            <tr>
              <th class="border border-neutral-400 px-2 py-1">Calendar Year</th>
              <th class="border border-neutral-400 px-2 py-1"># of Students Who Began Program</th>
              <th class="border border-neutral-400 px-2 py-1"># of Graduates</th>
              <th class="border border-neutral-400 px-2 py-1">Graduates Available for Employment</th>
              <th class="border border-neutral-400 px-2 py-1">Graduates Employed in the Field</th>
              <th class="border border-neutral-400 px-2 py-1">Placement Rate % Employed in Field</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="border border-neutral-400 px-2 py-1 text-center">2022</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">N/A</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">N/A</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">N/A</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">N/A</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">N/A</td>
            </tr>
            <tr>
              <td class="border border-neutral-400 px-2 py-1 text-center">2023</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">1</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">1</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">1</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">1</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">0.00%</td>
            </tr>
          </tbody>
        </table>

        <p class="mt-8 text-xl">You may obtain from the institution a list of the employment positions determined to be in the field for which a student received education and training. You may request this list from a Career Service Representative or review institution’s website and catalog.</p>
      </section>

      <!-- Gainfully Employed Categories -->
      <section class="mt-8">
        <h3 class=" text-[16px] font-bold uppercase tracking-wide">Gainfully Employed Categories <span class="italic text-[14px] normal-case">(includes data for the two calendar years prior to reporting)</span></h3>

        
        <h4 class="mt-4 text-xl font-bold text-center"><u>Part-Time vs. Full-Time Employment</u></h4>
        <table class="mt-6 w-full border border-neutral-400 text-xm">
          <thead class="">
            <tr>
              <th class="border border-neutral-400 px-2 py-1">Program</th>
              <th class="border border-neutral-400 px-2 py-1">Graduates Employed in the Field 20-29 Hours Per Week</th>
              <th class="border border-neutral-400 px-2 py-1">Graduates Employed in the Field at least 30 Hours Per Week</th>
              <th class="border border-neutral-400 px-2 py-1">Total Graduates Employed in the Field</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="border border-neutral-400 px-2 py-1 text-center">2022</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">N/A</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">N/A</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">N/A</td>
            </tr>
            <tr>
              <td class="border border-neutral-400 px-2 py-1 text-center">2023</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">0</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">0</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">0</td>
            </tr>
          </tbody>
        </table>

        <h4 class="mt-6 text-xl font-bold text-center"><u>Single Position vs. Concurrent Aggregated Positions</u></h4>
        <table class="mt-6 w-full border border-neutral-400 text-xm">
          <thead class="">
            <tr>
              <th class="border border-neutral-400 px-2 py-1">Program</th>
              <th class="border border-neutral-400 px-2 py-1">Graduates Employed in the Field 20-29 Hours Per Week</th>
              <th class="border border-neutral-400 px-2 py-1">Graduates Employed in the Field In Concurrent Aggregated Position</th>
              <th class="border border-neutral-400 px-2 py-1">Total Graduates Employed in the Field</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="border border-neutral-400 px-2 py-1 text-center">2022</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">N/A</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">N/A</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">N/A</td>
            </tr>
            <tr>
              <td class="border border-neutral-400 px-2 py-1 text-center">2023</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">0</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">0</td>
              <td class="border border-neutral-400 px-2 py-1 text-center">0</td>
            </tr>
          </tbody>
        </table>
      </section>

      <div class="absolute bottom-2.5 left-1/2 transform -translate-x-1/2 text-center text-sm text-gray-600">
        <em>Page 2 of 7</em>
      </div>
    </section>
  </main>
</body>
</html>
