<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Times New Roman', Times, serif;
        }

        p,
        h1,
        h2,
        ul,
        li {
            margin: 0;
        }
    </style>
</head>

<body>
    <div style="padding: 26px">
        <!-- Header Section -->
        <h2 style="color: #006BB6; font-size: 18px; font-weight: 700; margin-bottom: 12px;">{{universityName}} <span
                style="color: black">| SKILL ASSESSMENT</span></h2>

        <!-- Description Text -->
        <p style="font-size: 12px; color: black; text-align: justify; margin-bottom: 18px; margin-left: 20px;">
            To ensure that students will be successful, an assessment shall be made at the time of admissions
            evaluation. An assessment shall be made on whether each prospective student has the skills and competencies
            to succeed in an online learning environment, as well as a student's access to computer, software, and
            internet technologies. These will be taken into consideration before admitting a prospective student into
            the program. Applicants are to respond to the survey below that {{universityName}} may assess the applicant's ability to be
            successful in an online learning environment. (NOTE: Answering "No" to anyone question does not necessarily
            disqualify you from admissions.
        </p>

        <!-- Yes/No Headers -->
        <div style="display: flex; gap: 10px; margin-bottom: 10px; margin-left: 54px; font-size: 10px;">
            <p style="font-weight: 400;">Yes</p>
            <p style="font-weight: 400;">No</p>
        </div>

        <!-- Checklist Items -->
        <div style="display: flex; flex-direction: column; gap: 10px; margin-left: 34px;">
            <div style="display: flex; gap: 10px; align-items: flex-start; font-weight: 400; text-align: justify;">
                1.
                <!-- <input type="checkbox" checked style="margin-top: 3px;"> -->
                <svg width="18" height="18" viewBox="0 0 12 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M1.22998 9.12451C1.23015 9.48324 1.52161 9.77393 1.88037 9.77393H7.48584C7.84449 9.77378 8.13508 9.48315 8.13525 9.12451V5.85107C8.34971 5.54114 8.56817 5.24081 8.78564 4.95264V9.12451L8.78467 9.14111C8.77568 9.84547 8.20678 10.4144 7.50244 10.4233H1.86377C1.1594 10.4144 0.589554 9.84548 0.580566 9.14111V3.20459C0.580566 2.48682 1.16262 1.90479 1.88037 1.90479H7.50244C7.58663 1.90586 7.66893 1.9148 7.74854 1.93115C7.49897 2.13625 7.25442 2.34503 7.01709 2.5542H1.88037C1.5215 2.5542 1.22998 2.84571 1.22998 3.20459V9.12451ZM11.0122 1.32666C9.77739 2.39258 6.96714 5.58601 5.52783 8.90088L1.57666 4.86768L2.58252 4.04639L4.87939 5.85205C5.80676 4.64506 8.43301 2.0725 10.7612 0.757324L11.0122 1.32666Z"
                        fill="black" />
                </svg>

                <input type="checkbox" style="margin-top: 3px;">
                <p style="font-size: 12px; font-weight: 400;"> I have access to a reliable computer with a stable
                    internet connection.</p>
            </div>
            <div style="display: flex; gap: 10px; align-items: flex-start;">
                2.
                <svg width="18" height="18" viewBox="0 0 12 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M1.22998 9.12451C1.23015 9.48324 1.52161 9.77393 1.88037 9.77393H7.48584C7.84449 9.77378 8.13508 9.48315 8.13525 9.12451V5.85107C8.34971 5.54114 8.56817 5.24081 8.78564 4.95264V9.12451L8.78467 9.14111C8.77568 9.84547 8.20678 10.4144 7.50244 10.4233H1.86377C1.1594 10.4144 0.589554 9.84548 0.580566 9.14111V3.20459C0.580566 2.48682 1.16262 1.90479 1.88037 1.90479H7.50244C7.58663 1.90586 7.66893 1.9148 7.74854 1.93115C7.49897 2.13625 7.25442 2.34503 7.01709 2.5542H1.88037C1.5215 2.5542 1.22998 2.84571 1.22998 3.20459V9.12451ZM11.0122 1.32666C9.77739 2.39258 6.96714 5.58601 5.52783 8.90088L1.57666 4.86768L2.58252 4.04639L4.87939 5.85205C5.80676 4.64506 8.43301 2.0725 10.7612 0.757324L11.0122 1.32666Z"
                        fill="black" />
                </svg>
                <input type="checkbox" style="margin-top: 3px;">
                <p style="font-size: 12px;">I am capable of using standard hardware, such as a computer, USB drive,
                    mouse, keyboard, etc.</p>
            </div>
            <div style="display: flex; gap: 10px; align-items: flex-start;">
                3.
                <svg width="18" height="18" viewBox="0 0 12 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M1.22998 9.12451C1.23015 9.48324 1.52161 9.77393 1.88037 9.77393H7.48584C7.84449 9.77378 8.13508 9.48315 8.13525 9.12451V5.85107C8.34971 5.54114 8.56817 5.24081 8.78564 4.95264V9.12451L8.78467 9.14111C8.77568 9.84547 8.20678 10.4144 7.50244 10.4233H1.86377C1.1594 10.4144 0.589554 9.84548 0.580566 9.14111V3.20459C0.580566 2.48682 1.16262 1.90479 1.88037 1.90479H7.50244C7.58663 1.90586 7.66893 1.9148 7.74854 1.93115C7.49897 2.13625 7.25442 2.34503 7.01709 2.5542H1.88037C1.5215 2.5542 1.22998 2.84571 1.22998 3.20459V9.12451ZM11.0122 1.32666C9.77739 2.39258 6.96714 5.58601 5.52783 8.90088L1.57666 4.86768L2.58252 4.04639L4.87939 5.85205C5.80676 4.64506 8.43301 2.0725 10.7612 0.757324L11.0122 1.32666Z"
                        fill="black" />
                </svg>
                <input type="checkbox" style="margin-top: 3px;">
                <p style="font-size: 12px;">I possess good computer software skills and can use Microsoft Office
                    software (Word, Excel, PowerPoint), web browsers, and
                    email programs.</p>
            </div>
            <div style="display: flex; gap: 10px; align-items: flex-start;">
                4.
                <svg width="18" height="18" viewBox="0 0 12 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M1.22998 9.12451C1.23015 9.48324 1.52161 9.77393 1.88037 9.77393H7.48584C7.84449 9.77378 8.13508 9.48315 8.13525 9.12451V5.85107C8.34971 5.54114 8.56817 5.24081 8.78564 4.95264V9.12451L8.78467 9.14111C8.77568 9.84547 8.20678 10.4144 7.50244 10.4233H1.86377C1.1594 10.4144 0.589554 9.84548 0.580566 9.14111V3.20459C0.580566 2.48682 1.16262 1.90479 1.88037 1.90479H7.50244C7.58663 1.90586 7.66893 1.9148 7.74854 1.93115C7.49897 2.13625 7.25442 2.34503 7.01709 2.5542H1.88037C1.5215 2.5542 1.22998 2.84571 1.22998 3.20459V9.12451ZM11.0122 1.32666C9.77739 2.39258 6.96714 5.58601 5.52783 8.90088L1.57666 4.86768L2.58252 4.04639L4.87939 5.85205C5.80676 4.64506 8.43301 2.0725 10.7612 0.757324L11.0122 1.32666Z"
                        fill="black" />
                </svg>
                <input type="checkbox" style="margin-top: 3px;">
                <p style="font-size: 12px;"> I am a user/subscriber of web forums, blogs, social networking sites
                    (LinkedIn, Facebook), e-commerce (Amazon, eBay) sites.</p>
            </div>
            <div style="display: flex; gap: 10px; align-items: flex-start;">
                5.
                <svg width="18" height="18" viewBox="0 0 12 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M1.22998 9.12451C1.23015 9.48324 1.52161 9.77393 1.88037 9.77393H7.48584C7.84449 9.77378 8.13508 9.48315 8.13525 9.12451V5.85107C8.34971 5.54114 8.56817 5.24081 8.78564 4.95264V9.12451L8.78467 9.14111C8.77568 9.84547 8.20678 10.4144 7.50244 10.4233H1.86377C1.1594 10.4144 0.589554 9.84548 0.580566 9.14111V3.20459C0.580566 2.48682 1.16262 1.90479 1.88037 1.90479H7.50244C7.58663 1.90586 7.66893 1.9148 7.74854 1.93115C7.49897 2.13625 7.25442 2.34503 7.01709 2.5542H1.88037C1.5215 2.5542 1.22998 2.84571 1.22998 3.20459V9.12451ZM11.0122 1.32666C9.77739 2.39258 6.96714 5.58601 5.52783 8.90088L1.57666 4.86768L2.58252 4.04639L4.87939 5.85205C5.80676 4.64506 8.43301 2.0725 10.7612 0.757324L11.0122 1.32666Z"
                        fill="black" />
                </svg>
                <input type="checkbox" style="margin-top: 3px;">
                <p style="font-size: 12px;"> I have previously completed a course, academic program, and/or a
                    professional training course online.</p>
            </div>
            <div style="display: flex; gap: 10px; align-items: flex-start;">
                6.
                <svg width="18" height="18" viewBox="0 0 12 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M1.22998 9.12451C1.23015 9.48324 1.52161 9.77393 1.88037 9.77393H7.48584C7.84449 9.77378 8.13508 9.48315 8.13525 9.12451V5.85107C8.34971 5.54114 8.56817 5.24081 8.78564 4.95264V9.12451L8.78467 9.14111C8.77568 9.84547 8.20678 10.4144 7.50244 10.4233H1.86377C1.1594 10.4144 0.589554 9.84548 0.580566 9.14111V3.20459C0.580566 2.48682 1.16262 1.90479 1.88037 1.90479H7.50244C7.58663 1.90586 7.66893 1.9148 7.74854 1.93115C7.49897 2.13625 7.25442 2.34503 7.01709 2.5542H1.88037C1.5215 2.5542 1.22998 2.84571 1.22998 3.20459V9.12451ZM11.0122 1.32666C9.77739 2.39258 6.96714 5.58601 5.52783 8.90088L1.57666 4.86768L2.58252 4.04639L4.87939 5.85205C5.80676 4.64506 8.43301 2.0725 10.7612 0.757324L11.0122 1.32666Z"
                        fill="black" />
                </svg>
                <input type="checkbox" style="margin-top: 3px;">
                <p style="font-size: 12px;">I am capable of conducting online searches and doing internet research using
                    popular search engines (Google, Yahoo)</p>
            </div>
            <div style="display: flex; gap: 10px; align-items: flex-start;">
                7.
                <svg width="18" height="18" viewBox="0 0 12 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M1.22998 9.12451C1.23015 9.48324 1.52161 9.77393 1.88037 9.77393H7.48584C7.84449 9.77378 8.13508 9.48315 8.13525 9.12451V5.85107C8.34971 5.54114 8.56817 5.24081 8.78564 4.95264V9.12451L8.78467 9.14111C8.77568 9.84547 8.20678 10.4144 7.50244 10.4233H1.86377C1.1594 10.4144 0.589554 9.84548 0.580566 9.14111V3.20459C0.580566 2.48682 1.16262 1.90479 1.88037 1.90479H7.50244C7.58663 1.90586 7.66893 1.9148 7.74854 1.93115C7.49897 2.13625 7.25442 2.34503 7.01709 2.5542H1.88037C1.5215 2.5542 1.22998 2.84571 1.22998 3.20459V9.12451ZM11.0122 1.32666C9.77739 2.39258 6.96714 5.58601 5.52783 8.90088L1.57666 4.86768L2.58252 4.04639L4.87939 5.85205C5.80676 4.64506 8.43301 2.0725 10.7612 0.757324L11.0122 1.32666Z"
                        fill="black" />
                </svg>
                <input type="checkbox" style="margin-top: 3px;">
                <p style="font-size: 12px;">I am good at prioritizing tasks and often get things done ahead of time
                    without being reminded by my instructor.</p>
            </div>
            <div style="display: flex; gap: 10px; align-items: flex-start;">
                8.
                <svg width="18" height="18" viewBox="0 0 12 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M1.22998 9.12451C1.23015 9.48324 1.52161 9.77393 1.88037 9.77393H7.48584C7.84449 9.77378 8.13508 9.48315 8.13525 9.12451V5.85107C8.34971 5.54114 8.56817 5.24081 8.78564 4.95264V9.12451L8.78467 9.14111C8.77568 9.84547 8.20678 10.4144 7.50244 10.4233H1.86377C1.1594 10.4144 0.589554 9.84548 0.580566 9.14111V3.20459C0.580566 2.48682 1.16262 1.90479 1.88037 1.90479H7.50244C7.58663 1.90586 7.66893 1.9148 7.74854 1.93115C7.49897 2.13625 7.25442 2.34503 7.01709 2.5542H1.88037C1.5215 2.5542 1.22998 2.84571 1.22998 3.20459V9.12451ZM11.0122 1.32666C9.77739 2.39258 6.96714 5.58601 5.52783 8.90088L1.57666 4.86768L2.58252 4.04639L4.87939 5.85205C5.80676 4.64506 8.43301 2.0725 10.7612 0.757324L11.0122 1.32666Z"
                        fill="black" />
                </svg>
                <input type="checkbox" style="margin-top: 3px;">
                <p style="font-size: 12px;"> I can commit 10-15 hours to studying per week for every 3 unit course I
                    enroll and can plan blocks of time to devote to my
                    studies</p>
            </div>
            <div style="display: flex; gap: 10px; align-items: flex-start;">
                9.
                <svg width="18" height="18" viewBox="0 0 12 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M1.22998 9.12451C1.23015 9.48324 1.52161 9.77393 1.88037 9.77393H7.48584C7.84449 9.77378 8.13508 9.48315 8.13525 9.12451V5.85107C8.34971 5.54114 8.56817 5.24081 8.78564 4.95264V9.12451L8.78467 9.14111C8.77568 9.84547 8.20678 10.4144 7.50244 10.4233H1.86377C1.1594 10.4144 0.589554 9.84548 0.580566 9.14111V3.20459C0.580566 2.48682 1.16262 1.90479 1.88037 1.90479H7.50244C7.58663 1.90586 7.66893 1.9148 7.74854 1.93115C7.49897 2.13625 7.25442 2.34503 7.01709 2.5542H1.88037C1.5215 2.5542 1.22998 2.84571 1.22998 3.20459V9.12451ZM11.0122 1.32666C9.77739 2.39258 6.96714 5.58601 5.52783 8.90088L1.57666 4.86768L2.58252 4.04639L4.87939 5.85205C5.80676 4.64506 8.43301 2.0725 10.7612 0.757324L11.0122 1.32666Z"
                        fill="black" />
                </svg>
                <input type="checkbox" style="margin-top: 3px;">
                <p style="font-size: 12px;"> I possess good reading comprehension and can comprehend college-level texts
                    with minimal guidance from an instructor.</p>
            </div>
            <div style="display: flex; align-items: flex-start;">
                10.
                <svg style="margin-left: 2px;" width="18" height="18" viewBox="0 0 12 11" fill="none"
                    xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M1.22998 9.12451C1.23015 9.48324 1.52161 9.77393 1.88037 9.77393H7.48584C7.84449 9.77378 8.13508 9.48315 8.13525 9.12451V5.85107C8.34971 5.54114 8.56817 5.24081 8.78564 4.95264V9.12451L8.78467 9.14111C8.77568 9.84547 8.20678 10.4144 7.50244 10.4233H1.86377C1.1594 10.4144 0.589554 9.84548 0.580566 9.14111V3.20459C0.580566 2.48682 1.16262 1.90479 1.88037 1.90479H7.50244C7.58663 1.90586 7.66893 1.9148 7.74854 1.93115C7.49897 2.13625 7.25442 2.34503 7.01709 2.5542H1.88037C1.5215 2.5542 1.22998 2.84571 1.22998 3.20459V9.12451ZM11.0122 1.32666C9.77739 2.39258 6.96714 5.58601 5.52783 8.90088L1.57666 4.86768L2.58252 4.04639L4.87939 5.85205C5.80676 4.64506 8.43301 2.0725 10.7612 0.757324L11.0122 1.32666Z"
                        fill="black" />
                </svg>
                <input type="checkbox" style="margin-top: 3px; margin-left: 12px;">
                <p style="font-size: 12px; margin-left: 10px;"> I have local people around me (family, friends, and
                    professional colleagues) who can provide moral, academic, and/or
                    professional support to pursue my academic program.</p>
            </div>
            <!-- Add more checklist items similarly -->
        </div>

        <!-- Signature Section -->
        <div style="display: grid; grid-template-columns: 2fr 3fr 1fr; gap: 20px; margin-top: 20px;">
            <div>
                <div style="border-bottom: 1px solid black">
                    <p
                        style="font-weight: 400; font-size: 12px; text-align: center;  background-color: #E6E6E6; padding: 4px 0px; padding-left: 12px;">
                        {{studentName}}</p>
                </div>
                <p style="font-weight: 400; text-align: center; font-size: 12px; margin-top: 5px;">APPLICANT'S NAME
                </p>
            </div>
            <div>
                <div style="border-bottom: 1px solid black">
                    <p
                        style="font-weight: 400; font-size: 12px; text-align: center;  background-color: #E6E6E6; padding: 4px 0px; padding-left: 12px;">
                        {{studentName}}</p>
                </div>
                <p style="font-weight: 400; text-align: center; font-size: 12px; margin-top: 5px;">APPLICANT'S SIGNATURE
                </p>
            </div>
            <div>
                <div style="border-bottom: 1px solid black">
                    <p
                        style="font-weight: 400; font-size: 12px; text-align: center;  background-color: #E6E6E6; padding: 4px 0px; padding-left: 12px;">
                        {{apDate}}</p>
                </div>
                <p style="font-weight: 400; text-align: center; font-size: 12px; margin-top: 5px;">Date
                </p>
            </div>
        </div>

        <!-- Acknowledgments Section -->
        <h2 style="color: #006BB6; font-size: 18px; font-weight: 700;margin-top: 20px; margin-bottom: 12px;">{{universityName}} <span
                style="color: black">| ACKNOWLEDGMENTS</span></h2>
        <p style="font-size: 12px; margin-bottom: 20px; margin-left: 20px;">
            I hereby acknowledge the following statements are true and correct. I affirm that these statements will be
            considered as part of the admissions process and may influence a respective outcome. (NOTE: Answers should
            be Yes or No.)
        </p>
        <div style="display: flex; gap: 10px; margin-bottom: 10px; margin-left: 70px; font-size: 10px;">
            <p style="font-weight: 400;">Yes</p>
            <p style="font-weight: 400;">No</p>
        </div>

        <!-- Checklist Items -->
        <div style="display: flex; flex-direction: column; gap: 10px; margin-left: 54px;">
            <div style="display: flex; gap: 10px; align-items: flex-start; ">
                1.
                <!-- <input type="checkbox" checked style="margin-top: 3px;"> -->
                <svg width="18" height="18" viewBox="0 0 12 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M1.22998 9.12451C1.23015 9.48324 1.52161 9.77393 1.88037 9.77393H7.48584C7.84449 9.77378 8.13508 9.48315 8.13525 9.12451V5.85107C8.34971 5.54114 8.56817 5.24081 8.78564 4.95264V9.12451L8.78467 9.14111C8.77568 9.84547 8.20678 10.4144 7.50244 10.4233H1.86377C1.1594 10.4144 0.589554 9.84548 0.580566 9.14111V3.20459C0.580566 2.48682 1.16262 1.90479 1.88037 1.90479H7.50244C7.58663 1.90586 7.66893 1.9148 7.74854 1.93115C7.49897 2.13625 7.25442 2.34503 7.01709 2.5542H1.88037C1.5215 2.5542 1.22998 2.84571 1.22998 3.20459V9.12451ZM11.0122 1.32666C9.77739 2.39258 6.96714 5.58601 5.52783 8.90088L1.57666 4.86768L2.58252 4.04639L4.87939 5.85205C5.80676 4.64506 8.43301 2.0725 10.7612 0.757324L11.0122 1.32666Z"
                        fill="black" />
                </svg>

                <input type="checkbox" style="margin-top: 3px;">
                <p style="font-size: 12px;"> I have access to a reliable computer with a stable internet connection.</p>
            </div>
            <div style="display: flex; gap: 10px; align-items: flex-start;">
                2.
                <svg width="18" height="18" viewBox="0 0 12 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M1.22998 9.12451C1.23015 9.48324 1.52161 9.77393 1.88037 9.77393H7.48584C7.84449 9.77378 8.13508 9.48315 8.13525 9.12451V5.85107C8.34971 5.54114 8.56817 5.24081 8.78564 4.95264V9.12451L8.78467 9.14111C8.77568 9.84547 8.20678 10.4144 7.50244 10.4233H1.86377C1.1594 10.4144 0.589554 9.84548 0.580566 9.14111V3.20459C0.580566 2.48682 1.16262 1.90479 1.88037 1.90479H7.50244C7.58663 1.90586 7.66893 1.9148 7.74854 1.93115C7.49897 2.13625 7.25442 2.34503 7.01709 2.5542H1.88037C1.5215 2.5542 1.22998 2.84571 1.22998 3.20459V9.12451ZM11.0122 1.32666C9.77739 2.39258 6.96714 5.58601 5.52783 8.90088L1.57666 4.86768L2.58252 4.04639L4.87939 5.85205C5.80676 4.64506 8.43301 2.0725 10.7612 0.757324L11.0122 1.32666Z"
                        fill="black" />
                </svg>
                <input type="checkbox" style="margin-top: 3px;">
                <p style="font-size: 12px;">I am capable of using standard hardware, such as a computer, USB drive,
                    mouse, keyboard, etc.</p>
            </div>
            <div style="display: flex; gap: 10px; align-items: flex-start;">
                3.
                <svg width="18" height="18" viewBox="0 0 12 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M1.22998 9.12451C1.23015 9.48324 1.52161 9.77393 1.88037 9.77393H7.48584C7.84449 9.77378 8.13508 9.48315 8.13525 9.12451V5.85107C8.34971 5.54114 8.56817 5.24081 8.78564 4.95264V9.12451L8.78467 9.14111C8.77568 9.84547 8.20678 10.4144 7.50244 10.4233H1.86377C1.1594 10.4144 0.589554 9.84548 0.580566 9.14111V3.20459C0.580566 2.48682 1.16262 1.90479 1.88037 1.90479H7.50244C7.58663 1.90586 7.66893 1.9148 7.74854 1.93115C7.49897 2.13625 7.25442 2.34503 7.01709 2.5542H1.88037C1.5215 2.5542 1.22998 2.84571 1.22998 3.20459V9.12451ZM11.0122 1.32666C9.77739 2.39258 6.96714 5.58601 5.52783 8.90088L1.57666 4.86768L2.58252 4.04639L4.87939 5.85205C5.80676 4.64506 8.43301 2.0725 10.7612 0.757324L11.0122 1.32666Z"
                        fill="black" />
                </svg>
                <input type="checkbox" style="margin-top: 3px;">
                <p style="font-size: 12px;">I possess good computer software skills and can use Microsoft Office
                    software (Word, Excel, PowerPoint), web browsers, and
                    email programs.</p>
            </div>
            <div style="display: flex; align-items: flex-start;">
                4.
                <input type="checkbox" style="margin-top: 3px; margin-left: 10px;">
                <svg style="margin-left: 12px;" width="18" height="18" viewBox="0 0 12 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M1.22998 9.12451C1.23015 9.48324 1.52161 9.77393 1.88037 9.77393H7.48584C7.84449 9.77378 8.13508 9.48315 8.13525 9.12451V5.85107C8.34971 5.54114 8.56817 5.24081 8.78564 4.95264V9.12451L8.78467 9.14111C8.77568 9.84547 8.20678 10.4144 7.50244 10.4233H1.86377C1.1594 10.4144 0.589554 9.84548 0.580566 9.14111V3.20459C0.580566 2.48682 1.16262 1.90479 1.88037 1.90479H7.50244C7.58663 1.90586 7.66893 1.9148 7.74854 1.93115C7.49897 2.13625 7.25442 2.34503 7.01709 2.5542H1.88037C1.5215 2.5542 1.22998 2.84571 1.22998 3.20459V9.12451ZM11.0122 1.32666C9.77739 2.39258 6.96714 5.58601 5.52783 8.90088L1.57666 4.86768L2.58252 4.04639L4.87939 5.85205C5.80676 4.64506 8.43301 2.0725 10.7612 0.757324L11.0122 1.32666Z"
                        fill="black" />
                </svg>
                <p style="font-size: 12px; margin-left: 8px;"> I am a user/subscriber of web forums, blogs, social networking sites
                    (LinkedIn, Facebook), e-commerce (Amazon, eBay) sites.</p>
            </div>
        </div>
        <div style="margin-top: 20px;">
                <h2 style="color: #006BB6; font-size: 18px; font-weight: 700; margin-bottom: 12px;">{{universityName}} <span
                        style="color: black">| EMERGENCY CONTACT INFO</span></h2>
                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; margin-top: 10px; margin-left: 20px;">
                    <div>
                        <p style="font-weight: 400; text-align: left; font-size: 12px; margin-top: 5px;">(LEGAL
                            NAME)FIRST NAME
                        </p>
                        <div style="border-bottom: 1px solid black">
                            <p
                                style="font-weight: 400; font-size: 12px; text-align: center;  background-color: #E6E6E6; padding: 4px 0px; padding-left: 12px;">
                                {{studentFirstName}}</p>
                        </div>
                    </div>
                    <div>
                        <p style="font-weight: 400; text-align: center; font-size: 12px; margin-top: 5px;">MIDDLE NAME
                        </p>
                        <div style="border-bottom: 1px solid black">
                            <p
                                style="font-weight: 400; font-size: 12px; text-align: center;  background-color: #E6E6E6; padding: 4px 0px; padding-left: 12px;">
                                {{studentMiddleName}}</p>
                        </div>
                    </div>
                    <div>
                        <p style="font-weight: 400; text-align: center; font-size: 12px; margin-top: 5px;">LAST NAME
                        </p>
                        <div style="border-bottom: 1px solid black">
                            <p
                                style="font-weight: 400; font-size: 12px; text-align: center;  background-color: #E6E6E6; padding: 4px 0px; padding-left: 12px;">
                                {{studentLastName}}</p>
                        </div>
                    </div>
                </div>
                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; margin-left: 20px;">
                    <div>
                        <p style="font-weight: 400; text-align: center; font-size: 12px; margin-top: 5px;">TELEPHONE
                            (Home)
                        </p>
                        <div style="border-bottom: 1px solid black">
                            <p
                                style="font-weight: 400; font-size: 12px; text-align: center;  background-color: #E6E6E6; padding: 4px 0px; padding-left: 12px;">
                                {{emergencyContactPhoneHome}}</p>
                        </div>

                    </div>
                    <div>
                        <p style="font-weight: 400; text-align: center; font-size: 12px; margin-top: 5px;">TELEPHONE
                            (Mobile)
                        </p>
                        <div style="border-bottom: 1px solid black">
                            <p
                                style="font-weight: 400; font-size: 12px; text-align: center;  background-color: #E6E6E6; padding: 4px 0px; padding-left: 12px;">
                               {{emergencyContactPhoneMobile}}</p>
                        </div>
                    </div>
                    <div>
                        <p style="font-weight: 400; text-align: center; font-size: 12px; margin-top: 5px;">RELATIONSHIP
                            TO STUDENT
                        </p>
                        <div style="border-bottom: 1px solid black">
                            <p
                                style="font-weight: 400; font-size: 12px; text-align: center;  background-color: #E6E6E6; padding: 4px 0px; padding-left: 12px;">
                                {{emergencyContactRelation}}</p>
                        </div>
                    </div>
                </div>
            </div>
            <div style="position: fixed; bottom: 22px; left: 0; right: 0; display: flex; justify-content: center;">
                <p style="font-weight: 400; font-size: 12px; color: #BFBFBF;">     {{campusAddress}}| Tel:
        {{campusContactNumber}} | Email: {{campusEmail}}| Web:
        {{universityWebsite}}</p>
            </div>
           
</body>

</html>