<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        body {
            margin: 0;
            padding: 0;
        }
        p, h1, h2, ul, li {
            margin: 0;
        }

    </style>
</head>

<body>
    <div style="padding: 22px 63px;">
        <div style="display:flex; align-items:flex-end; gap:8px; margin-bottom:4px;">
          <span style="font-size:22px; font-weight:700; color:#1A64B7; letter-spacing:.5px;">IAU</span>
          <span style="font-size:22px; font-weight:700; color:#778699;">| AFFIDAVIT OF SUPPORT</span>
      </div>
        <div style="margin-top: 12px;">
            <p style="font-size:9px; weight: 400; line-height:1.45; text-align:justify; margin:6px 0 14px;">
              International F-1 applicants at {{universityName}} must show sufficient proof of financial stability before being admitted into the
university. If a student does not have sufficient funds of their own, they are permitted to have a sponsor who will attest to supporting them financially
for all tuition, living, and miscellaneous expenses incurred throughout their program at our university. This form is for IAU's internal use only and is not
permitted for use to show financial support at any government agency, diplomatic office, or for personal use. Once complete, please submit this form to:
<EMAIL>
          </p>
          <p style="font-size:9px; weight: 400; line-height:1.45; text-align:justify; margin:6px 0 14px;">
            The form <b>MUST</b> be properly completed, signed, and <b>MUST</b> be accompanied by the Sponsor's Bank Statement(s), Pay Stub(s), or any other
documentation(s) with an appropriate amount. IAU will <b>NOT</b> accept a typewritten name in place of a handwritten signature.
          </p>
        </div>
        <div style="background:#D3D3D3;
            color:#000;
            padding:5px 8px;
            font-family:'Times New Roman', Times, serif;
            font-weight:700;
            font-size:9px;
            line-height:1.2;
            text-transform:uppercase;
            margin:10px 0 8px;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;"
        >
          SECTION 1) STUDENT INFORMATION
        </div>
        <!-- Container -->
          <div style="width:100%; font-family:'Times New Roman', Times, serif; color:#000; font-size:7.5px;">

            <!-- ROW 1: LEGAL NAME -->
            <div style="display:grid; grid-template-columns: 120px 1fr; column-gap:12px; align-items:end;">
              <!-- Left label -->
              <div style="font-weight:700; text-transform:uppercase;">LEGAL NAME:</div>

              <!-- Three fields (First / Middle / Last) -->
              <div style="display:grid; grid-template-columns: 1fr 1fr 1fr; column-gap:16px; align-items:end;">
                <!-- First -->
                <div style="display:flex; flex-direction:column; gap:3px;">
                  <div style="border-bottom:1px solid #000; padding:2px 6px; text-align:center; font-style:italic;">{{studentFirstName}}</div>
                  <div style="font-size:9px; text-transform:uppercase; letter-spacing:.2px;">First Name</div>
                </div>
                <!-- Middle -->
                <div style="display:flex; flex-direction:column; gap:3px;">
                  <div style="border-bottom:1px solid #000; padding:2px 6px; text-align:center; font-style:italic;">{{studentMiddleName}}</div>
                  <div style="font-size:9px; text-transform:uppercase; letter-spacing:.2px;">Middle Name</div>
                </div>
                <!-- Last -->
                <div style="display:flex; flex-direction:column; gap:3px;">
                  <div style="border-bottom:1px solid #000; padding:2px 6px; text-align:center; font-style:italic;">{{studentLastName}}</div>
                  <div style="font-size:9px; text-transform:uppercase; letter-spacing:.2px;">Last Name</div>
                </div>
              </div>
            </div>

            <!-- Spacer -->
            <div style="height:10px;"></div>

            <!-- ROW 2: PROGRAM OF STUDY + DOB + PASSPORT -->
            <div style="display:grid; grid-template-columns: 120px 1fr auto; column-gap:12px; align-items:end;">
              <!-- Left label -->
              <div style="font-weight:700; text-transform:uppercase; line-height:1.1;">
                PROGRAM<br>OF STUDY:
              </div>

              <!-- Long underline value -->
              <div style="border-bottom:1px solid #000; padding:2px 6px; font-style:italic;">
                {{programName}}
              </div>

              <!-- Right side small fields -->
              <div style="display:grid; grid-auto-flow:column; align-items:end; column-gap:18px;">
                <!-- DOB -->
                <div style="display:grid; grid-template-columns:auto 120px; align-items:end; column-gap:6px;">
                  <div style="font-weight:700; font-size:7.5px; text-transform:uppercase; line-height:1;">
                    <span style="display:block;">DATE OF</span>
                    <span style="display:block;">BIRTH:</span>
                  </div>
                  <div style="padding:2px 6px; text-align:center; font-style:italic; border-bottom:1px solid #000;">{{dateOfBirth}}</div>
                </div>

                <!-- Passport -->
                <div style="display:grid; grid-template-columns:auto 140px; align-items:end; column-gap:6px;">
                  <div style="font-weight:700; font-size:7.5px; text-transform:uppercase; line-height:1;">
                    <span style="display:block;">PASSPORT</span>
                    <span style="display:block;">NUMBER:</span>
                  </div>
                  <div style="padding:2px 6px; text-align:center; font-style:italic; border-bottom:1px solid #000;">{{passport}}</div>
                </div>
              </div>
            </div>
          </div>
          <div style="background:#D3D3D3;
            color:#000;
            padding:5px 8px;
            font-family:'Times New Roman', Times, serif;
            font-weight:700;
            font-size:9px;
            line-height:1.2;
            margin:10px 0 8px;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;"
        >
          SECTION 2) SPONSOR INFORMATION: (<i>Please provide a photocopy of any form of government issued identification</i>).
        </div>
        <div style="display:grid; grid-template-columns: 120px 1fr; column-gap:12px; align-items:end; font-size: 7.5px;">
              <!-- Left label -->
              <div style="font-weight:700; text-transform:uppercase;">LEGAL NAME:</div>

              <!-- Three fields (First / Middle / Last) -->
              <div style="display:grid; grid-template-columns: 1fr 1fr 1fr; column-gap:16px; align-items:end;">
                <!-- First -->
                <div style="display:flex; flex-direction:column; gap:3px;">
                  <div style="border-bottom:1px solid #000; padding:2px 6px; text-align:center; font-style:italic;">{{sponsor}}</div>
                  <div style="font-size:9px; text-transform:uppercase; letter-spacing:.2px;">First Name</div>
                </div>
                <!-- Middle -->
                <div style="display:flex; flex-direction:column; gap:3px;">
                  <div style="border-bottom:1px solid #000; padding:2px 6px; text-align:center; font-style:italic;">N/A</div>
                  <div style="font-size:9px; text-transform:uppercase; letter-spacing:.2px;">Middle Name</div>
                </div>
                <!-- Last -->
                <div style="display:flex; flex-direction:column; gap:3px;">
                  <div style="border-bottom:1px solid #000; padding:2px 6px; text-align:center; font-style:italic;">N/A</div>
                  <div style="font-size:9px; text-transform:uppercase; letter-spacing:.2px;">Last Name</div>
                </div>
              </div>
            </div>
            <!-- Sponsor company line -->
            <div style="
              margin-top: 20px;
              width:100%;
              display:grid;
              grid-template-columns: 1fr 2fr;
              column-gap:24px;
              align-items:center;
              font-family:'Times New Roman', Times, serif;
              font-size:7.5px;
              color:#000;
            ">

              <!-- Left: checkbox + statement -->
              <div style="display:flex; align-items:flex-start; gap:8px;">
                <!-- custom square checkbox (print-safe) -->
                <div style="width:12px; height:12px; border:1px solid #000; margin-top:2px;"></div>
                <div style="line-height:1.25;">
                  I am a representative of the following<br/>
                  company who is providing financial support:
                </div>
              </div>

              <!-- Right: COMPANY NAME with underline -->
              <div style="display:grid; grid-template-columns:auto 1fr; column-gap:8px; align-items:end;">
                <div style="font-weight:700; text-transform:uppercase; font-size:11px; line-height:1;">
                  <div>COMPANY</div>
                  <div>NAME:</div>
                </div>
                <div style="border-bottom:1px solid #000; padding:3px 6px;">
                  N/A
                </div>
              </div>
            </div>
        <div style="display:grid; grid-template-columns: 120px 1fr; column-gap:12px; align-items:end; font-size: 7.5px; margin-top: 20px">
              <!-- Left label -->
              <div style="font-weight:700; text-transform:uppercase;">MAILING ADDRESS:</div>

              <!-- Three fields (First / Middle / Last) -->
              <div style="display:grid; grid-template-columns: 1fr 1fr 1fr; column-gap:16px; align-items:end;">
                <!-- First -->
                <div style="display:flex; flex-direction:column; gap:3px;">
                  <div style="border-bottom:1px solid #000; padding:2px 6px; text-align:center; font-style:italic;">N/A</div>
                  <div style="font-size:9px; text-transform:uppercase; letter-spacing:.2px;">STREET</div>
                </div>
                <!-- Middle -->
                <div style="display:flex; flex-direction:column; gap:3px;">
                  <div style="border-bottom:1px solid #000; padding:2px 6px; text-align:center; font-style:italic;">N/A</div>
                  <div style="font-size:9px; text-transform:uppercase; letter-spacing:.2px;">APT#</div>
                </div>
                <!-- Last -->
                <div style="display:flex; flex-direction:column; gap:3px;">
                  <div style="border-bottom:1px solid #000; padding:2px 6px; text-align:center; font-style:italic;">N/A</div>
                  <div style="font-size:9px; text-transform:uppercase; letter-spacing:.2px;">CITY</div>
                </div>
              </div>
            </div>
              <!-- Three fields (First / Middle / Last) -->
              <div style="display:grid; grid-template-columns: 1fr 1fr 1fr 1fr; column-gap:16px; align-items:end;">
                <!-- First -->
                <div style="display:flex; flex-direction:column; gap:3px;">
                  <div style="border-bottom:1px solid #000; padding:2px 6px; text-align:center; font-style:italic;">N/A</div>
                  <div style="font-size:9px; text-transform:uppercase; letter-spacing:.2px;">STATE</div>
                </div>
                <div style="display:flex; flex-direction:column; gap:3px;">
                  <div style="border-bottom:1px solid #000; padding:2px 6px; text-align:center; font-style:italic;">N/A</div>
                  <div style="font-size:9px; text-transform:uppercase; letter-spacing:.2px;">COUNTRY</div>
                </div>
                <!-- Middle -->
                <div style="display:flex; flex-direction:column; gap:3px;">
                  <div style="border-bottom:1px solid #000; padding:2px 6px; text-align:center; font-style:italic;">N/A</div>
                  <div style="font-size:9px; text-transform:uppercase; letter-spacing:.2px;">POSTAL CODE</div>
                </div>
                <!-- Last -->
                <div style="display:flex; flex-direction:column; gap:3px;">
                  <div style="border-bottom:1px solid #000; padding:2px 6px; text-align:center; font-style:italic;">{{sponsorPhone}}</div>
                  <div style="font-size:9px; text-transform:uppercase; letter-spacing:.2px;">TELEPHONE NUMBER</div>
                </div>
              </div>
            <div style="
              width:100%;
              display:grid;
              grid-template-columns: auto minmax(0,1fr) auto minmax(0,1fr);
              column-gap:12px;
              align-items:end;
              font-family:'Times New Roman', Times, serif;
              font-size:7.5px;
              margin-top:8px;
            ">
              <div style="font-weight:700; text-transform:uppercase;">RELATIONSHIP TO STUDENT:</div>
              <div style="border-bottom:1px solid #000; padding:2px 6px; text-align:center; font-style:italic;">{{sponsorRelation}}</div>

              <div style="font-weight:700; text-transform:uppercase; text-align:right;">DATE OF BIRTH:</div>
              <div style="border-bottom:1px solid #000; padding:2px 6px; text-align:center; font-style:italic;">N/A</div>
            </div>
        <div style="
          background:#D3D3D3; color:#000;
          padding:5px 8px; margin:10px 0 8px;
          font-family:'Times New Roman', Times, serif; font-size:9px; line-height:1.3;
          -webkit-print-color-adjust:exact; print-color-adjust:exact;
          display:flex; gap:6px; align-items:baseline; flex-wrap:wrap;
        ">
          <span style="font-weight:700; text-transform:uppercase;">
            SECTION 3) SPONSOR FINANCIAL INFORMATION:
          </span>
          <span style="font-style:italic;">
            Please note that sponsors can fill out <b>ITEM A</b>, <b>ITEM B</b>, or <b>both</b>. However,
            <span style="color:#FF4400; font-weight:700;">ITEM C (Monthly Support) is REQUIRED</span>.
          </span>
        </div>
        <div style="width:100%; font-family:'Times New Roman', Times, serif; color:#000; font-size:12px; -webkit-print-color-adjust:exact; print-color-adjust:exact;">

        <!-- ITEM A -->
        <div style="display:grid; grid-template-columns:auto 1fr; column-gap:12px; align-items:center; padding:6px 0;">
          <div style="display:flex; align-items:center; gap:6px; flex-direction: column;">
            <div style="width:12px;height:12px;border:1px solid #000; display:flex; align-items:center; justify-content:center; font-size:11px;">✓</div>
            <div style="font-weight:700; text-transform:uppercase; font-size:13px;">ITEM A)</div>
          </div>
          <div style="display: flex; grid-template-columns: auto 1fr; ">
            <div style='font-size: 8px; white-space: nowrap;'>TOTAL OF <span style="text-decoration:underline;">SAVINGS/CHECKINGS</span> :</div>
            
            <span style="font-weight:700;">&nbsp;USD$</span>
            <span style="display:inline-block; width: 100%; border-bottom:1px solid #000; padding:0 4px; font-style:italic; text-align:center;">N/A</span>
          </div>
        </div>

        <!-- ITEM B -->
        <div style="display:grid; grid-template-columns:auto 1fr; column-gap:12px; align-items:center; padding:6px 0;">
          <div style="display:flex; align-items:center; gap:6px; flex-direction: column;">
            <div style="width:12px;height:12px;border:1px solid #000; display:flex; align-items:center; justify-content:center; font-size:11px;"></div>
            <div style="font-weight:700; text-transform:uppercase; font-size:13px;">ITEM B)</div>
          </div>
          <div style="display: flex; grid-template-columns: auto 1fr; ">
            <div style='font-size: 8px; white-space: nowrap;'>TOTAL <span style="text-decoration:underline;">ANNUAL GROSS INCOME</span> :</div>
            
            <span style="font-weight:700;">&nbsp;USD$</span>
            <span style="display:inline-block; width: 100%; border-bottom:1px solid #000; padding:0 4px; font-style:italic; text-align:center;">N/A</span>
          </div>
        </div>

        <!-- ITEM C (red dashed box) -->
        <div style="display:grid; grid-template-columns:auto 1fr; column-gap:12px; align-items:start; margin-top:6px; border:1.5px dashed #FF4400">
          <div style="display:flex; align-items:flex-start; gap:6px; align-self: center;">
            <div style="font-weight:700; text-transform:uppercase; font-size:13px;">ITEM C)</div>
          </div>
          <div style="padding:6px 8px; display:grid; grid-template-columns:1fr auto; column-gap:16px; align-items:center;">
            <div style="font-size:11px; line-height:1.25;">
              I INTEND TO PROVIDE THE STUDENT <span style="text-decoration:underline;">MONTHLY SUPPORT</span><br/>IN THE AMOUNT OF:
            </div>
            <div style="white-space:nowrap;">
              <span style="font-weight:700;">USD$</span>
              <span style="display:inline-block; min-width:120px; border-bottom:1px solid #000; padding:0 4px; font-style:italic; text-align:center;">N/A</span>
            </div>
          </div>
        </div>

        <!-- Additional comments -->
        <div style="display:grid; grid-template-columns:80px 1fr; column-gap:2px; align-items:end; margin-top:10px;">
          <div style="font-weight:700; text-transform:uppercase; line-height:1.1;">ADDITIONAL<br/>COMMENTS:</div>
          <div style="border-bottom:1px solid #000; height:16px;"></div>
        </div>

        <!-- Note + evidence checkboxes -->
        <div style="font-size:11px; margin-top:10px;">
          Please ensure that the SPONSOR will submit a PHOTOCOPY OF ANY FORM OF GOVERNMENT ISSUED IDENTIFICATION and also, please
          check all that you will be submitting to demonstrate financial support:
        </div>

        <div style="display:flex; gap:24px; margin-top:8px; align-items:center; font-size:11px; justify-content: space-evenly;">
          <label style="display:flex; align-items:center; gap:6px;">
            <span style="width:12px; height:12px; border:1px solid #000; display:inline-block;"></span>
            U.S. Bank Statements
          </label>
          <label style="display:flex; align-items:center; gap:6px;">
            <span style="width:12px; height:12px; border:1px solid #000; display:inline-block;"></span>
            Foreign Bank Statements
          </label>
          <label style="display:flex; align-items:center; gap:6px;">
            <span style="width:12px; height:12px; border:1px solid #000; display:flex; align-items:center; justify-content:center; font-size:11px;">✓</span>
            Other:
          </label>
        </div>
      </div>
      <div style="background:#D3D3D3;
            color:#000;
            padding:5px 8px;
            font-family:'Times New Roman', Times, serif;
            font-weight:700;
            font-size:9px;
            line-height:1.2;
            margin:10px 0 8px;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;"
        >
          SECTION 4) CERTIFICATION.
        </div>
        <div style="margin-top: 12px;">
            <p style="font-size:9px; weight: 400; line-height:1.45; text-align:justify; margin:6px 0 14px;">
             By signing this document, I acknowledge that I am responsible for supporting the student named above for tuition, living expenses, and any other
miscellaneous fees incurred throughout their academic program with {{universityName}}. Once the student completes their program, this
contract will be nullified. If for any circumstance I am no longer able to support the student, it is my responsibility to inform the university of this
change to ensure the student can find sufficient alternatives to show financial capability.
            </p>
        </div>
        <div style="width:100%; display:grid; grid-template-columns:2fr 1fr; column-gap:16px; margin-top:16px; font-family:'Times New Roman', Times, serif; font-size:12px;">
          <!-- Signature -->
          <div style="text-align:center; padding:10px 6px;">
            <div style="display:inline-block; min-width:140px; padding:2px 12px; border-bottom:1px solid #000;">

              <!-- <img src="../ap-4/signature0.png" alt="" style=" height: 20px;" > -->
               {{sponsor}}
            </div>
            <div style="font-size:11px; margin-top:4px;">LIVE SIGNATURE OF SPONSOR</div>
          </div>
          <!-- Date -->
          <div style="text-align:center; padding:10px 6px;">
            <div style="display:inline-block; min-width:140px; padding:2px 12px; border-bottom:1px solid #000;">
              {{apDate}}
            </div>
            <div style="font-size:11px; margin-top:4px;">Date</div>
          </div>
        </div>
      <div style="
        color:#BFBFBF;
        font-family:'Times New Roman', Times, serif; font-weight:400; font-size:9px; line-height:1.3;
        padding:5px 8px;
        -webkit-print-color-adjust:exact; print-color-adjust:exact;">
        {{universityAddress}} | {{universityCity}}, {{universityState}} {{universityPostalCode}} | Tel: {{universityContact}} | Email: {{universityEmail}} | Web: {{universityWebsite}}
      </div>
    </div>
</body>

</html>

