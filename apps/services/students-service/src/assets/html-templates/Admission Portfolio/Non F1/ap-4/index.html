<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Times New Roman', Times, serif;
        }

        p,
        h1,
        h2,
        ul,
        li {
            margin: 0;
        }
    </style>
</head>

<body>
    <div style="padding: 14px 26px">
        <!-- Header Section -->
        <h2 style="color: #006BB6; font-size: 18px; font-weight: 700; margin-bottom: 12px;">{{universityName}} <span
                style="color: black">| ACKNOWLEDGMENTS</span></h2>
        <p style="font-size: 12px; margin-bottom: 20px; margin-left: 20px;">
            I hereby acknowledge the following statements are true and correct. I affirm that these statements will be
            considered as part of the admissions process and may influence a respective outcome. (NOTE: Answers should
            be Yes or No.)
        </p>

        <!-- Yes/No Headers -->
        <div style="display: flex; gap: 18px; margin-bottom: 10px; margin-left: 54px; font-size: 10px;">
            <p style="font-weight: 400;">Yes</p>
            <p style="font-weight: 400;">No</p>
        </div>

        <!-- Checklist Items -->
        <div style="display: flex; flex-direction: column; gap: 10px; margin-left: 34px;">
            <div style="display: flex; gap: 10px; align-items: flex-start; font-weight: 400; text-align: justify;">
                1.
                <svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="0.5" y="0.571777" width="17.0391" height="16.9805" fill="#F1F4FF" stroke="#565964" />
                    <g clip-path="url(#clip0_13169_1393)">
                        <path
                            d="M3.38656 9.44035C3.20707 8.41802 3.74087 7.72502 4.46352 7.15533C5.22051 6.55754 5.98686 6.87595 6.13046 7.80618C6.22098 8.39617 6.27405 8.99552 6.40984 9.57457C6.59401 10.3534 6.95768 10.4517 7.53986 9.9039C9.52989 8.03094 11.5121 6.15017 13.4974 4.27253C13.6488 4.12893 13.7831 3.94476 13.9641 3.86203C14.231 3.73873 14.5463 3.6045 14.8148 3.64508C14.9318 3.66225 15.0208 4.09459 15.0629 4.349C15.1737 5.01703 14.9131 5.51336 14.4246 5.99877C11.9101 8.48826 9.44404 11.0261 6.93115 13.5156C6.49724 13.9448 6.01964 14.5489 5.25484 14.1571C4.46196 13.7513 3.71589 13.3221 3.69404 12.2264C3.67531 11.2961 3.49582 10.369 3.38812 9.44035H3.38656Z"
                            fill="#565964" />
                    </g>
                    <defs>
                        <clipPath id="clip0_13169_1393">
                            <rect width="11.7372" height="10.6462" fill="white"
                                transform="translate(3.35156 3.63721)" />
                        </clipPath>
                    </defs>
                </svg>
                <svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="0.5" y="0.571777" width="17.0391" height="16.9805" fill="#F1F4FF" stroke="#565964" />
                </svg>
                <p style="font-size: 12px; font-weight: 400;"> I am currently authorized to study in the U.S. and will
                    reside within the State of California.</p>
            </div>
            <div style="display: flex; gap: 10px; align-items: flex-start;">
                2.
                <svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="0.5" y="0.571777" width="17.0391" height="16.9805" fill="#F1F4FF" stroke="#565964" />
                </svg>
                <svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="0.5" y="0.571777" width="17.0391" height="16.9805" fill="#F1F4FF" stroke="#565964" />
                    <g clip-path="url(#clip0_13169_1393)">
                        <path
                            d="M3.38656 9.44035C3.20707 8.41802 3.74087 7.72502 4.46352 7.15533C5.22051 6.55754 5.98686 6.87595 6.13046 7.80618C6.22098 8.39617 6.27405 8.99552 6.40984 9.57457C6.59401 10.3534 6.95768 10.4517 7.53986 9.9039C9.52989 8.03094 11.5121 6.15017 13.4974 4.27253C13.6488 4.12893 13.7831 3.94476 13.9641 3.86203C14.231 3.73873 14.5463 3.6045 14.8148 3.64508C14.9318 3.66225 15.0208 4.09459 15.0629 4.349C15.1737 5.01703 14.9131 5.51336 14.4246 5.99877C11.9101 8.48826 9.44404 11.0261 6.93115 13.5156C6.49724 13.9448 6.01964 14.5489 5.25484 14.1571C4.46196 13.7513 3.71589 13.3221 3.69404 12.2264C3.67531 11.2961 3.49582 10.369 3.38812 9.44035H3.38656Z"
                            fill="#565964" />
                    </g>
                    <defs>
                        <clipPath id="clip0_13169_1393">
                            <rect width="11.7372" height="10.6462" fill="white"
                                transform="translate(3.35156 3.63721)" />
                        </clipPath>
                    </defs>
                </svg>
                <p style="font-size: 12px;">I will be completing my program Online outside the U.S..</p>
            </div>
            <div style="display: flex; gap: 10px; align-items: flex-start;">
                3.
                <svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="0.5" y="0.571777" width="17.0391" height="16.9805" fill="#F1F4FF" stroke="#565964" />
                    <g clip-path="url(#clip0_13169_1393)">
                        <path
                            d="M3.38656 9.44035C3.20707 8.41802 3.74087 7.72502 4.46352 7.15533C5.22051 6.55754 5.98686 6.87595 6.13046 7.80618C6.22098 8.39617 6.27405 8.99552 6.40984 9.57457C6.59401 10.3534 6.95768 10.4517 7.53986 9.9039C9.52989 8.03094 11.5121 6.15017 13.4974 4.27253C13.6488 4.12893 13.7831 3.94476 13.9641 3.86203C14.231 3.73873 14.5463 3.6045 14.8148 3.64508C14.9318 3.66225 15.0208 4.09459 15.0629 4.349C15.1737 5.01703 14.9131 5.51336 14.4246 5.99877C11.9101 8.48826 9.44404 11.0261 6.93115 13.5156C6.49724 13.9448 6.01964 14.5489 5.25484 14.1571C4.46196 13.7513 3.71589 13.3221 3.69404 12.2264C3.67531 11.2961 3.49582 10.369 3.38812 9.44035H3.38656Z"
                            fill="#565964" />
                    </g>
                    <defs>
                        <clipPath id="clip0_13169_1393">
                            <rect width="11.7372" height="10.6462" fill="white"
                                transform="translate(3.35156 3.63721)" />
                        </clipPath>
                    </defs>
                </svg>
                <svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="0.5" y="0.571777" width="17.0391" height="16.9805" fill="#F1F4FF" stroke="#565964" />
                </svg>
                <p style="font-size: 12px;">If I have been referred by a member of the {{universityName}} community, I have provided
                    the referrer name on Page 3, Section 1, of the Application for Admission s Form.</p>
            </div>
            <div style="display: flex; gap: 10px; align-items: flex-start;">
                4.
                <svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="0.5" y="0.571777" width="17.0391" height="16.9805" fill="#F1F4FF" stroke="#565964" />
                </svg>
                <svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="0.5" y="0.571777" width="17.0391" height="16.9805" fill="#F1F4FF" stroke="#565964" />
                    <g clip-path="url(#clip0_13169_1393)">
                        <path
                            d="M3.38656 9.44035C3.20707 8.41802 3.74087 7.72502 4.46352 7.15533C5.22051 6.55754 5.98686 6.87595 6.13046 7.80618C6.22098 8.39617 6.27405 8.99552 6.40984 9.57457C6.59401 10.3534 6.95768 10.4517 7.53986 9.9039C9.52989 8.03094 11.5121 6.15017 13.4974 4.27253C13.6488 4.12893 13.7831 3.94476 13.9641 3.86203C14.231 3.73873 14.5463 3.6045 14.8148 3.64508C14.9318 3.66225 15.0208 4.09459 15.0629 4.349C15.1737 5.01703 14.9131 5.51336 14.4246 5.99877C11.9101 8.48826 9.44404 11.0261 6.93115 13.5156C6.49724 13.9448 6.01964 14.5489 5.25484 14.1571C4.46196 13.7513 3.71589 13.3221 3.69404 12.2264C3.67531 11.2961 3.49582 10.369 3.38812 9.44035H3.38656Z"
                            fill="#565964" />
                    </g>
                    <defs>
                        <clipPath id="clip0_13169_1393">
                            <rect width="11.7372" height="10.6462" fill="white"
                                transform="translate(3.35156 3.63721)" />
                        </clipPath>
                    </defs>
                </svg>
                <p style="font-size: 12px;">I would like Transfer Credit (TRC) evaluated..By checking YES in this box, I
                    understand that I must provide the appropriate transcript(s) or a Course-by-Course Foreign
                    Credential Evaluation. I also understand that TRC is awarded at the sole discretion of the Office of
                    Admissions.</p>
            </div>
            <div style="display: flex; gap: 10px; align-items: flex-start;">
                5.
                <svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="0.5" y="0.571777" width="17.0391" height="16.9805" fill="#F1F4FF" stroke="#565964" />
                </svg>
                <svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="0.5" y="0.571777" width="17.0391" height="16.9805" fill="#F1F4FF" stroke="#565964" />
                    <g clip-path="url(#clip0_13169_1393)">
                        <path
                            d="M3.38656 9.44035C3.20707 8.41802 3.74087 7.72502 4.46352 7.15533C5.22051 6.55754 5.98686 6.87595 6.13046 7.80618C6.22098 8.39617 6.27405 8.99552 6.40984 9.57457C6.59401 10.3534 6.95768 10.4517 7.53986 9.9039C9.52989 8.03094 11.5121 6.15017 13.4974 4.27253C13.6488 4.12893 13.7831 3.94476 13.9641 3.86203C14.231 3.73873 14.5463 3.6045 14.8148 3.64508C14.9318 3.66225 15.0208 4.09459 15.0629 4.349C15.1737 5.01703 14.9131 5.51336 14.4246 5.99877C11.9101 8.48826 9.44404 11.0261 6.93115 13.5156C6.49724 13.9448 6.01964 14.5489 5.25484 14.1571C4.46196 13.7513 3.71589 13.3221 3.69404 12.2264C3.67531 11.2961 3.49582 10.369 3.38812 9.44035H3.38656Z"
                            fill="#565964" />
                    </g>
                    <defs>
                        <clipPath id="clip0_13169_1393">
                            <rect width="11.7372" height="10.6462" fill="white"
                                transform="translate(3.35156 3.63721)" />
                        </clipPath>
                    </defs>
                </svg>
                <p style="font-size: 12px;">Are you a current Military Member in Active Duty or a Military Veteran?</p>
            </div>
        </div>
        <div style="margin-top: 20px;">
            <h2 style="color: #006BB6; font-size: 18px; font-weight: 700; margin-bottom: 12px;">{{universityName}} <span
                    style="color: black">| EMERGENCY CONTACT INFO</span></h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; margin-left: 20px;">

            </div>

            <!-- Legal Name Row -->
            <div style="display:flex; margin-bottom:8px; align-items:flex-start;">
                <div style="width:140px; font-weight:bold; font-size: 14px;">LEGAL NAME:</div>

                <!-- First Name -->
                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; width: 100%; gap: 10px;">
                    <div>
                        <div style="border-bottom: 1px solid black">
                            <p
                                style="font-weight: 400; font-size: 12px;  background-color: #E6E6E6; padding: 4px 0px; padding-left: 12px;">
                                {ABDUL HALIM}</p>
                        </div>
                        <p style="font-weight: 400; text-align: left; font-size: 12px; margin-top: 5px;">FIRST NAME
                        </p>
                    </div>
                    <div>
                        <div style="border-bottom: 1px solid black">
                            <p
                                style="font-weight: 400; font-size: 12px;  background-color: #E6E6E6; padding: 4px 0px; padding-left: 12px;">
                                {ABDUL HALIM}</p>
                        </div>
                        <p style="font-weight: 400; text-align: left; font-size: 12px; margin-top: 5px;">MIDDLE NAME
                        </p>
                    </div>
                    <div>
                        <div style="border-bottom: 1px solid black">
                            <p
                                style="font-weight: 400; font-size: 12px;  background-color: #E6E6E6; padding: 4px 0px; padding-left: 12px;">
                                {ABDUL HALIM}</p>
                        </div>
                        <p style="font-weight: 400; text-align: left; font-size: 12px; margin-top: 5px;">LAST NAME
                        </p>
                    </div>
                </div>
            </div>

            <!-- Telephone Row -->
            <div style="display:flex; margin-bottom:8px; align-items:flex-start;">
                <div style="width:142px; font-weight:bold; font-size: 14px;">TELEPHONE #1:</div>
                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; width: 100%; gap: 10px;">
                    <div>
                        <div style="border-bottom: 1px solid black">
                            <p
                                style="font-weight: 400; font-size: 12px;  background-color: #E6E6E6; padding: 4px 0px; padding-left: 12px;">
                                {}</p>
                        </div>
                        <p style="font-weight: 400; text-align: left; font-size: 12px; margin-top: 5px;">HOME
                        </p>
                    </div>
                    <div>
                        <div style="border-bottom: 1px solid black">
                            <p
                                style="font-weight: 400; font-size: 12px;  background-color: #E6E6E6; padding: 4px 0px; padding-left: 12px;">
                                {}</p>
                        </div>
                        <p style="font-weight: 400; text-align: left; font-size: 12px; margin-top: 5px;">MOBILE
                        </p>
                    </div>
                    <div>
                        <div style="border-bottom: 1px solid black">
                            <p
                                style="font-weight: 400; font-size: 12px;  background-color: #E6E6E6; padding: 4px 0px; padding-left: 12px;">
                                {}</p>
                        </div>
                        <p style="font-weight: 400; text-align: left; font-size: 12px; margin-top: 5px;">RELATIONSHIP TO
                            STUDENT
                        </p>
                    </div>
                </div>
            </div>
        </div>
        <div style="margin-top: 20px;">
            <h2 style="color: #006BB6; font-size: 18px; font-weight: 700; margin-bottom: 12px;">{{universityName}} <span
                    style="color: black">| CERTIFICATION</span></h2>
            <p style="font-size: 12px; font-weight: 400; color: black;">I hereby affirm that the information provided by me in this application is true and correct, and that
                there are no omissions or
                misstatements in my application. I consent to {{universityName}} taking one or more of the following actions upon
                discovery, at any time, of any
                such omission of misstatement of mine in this application: (1) Voiding of my admissions & registration
                to {{universityName}}; (2) Voiding of
                credits for course work completed at {{universityName}}; and (3) Distribution of information relating to such omissions
                and/or misstatements to
                other academic institutions, governmental agencies, and other third parties. I have received and read a
                copy of the university
                catalog, schedules of fees, School Performance Fact Sheet, institution’s cancellation and refund
                policies, and course descriptions.</p>
            <p style="margin-top: 12px; font-weight: 700; font-size: 14px;">I have read, understood, and will respect {{universityName}}'s mission and faith statement as published in the catalog
            </p>
            <p><a style="font-size: 12px; font-weight: 700; color: #1952BB;" href="">({{universityWebsite}}/downloads/)</a> and the website <a
                   style="font-size: 12px; font-weight: 700; color: #1952BB;" href="">({{universityWebsite}}/mission-objectives/)</a></p>
            <p style="margin-top: 12px; font-size: 12px; font-weight: 400;">By signing my name, I hereby apply for admissions to the academic program selected. I understand that electronically typing my
name in this document is considered to be the same legally-binding effect as signing my signature using pen and paper.</p>
        </div>
        <div style="display: flex; gap: 60px; margin-top: 60px;">
            <div style="width: 50%;">
                <div style="border-bottom: 1px solid black">
                    <p
                        style="font-weight: 400; font-size: 12px; padding: 4px 0px;">
                        {Sevinch Tursunkulova}</p>
                </div>
                <p style="font-weight: 700; text-align: left; font-size: 16px; margin-top: 5px;">Applicant Signature - Electronic Signature
                </p>
            </div>
             <div style="width: 50%;">
                <div style="border-bottom: 1px solid black">
                    <p
                        style="font-weight: 400; font-size: 12px; padding: 4px 0px;">
                        {6/23/2025}</p>
                </div>
                <p style="font-weight: 700; text-align: left; font-size: 16px; margin-top: 5px;">Date
                </p>
            </div>
        </div>
        <div style="position: fixed; bottom: 22px; left: 0; right: 0; display: flex; justify-content: center;">
            <p style="font-weight: 400; font-size: 12px; color: #BFBFBF;">{{universityAddress}} | {{universityCity}}
                {{universityCity}}, {{universityState}} {{universityPostalCode}} | Tel: {{universityContact}} | Fax: | ************ | Email: {{universityEmail}}| Web:
                {{universityWebsite}}</p>
        </div>
</body>

</html>