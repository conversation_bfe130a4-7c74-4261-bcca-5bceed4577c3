.new-program-6,
.new-program-6 * {
  box-sizing: border-box;
}
.new-program-6 {
  background: #ffffff;
  height: 792px;
  position: relative;
  overflow: hidden;
  width: 595px;
}
.group-34423 {
  position: absolute;
  inset: 0;
}
.program-completion-plan-pcp {
  color: #4a68b1;
  text-align: center;
  font-family: "ArialCe-Bold", sans-serif;
  font-size: 10.5600004196167px;
  font-weight: 700;
  position: absolute;
  left: 107.48px;
  top: 55.27px;
}
.bachelor-of-business-administration-b-b-a {
  color: #000000;
  text-align: center;
  font-family: "ArialCe-Bold", sans-serif;
  font-size: 10.5600004196167px;
  letter-spacing: 0.005em;
  font-weight: 700;
  position: absolute;
  left: 112.36px;
  top: 69.01px;
}
.iau {
  color: #578cc9;
  text-align: center;
  font-family: "ArialCe-Bold", sans-serif;
  font-size: 10.5600004196167px;
  font-weight: 700;
  position: absolute;
  left: 85.96px;
  top: 55.27px;
}
.frame-1171276311 {
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  width: 464px;
  position: absolute;
  left: 84px;
  top: 95px;
}
.container {
  display: flex;
  flex-direction: column;
  gap: 0px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.header {
  background: #bfbfbf;
  border-style: solid;
  border-color: #000000;
  border-width: 0.72px;
  padding: 10px 2px 10px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  height: 11px;
  position: relative;
}
.title {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Bold", sans-serif;
  font-size: 7.559999942779541px;
  letter-spacing: 0.002em;
  font-weight: 700;
  position: relative;
}
.name-fields {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.first-name-field {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0.72px;
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 156px;
  height: 10px;
  position: relative;
}
.first-name-label {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Regular", sans-serif;
  font-size: 6px;
  letter-spacing: 0.002em;
  font-weight: 400;
  position: relative;
}
.middle-name-field {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 85px;
  height: 10px;
  position: relative;
}
.middle-name-label {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Regular", sans-serif;
  font-size: 6px;
  letter-spacing: 0.002em;
  font-weight: 400;
  position: relative;
}
.last-name-field {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 223px;
  height: 10px;
  position: relative;
}
.last-name-label {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Regular", sans-serif;
  font-size: 6px;
  letter-spacing: 0.002em;
  font-weight: 400;
  position: relative;
}
.name-values {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.first-name-value {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0.72px;
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 156px;
  height: 10px;
  position: relative;
}
.first-name-text {
  color: #000000;
  text-align: left;
  font-family: "Arial-Bold", sans-serif;
  font-size: 6px;
  letter-spacing: 0.002em;
  font-weight: 700;
  position: relative;
}
.middle-name-value {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 85px;
  height: 10px;
  position: relative;
}
.last-name-value {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 223px;
  height: 10px;
  position: relative;
}
.last-name-text {
  color: #000000;
  text-align: left;
  font-family: "Arial-Bold", sans-serif;
  font-size: 6px;
  letter-spacing: 0.002em;
  font-weight: 700;
  position: relative;
}
.course-list {
  display: flex;
  flex-direction: column;
  gap: 0px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.general-education {
  display: flex;
  flex-direction: column;
  gap: 0px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.section-title {
  background: #bfbfbf;
  border-style: solid;
  border-color: #000000;
  border-width: 0.72px;
  padding: 10px 2px 10px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  height: 11px;
  position: relative;
}
.section-description {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Bold", sans-serif;
  font-size: 7.559999942779541px;
  letter-spacing: 0.002em;
  font-weight: 700;
  position: relative;
}
.section-title2 {
  background: #bfbfbf;
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0.72px;
  padding: 10px 2px 10px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  height: 11px;
  position: relative;
}
.table-header {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.header-column {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0.72px;
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 156px;
  height: 10px;
  position: relative;
}
.column-title {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Bold", sans-serif;
  font-size: 6px;
  letter-spacing: 0.002em;
  font-weight: 700;
  position: relative;
}
.header-column2 {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: center;
  flex-shrink: 0;
  width: 30px;
  height: 10px;
  position: relative;
}
.header-column3 {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: center;
  flex-shrink: 0;
  width: 55px;
  height: 10px;
  position: relative;
}
.header-column4 {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: center;
  flex-shrink: 0;
  width: 129px;
  height: 10px;
  position: relative;
}
.header-column5 {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: center;
  flex-shrink: 0;
  width: 45px;
  height: 10px;
  position: relative;
}
.header-column6 {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: center;
  flex-shrink: 0;
  width: 49px;
  height: 10px;
  position: relative;
}
.table-row {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.row-column {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0.72px;
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 12px;
  height: 10px;
  position: relative;
}
.row-number {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Regular", sans-serif;
  font-size: 6px;
  letter-spacing: 0.002em;
  font-weight: 400;
  position: relative;
}
.row-column2 {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0.5px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 144px;
  height: 10px;
  position: relative;
}
.course-title {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Regular", sans-serif;
  font-size: 6px;
  letter-spacing: 0.002em;
  font-weight: 400;
  position: relative;
}
.row-column3 {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0.5px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: center;
  flex-shrink: 0;
  width: 30px;
  height: 10px;
  position: relative;
}
.semester-hours {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Regular", sans-serif;
  font-size: 6px;
  letter-spacing: 0.002em;
  font-weight: 400;
  position: relative;
}
.row-column4 {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0.5px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: center;
  flex-shrink: 0;
  width: 55px;
  height: 10px;
  position: relative;
}
.row-column5 {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0.5px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: center;
  flex-shrink: 0;
  width: 129px;
  height: 10px;
  position: relative;
}
.row-column6 {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0.5px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: center;
  flex-shrink: 0;
  width: 45px;
  height: 10px;
  position: relative;
}
.row-column7 {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0.5px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: center;
  flex-shrink: 0;
  width: 49px;
  height: 10px;
  position: relative;
}
.credit {
  color: #000000;
  text-align: left;
  font-family: "Arial-Regular", sans-serif;
  font-size: 6px;
  letter-spacing: 0.002em;
  font-weight: 400;
  position: relative;
}
.credit2 {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Regular", sans-serif;
  font-size: 6px;
  letter-spacing: 0.002em;
  font-weight: 400;
  position: relative;
}
.social-sciences {
  display: flex;
  flex-direction: column;
  gap: 0px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.math-sciences {
  display: flex;
  flex-direction: column;
  gap: 0px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.religious-studies {
  display: flex;
  flex-direction: column;
  gap: 0px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.course-list2 {
  display: flex;
  flex-direction: column;
  gap: 0px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  height: 191px;
  position: relative;
}
.header-row {
  background: #bfbfbf;
  border-style: solid;
  border-color: #000000;
  border-width: 0.72px;
  padding: 10px 2px 10px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  height: 11px;
  position: relative;
}
.column-headers {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.course-code-and-title-header {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0.72px;
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 156px;
  height: 10px;
  position: relative;
}
.course-code-and-title-text {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Bold", sans-serif;
  font-size: 6px;
  letter-spacing: 0.002em;
  font-weight: 700;
  position: relative;
}
.semester-hours-header {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: center;
  flex-shrink: 0;
  width: 30px;
  height: 10px;
  position: relative;
}
.semester-hours-text {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Bold", sans-serif;
  font-size: 6px;
  letter-spacing: 0.002em;
  font-weight: 700;
  position: relative;
}
.source-header {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: center;
  flex-shrink: 0;
  width: 55px;
  height: 10px;
  position: relative;
}
.source-text {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Bold", sans-serif;
  font-size: 6px;
  letter-spacing: 0.002em;
  font-weight: 700;
  position: relative;
}
.course-description-header {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: center;
  flex-shrink: 0;
  width: 129px;
  height: 10px;
  position: relative;
}
.course-description-text {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Bold", sans-serif;
  font-size: 6px;
  letter-spacing: 0.002em;
  font-weight: 700;
  position: relative;
}
.semester-hours-header2 {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: center;
  flex-shrink: 0;
  width: 45px;
  height: 10px;
  position: relative;
}
.credit-header {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: center;
  flex-shrink: 0;
  width: 49px;
  height: 10px;
  position: relative;
}
.credit-text {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Bold", sans-serif;
  font-size: 6px;
  letter-spacing: 0.002em;
  font-weight: 700;
  position: relative;
}
.course-row {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.course-number {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0.72px;
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 12px;
  height: 10px;
  position: relative;
}
.course-number-text {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Regular", sans-serif;
  font-size: 6px;
  letter-spacing: 0.002em;
  font-weight: 400;
  position: relative;
}
.course-code-and-title {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0.5px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 144px;
  height: 10px;
  position: relative;
}
.course-code-and-title-text2 {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Regular", sans-serif;
  font-size: 6px;
  letter-spacing: 0.002em;
  font-weight: 400;
  position: relative;
}
.semester-hours2 {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0.5px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: center;
  flex-shrink: 0;
  width: 30px;
  height: 10px;
  position: relative;
}
.semester-hours-text2 {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Regular", sans-serif;
  font-size: 6px;
  letter-spacing: 0.002em;
  font-weight: 400;
  position: relative;
}
.source {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0.5px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: center;
  flex-shrink: 0;
  width: 55px;
  height: 10px;
  position: relative;
}
.course-description {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0.5px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: center;
  flex-shrink: 0;
  width: 129px;
  height: 10px;
  position: relative;
}
.semester-hours3 {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0.5px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: center;
  flex-shrink: 0;
  width: 45px;
  height: 10px;
  position: relative;
}
.credit3 {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0.5px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: center;
  flex-shrink: 0;
  width: 49px;
  height: 10px;
  position: relative;
}
.frame-1171276293 {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0.5px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: center;
  flex-shrink: 0;
  width: 55px;
  height: 10px;
  position: relative;
}
.frame-1171276294 {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0.5px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: center;
  flex-shrink: 0;
  width: 129px;
  height: 10px;
  position: relative;
}
.credit-text2 {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Regular", sans-serif;
  font-size: 6px;
  letter-spacing: 0.002em;
  font-weight: 400;
  position: relative;
}
.elective-component {
  display: flex;
  flex-direction: column;
  gap: 0px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.header-container {
  background: #bfbfbf;
  border-style: solid;
  border-color: #000000;
  border-width: 0.72px;
  padding: 10px 2px 10px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  height: 11px;
  position: relative;
}
.header-title {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Bold", sans-serif;
  font-size: 7.559999942779541px;
  letter-spacing: 0.002em;
  font-weight: 700;
  position: relative;
}
.course-code-and-title-header2 {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0.72px;
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 186px;
  height: 10px;
  position: relative;
}
.course-code-and-title-header-text {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Bold", sans-serif;
  font-size: 6px;
  letter-spacing: 0.002em;
  font-weight: 700;
  position: relative;
}
.source-header-text {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Bold", sans-serif;
  font-size: 6px;
  letter-spacing: 0.002em;
  font-weight: 700;
  position: relative;
}
.course-description-header-text {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Bold", sans-serif;
  font-size: 6px;
  letter-spacing: 0.002em;
  font-weight: 700;
  position: relative;
}
.semester-hours-header-text {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Bold", sans-serif;
  font-size: 6px;
  letter-spacing: 0.002em;
  font-weight: 700;
  position: relative;
}
.credit-header-text {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Bold", sans-serif;
  font-size: 6px;
  letter-spacing: 0.002em;
  font-weight: 700;
  position: relative;
}
.course-number-container {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0.72px;
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 12px;
  height: 10px;
  position: relative;
}
.course-number2 {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Regular", sans-serif;
  font-size: 6px;
  letter-spacing: 0.002em;
  font-weight: 400;
  position: relative;
}
.course-code-and-title2 {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0.5px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 174px;
  height: 10px;
  position: relative;
}
.source-container {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0.5px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: center;
  flex-shrink: 0;
  width: 55px;
  height: 10px;
  position: relative;
}
.course-description-container {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0.5px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: center;
  flex-shrink: 0;
  width: 129px;
  height: 10px;
  position: relative;
}
.semester-hours-container {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0.5px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: center;
  flex-shrink: 0;
  width: 45px;
  height: 10px;
  position: relative;
}
.credit-container {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0.5px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: center;
  flex-shrink: 0;
  width: 49px;
  height: 10px;
  position: relative;
}
