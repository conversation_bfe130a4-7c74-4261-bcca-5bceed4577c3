.new-program-4,
.new-program-4 * {
  box-sizing: border-box;
}
.new-program-4 {
  background: #ffffff;
  height: 792px;
  position: relative;
  overflow: hidden;
  width: 595px;
}
.border {
  position: absolute;
  inset: 0;
}
.middle-border {
  border-style: solid;
  border-color: #000000;
  border-width: 2.8px;
  width: 561.06px;
  height: 741.05px;
  position: absolute;
  left: 25.45px;
  top: 25.48px;
}
.outer-border {
  border-style: solid;
  border-color: #000000;
  border-width: 0.5px;
  width: 563.97px;
  height: 743.98px;
  position: absolute;
  left: 24.04px;
  top: 24.02px;
}
.inner-border {
  border-style: solid;
  border-color: #000000;
  border-width: 0.5px;
  width: 553.42px;
  height: 733.46px;
  position: absolute;
  left: 29.29px;
  top: 29.3px;
}
.greeting-text {
  color: #000000;
  text-align: center;
  font-family: "-", sans-serif;
  font-size: 7px;
  line-height: 9.5px;
  letter-spacing: 0undefined;
  font-weight: 400;
  position: absolute;
  left: 50%;
  translate: -50%;
  top: 747.32px;
  width: 508.55px;
  height: 9.99px;
}
.greeting-text-span {
  font-family: "TwCenMt-Regular", sans-serif;
  letter-spacing: -0.011899999999999999em;
}
.greeting-text-span2 {
  font-family: "TwCenMt-Regular", sans-serif;
  letter-spacing: -0.0219em;
}
.greeting-text-span3 {
  font-family: "TwCenMt-Bold", sans-serif;
  letter-spacing: 0.008100000000000001em;
  font-weight: 700;
}
.greeting-text-span4 {
  font-family: "TwCenMt-Regular", sans-serif;
  letter-spacing: 0.008100000000000001em;
}
.greeting-container {
  background: #000000;
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 53px;
  top: 43px;
}
.greeting-text2 {
  color: #ffffff;
  text-align: left;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 9px;
  line-height: 8.5px;
  letter-spacing: -0.0219em;
  font-weight: 700;
  position: relative;
  flex: 1;
}
.greeting-container2 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 53px;
  top: 59px;
}
.greeting-text3 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 9px;
  line-height: 9.5px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.greeting-container3 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 53px;
  top: 142px;
}
.greeting-text4 {
  color: #000000;
  text-align: left;
  font-family: "-", sans-serif;
  font-size: 9px;
  line-height: 9.5px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.greeting-text-4-span {
  font-family: "TwCenMt-Regular", sans-serif;
}
.greeting-text-4-span2 {
  font-family: "TwCenMt-Bold", sans-serif;
  font-weight: 700;
}
.greeting-container4 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 53px;
  top: 180px;
}
.greeting-container5 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 52.98px;
  top: 355px;
}
.steps-list-container {
  display: flex;
  flex-direction: column;
  gap: 0px;
  align-items: flex-start;
  justify-content: flex-start;
  width: 488px;
  position: absolute;
  left: 72px;
  top: 208px;
}
.frame {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.step-number {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 9px;
  line-height: 9.3px;
  font-weight: 400;
  position: relative;
}
.step-instruction {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 9px;
  line-height: 9.3px;
  letter-spacing: -0.011899999999999999em;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.step-instruction2 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 9px;
  line-height: 9.3px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.frame2 {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  flex-shrink: 0;
  position: relative;
}
.step-instruction3 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 9px;
  line-height: 9.3px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
  width: 471px;
}
.greeting-container6 {
  background: #000000;
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 52.98px;
  top: 566px;
}
.greeting-container7 {
  background: #000000;
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 52.98px;
  top: 420px;
}
.greeting-container8 {
  background: #000000;
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 52.98px;
  top: 501px;
}
.greeting-container9 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 52.98px;
  top: 435px;
}
.greeting-text-3-span {
}
.greeting-text-3-span2 {
  text-decoration: underline;
}
.greeting-container10 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 52.98px;
  top: 582px;
}
.greeting-container11 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 52.98px;
  top: 519px;
}
.greeting-container12 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 52.98px;
  top: 619px;
}
.greeting-text-4-span2 {
  font-family: "TwCenMt-Bold", sans-serif;
  font-weight: 700;
}
.greeting-text-4-span {
  font-family: "TwCenMt-Regular", sans-serif;
}
.greeting-container13 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 52.98px;
  top: 454px;
}
