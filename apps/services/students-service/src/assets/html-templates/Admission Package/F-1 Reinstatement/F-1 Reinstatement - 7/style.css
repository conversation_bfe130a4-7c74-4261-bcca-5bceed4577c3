.new-program-7,
.new-program-7 * {
  box-sizing: border-box;
}
.new-program-7 {
  background: #ffffff;
  height: 792px;
  position: relative;
  overflow: hidden;
  width: 595px;
}
.enrollment-instructions {
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  width: 464px;
  position: absolute;
  left: 84px;
  top: 186px;
}
.enrollment-instructions-text {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Italic", sans-serif;
  font-size: 6px;
  line-height: 8px;
  letter-spacing: 0.002em;
  font-weight: 400;
  font-style: italic;
  position: relative;
}
.conditional-credits {
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  width: 464px;
  position: absolute;
  left: 84px;
  top: 206px;
}
.conditional-credits-text {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Italic", sans-serif;
  font-size: 6px;
  line-height: 8px;
  letter-spacing: 0.002em;
  font-weight: 400;
  font-style: italic;
  position: relative;
}
.evaluator {
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  width: 464px;
  position: absolute;
  left: 84px;
  top: 258.18px;
}
.evaluator-text {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Regular", sans-serif;
  font-size: 6px;
  line-height: 8px;
  letter-spacing: 0.002em;
  font-weight: 400;
  position: relative;
}
.evaluator-name {
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  width: 464px;
  position: absolute;
  left: 84px;
  top: 298.84px;
}
.evaluator-name-text {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Regular", sans-serif;
  font-size: 6px;
  line-height: 8px;
  letter-spacing: 0.002em;
  font-weight: 400;
  position: relative;
}
.director {
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  width: 464px;
  position: absolute;
  left: 84px;
  top: 310.84px;
}
.director-text {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Regular", sans-serif;
  font-size: 6px;
  line-height: 8px;
  letter-spacing: 0.002em;
  font-weight: 400;
  position: relative;
}
.office {
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  width: 464px;
  position: absolute;
  left: 84px;
  top: 319.92px;
}
.office-text {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Regular", sans-serif;
  font-size: 6px;
  line-height: 8px;
  letter-spacing: 0.002em;
  font-weight: 400;
  position: relative;
}
.report-end {
  padding: 0px 119px 0px 119px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  width: 464px;
  position: absolute;
  left: 85px;
  top: 227px;
}
.report-end-text {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-BoldItalic", sans-serif;
  font-size: 6px;
  line-height: 8px;
  letter-spacing: 0.002em;
  font-weight: 700;
  font-style: italic;
  position: relative;
}
.date {
  padding: 0px 169px 0px 169px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  width: 464px;
  position: absolute;
  left: 85px;
  top: 237.18px;
}
.date-text {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Regular", sans-serif;
  font-size: 6px;
  line-height: 8px;
  letter-spacing: 0.002em;
  font-weight: 400;
  position: relative;
}
.vector-157 {
  width: 464px;
  height: 0px;
  position: absolute;
  left: 84px;
  top: 236.09px;
  overflow: visible;
}
.signature {
  width: 15.7%;
  height: 2.19%;
  position: absolute;
  right: 71.1%;
  left: 13.2%;
  bottom: 63.24%;
  top: 34.58%;
  object-fit: cover;
}
.frame-1171276426 {
  display: flex;
  flex-direction: column;
  gap: 0px;
  align-items: flex-start;
  justify-content: flex-start;
  width: 464px;
  position: absolute;
  left: 84px;
  top: 110px;
}
.container {
  background: #bfbfbf;
  border-style: solid;
  border-color: #000000;
  border-width: 0.72px 0.72px 0px 0.72px;
  padding: 10px 2px 10px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  height: 11px;
  position: relative;
}
.text {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Bold", sans-serif;
  font-size: 7.559999942779541px;
  letter-spacing: 0.002em;
  font-weight: 700;
  position: relative;
}
.course-list {
  display: flex;
  flex-direction: column;
  gap: 0px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.course-list-header {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.course-code-title {
  border-style: solid;
  border-color: #000000;
  border-width: 0.72px;
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 186px;
  height: 10px;
  position: relative;
}
.course-code-title-text {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Bold", sans-serif;
  font-size: 6px;
  letter-spacing: 0.002em;
  font-weight: 700;
  position: relative;
}
.source-header {
  border-style: solid;
  border-color: #000000;
  border-width: 0.72px 0.72px 0.72px 0px;
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: center;
  flex-shrink: 0;
  width: 55px;
  height: 10px;
  position: relative;
}
.source-header-text {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Bold", sans-serif;
  font-size: 6px;
  letter-spacing: 0.002em;
  font-weight: 700;
  position: relative;
}
.description-header {
  border-style: solid;
  border-color: #000000;
  border-width: 0.72px 0.72px 0.72px 0px;
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: center;
  flex-shrink: 0;
  width: 129px;
  height: 10px;
  position: relative;
}
.description-header-text {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Bold", sans-serif;
  font-size: 6px;
  letter-spacing: 0.002em;
  font-weight: 700;
  position: relative;
}
.hours-header {
  border-style: solid;
  border-color: #000000;
  border-width: 0.72px 0.72px 0.72px 0px;
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: center;
  flex-shrink: 0;
  width: 45px;
  height: 10px;
  position: relative;
}
.hours-header-text {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Bold", sans-serif;
  font-size: 6px;
  letter-spacing: 0.002em;
  font-weight: 700;
  position: relative;
}
.credit-header {
  border-style: solid;
  border-color: #000000;
  border-width: 0.72px 0.72px 0.72px 0px;
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: center;
  flex-shrink: 0;
  width: 49px;
  height: 10px;
  position: relative;
}
.credit-header-text {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Bold", sans-serif;
  font-size: 6px;
  letter-spacing: 0.002em;
  font-weight: 700;
  position: relative;
}
.course-row {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.course-number {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0.72px;
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 12px;
  height: 10px;
  position: relative;
}
.course-number-text {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Regular", sans-serif;
  font-size: 6px;
  letter-spacing: 0.002em;
  font-weight: 400;
  position: relative;
}
.course-title {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0.5px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 174px;
  height: 10px;
  position: relative;
}
.course-title-text {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Regular", sans-serif;
  font-size: 6px;
  letter-spacing: 0.002em;
  font-weight: 400;
  position: relative;
}
.course-source {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0.5px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: center;
  flex-shrink: 0;
  width: 55px;
  height: 10px;
  position: relative;
}
.course-description {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0.5px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: center;
  flex-shrink: 0;
  width: 129px;
  height: 10px;
  position: relative;
}
.course-hours {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0.5px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: center;
  flex-shrink: 0;
  width: 45px;
  height: 10px;
  position: relative;
}
.course-hours-text {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Regular", sans-serif;
  font-size: 6px;
  letter-spacing: 0.002em;
  font-weight: 400;
  position: relative;
}
.course-credit {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0.5px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: center;
  flex-shrink: 0;
  width: 49px;
  height: 10px;
  position: relative;
}
.course-credit-text {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Regular", sans-serif;
  font-size: 6px;
  letter-spacing: 0.002em;
  font-weight: 400;
  position: relative;
}
.notes {
  background: #bfbfbf;
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0.72px;
  padding: 10px 2px 10px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  height: 11px;
  position: relative;
}
.notes-text {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Bold", sans-serif;
  font-size: 7.559999942779541px;
  letter-spacing: 0.002em;
  font-weight: 700;
  position: relative;
}
.notes-row {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.notes-row2 {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0.72px;
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  flex: 1;
  height: 10px;
  position: relative;
}
