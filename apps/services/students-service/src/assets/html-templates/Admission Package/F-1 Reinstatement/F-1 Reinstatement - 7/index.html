<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="stylesheet" href="./vars.css">
  <link rel="stylesheet" href="./style.css">
  
  
  <style>
   a,
   button,
   input,
   select,
   h1,
   h2,
   h3,
   h4,
   h5,
   * {
       box-sizing: border-box;
       margin: 0;
       padding: 0;
       border: none;
       text-decoration: none;
       background: none;
   
       -webkit-font-smoothing: antialiased;
   }
   
   menu, ol, ul {
       list-style-type: none;
       margin: 0;
       padding: 0;
   }
   body {
    display: flex;
    justify-content: center;
   }
   </style>
  <title>Document</title>
</head>
<body>
  <div class="new-program-7">
    <div class="enrollment-instructions">
      <div class="enrollment-instructions-text">
        When enrolling in courses throughout your entire program, only refer to
        this PCP report for required courses. Do not refer to the current website
        or catalog, as those sources
        <br />
        may not reflect your custom degree plan at the time of enrollment.
      </div>
    </div>
    <div class="conditional-credits">
      <div class="conditional-credits-text">
        The award of the above transfer credits is CONDITIONAL upon receipt and
        review of the official school transcript(s)
      </div>
    </div>
    <div class="evaluator">
      <div class="evaluator-text">Evaluated by:</div>
    </div>
    <div class="evaluator-name">
      <div class="evaluator-name-text">{{shortName}}</div>
    </div>
    <div class="director">
      <div class="director-text">Associate Director</div>
    </div>
    <div class="office">
      <div class="office-text">Office of Admissions</div>
    </div>
    <div class="report-end">
      <div class="report-end-text">End of PCP Report for {{studentName}}</div>
    </div>
    <div class="date">
      <div class="date-text">Date: {{issueDate}}</div>
    </div>
    <svg
      class="vector-157"
      width="464"
      height="2"
      viewBox="0 0 464 2"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M0 1.15967H464" stroke="black" />
    </svg>
    <img class="signature" src="signature0.png" />
    <div class="frame-1171276426">
      <div class="container">
        <div class="text">INTERNSHIP COMPONENT | (2 Semester Hours)</div>
      </div>
      <div class="course-list">
        <div class="course-list-header">
          <div class="course-code-title">
            <div class="course-code-title-text">Course Code and Course Title</div>
          </div>
          <div class="source-header">
            <div class="source-header-text">Source</div>
          </div>
          <div class="description-header">
            <div class="description-header-text">Course Description</div>
          </div>
          <div class="hours-header">
            <div class="hours-header-text">Sem. Hrs.</div>
          </div>
          <div class="credit-header">
            <div class="credit-header-text">Credit</div>
          </div>
        </div>
        <div class="course-row">
          <div class="course-number">
            <div class="course-number-text">1</div>
          </div>
          <div class="course-title">
            <div class="course-title-text">Internship I</div>
          </div>
          <div class="course-source"></div>
          <div class="course-description"></div>
          <div class="course-hours">
            <div class="course-hours-text">1.0</div>
          </div>
          <div class="course-credit">
            <div class="course-credit-text">To Do</div>
          </div>
        </div>
        <div class="course-row">
          <div class="course-number">
            <div class="course-number-text">2</div>
          </div>
          <div class="course-title">
            <div class="course-title-text">Internship II</div>
          </div>
          <div class="course-source"></div>
          <div class="course-description"></div>
          <div class="course-hours">
            <div class="course-hours-text">1.0</div>
          </div>
          <div class="course-credit">
            <div class="course-credit-text">To Do</div>
          </div>
        </div>
        <div class="notes">
          <div class="notes-text">NOTES</div>
        </div>
        <div class="notes-row">
          <div class="notes-row2"></div>
        </div>
      </div>
    </div>
  </div>
  
</body>
</html>