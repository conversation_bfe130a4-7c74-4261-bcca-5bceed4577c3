.new-program-3,
.new-program-3 * {
  box-sizing: border-box;
}
.new-program-3 {
  background: #ffffff;
  height: 792px;
  position: relative;
  overflow: hidden;
  width: 595px;
}
.border {
  position: absolute;
  inset: 0;
}
.middle-border {
  border-style: solid;
  border-color: #000000;
  border-width: 2.8px;
  width: 561.06px;
  height: 741.05px;
  position: absolute;
  left: 25.45px;
  top: 25.48px;
}
.outer-border {
  border-style: solid;
  border-color: #000000;
  border-width: 0.5px;
  width: 563.97px;
  height: 743.98px;
  position: absolute;
  left: 24.04px;
  top: 24.02px;
}
.inner-border {
  border-style: solid;
  border-color: #000000;
  border-width: 0.5px;
  width: 553.42px;
  height: 733.46px;
  position: absolute;
  left: 29.29px;
  top: 29.3px;
}
.greeting-text {
  color: #000000;
  text-align: center;
  font-family: "-", sans-serif;
  font-size: 7px;
  line-height: 9.5px;
  letter-spacing: 0undefined;
  font-weight: 400;
  position: absolute;
  left: 50%;
  translate: -50%;
  top: 750.19px;
  width: 508.55px;
  height: 9.99px;
}
.greeting-text-span {
  font-family: "TwCenMt-Regular", sans-serif;
  letter-spacing: -0.011899999999999999em;
}
.greeting-text-span2 {
  font-family: "TwCenMt-Regular", sans-serif;
  letter-spacing: -0.0219em;
}
.greeting-text-span3 {
  font-family: "TwCenMt-Bold", sans-serif;
  letter-spacing: 0.008100000000000001em;
  font-weight: 700;
}
.greeting-text-span4 {
  font-family: "TwCenMt-Regular", sans-serif;
  letter-spacing: 0.008100000000000001em;
}
.container {
  display: flex;
  flex-direction: column;
  gap: 11px;
  align-items: flex-start;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 52.98px;
  top: 48px;
}
.greeting-container {
  background: #000000;
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.greeting-text2 {
  color: #ffffff;
  text-align: left;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 9px;
  line-height: 8.5px;
  letter-spacing: -0.0219em;
  font-weight: 700;
  position: relative;
  flex: 1;
}
.greeting-container2 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.greeting-text3 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 9px;
  line-height: 9.5px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.container2 {
  display: flex;
  flex-direction: column;
  gap: 9px;
  align-items: flex-start;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 52.98px;
  top: 136px;
}
.container3 {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.greeting-text4 {
  color: #000000;
  text-align: left;
  font-family: "-", sans-serif;
  font-size: 9px;
  line-height: 9.5px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.greeting-text-4-span {
  font-family: "TwCenMt-Bold", sans-serif;
  font-weight: 700;
}
.greeting-text-4-span2 {
  font-family: "TwCenMt-Regular", sans-serif;
}
.greeting-text-4-span {
  font-family: "TwCenMt-Bold", sans-serif;
  font-weight: 700;
}
.greeting-text-4-span2 {
  font-family: "TwCenMt-Regular", sans-serif;
}
.greeting-text5 {
  text-align: left;
  font-family: "-", sans-serif;
  font-size: 9px;
  line-height: 9.5px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.greeting-text-5-span {
  color: #000000;
  font-family: "TwCenMt-Bold", sans-serif;
  font-weight: 700;
}
.greeting-text-5-span2 {
  color: #000000;
  font-family: "TwCenMt-Regular", sans-serif;
}
.greeting-text-5-span3 {
  color: #003eff;
  font-family: "TwCenMt-Regular", sans-serif;
  text-decoration: underline;
}
.greeting-container3 {
  background: #000000;
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 506px;
  position: absolute;
  left: 52.98px;
  top: 478px;
}
.greeting-container4 {
  background: #000000;
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 53px;
  top: 340px;
}
.greeting-container5 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 510px;
  position: absolute;
  left: 53px;
  top: 358px;
}
.greeting-container6 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 52.98px;
  top: 496px;
}
.greeting-container7 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 52.98px;
  top: 576px;
}
.group-34447 {
  position: absolute;
  inset: 0;
}
.group-34420 {
  width: 58px;
  height: 84.99px;
  position: static;
}
.greeting-container8 {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 80.98px;
  top: 385.01px;
}
.greeting-text6 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 9px;
  line-height: 9.5px;
  letter-spacing: -0.0219em;
  font-weight: 700;
  position: relative;
}
.group-34418 {
  width: 7px;
  height: 75.72px;
  position: static;
}
.greeting-container9 {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 106.98px;
  top: 394.28px;
}
.greeting-text7 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 9px;
  line-height: 9.4px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
}
.greeting-container10 {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 106.98px;
  top: 405px;
}
.greeting-text8 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 9px;
  line-height: 9.3px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
}
.greeting-container11 {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 106.98px;
  top: 414px;
}
.greeting-container12 {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 106.98px;
  top: 423.14px;
}
.greeting-container13 {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 106.98px;
  top: 432.28px;
}
.greeting-container14 {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 106.98px;
  top: 442px;
}
.greeting-container15 {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 106.98px;
  top: 450px;
}
.greeting-container16 {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 106.98px;
  top: 460px;
}
.group-34421 {
  width: 109px;
  height: 85px;
  position: static;
}
.group-34419 {
  width: 38px;
  height: 76px;
  position: static;
}
.greeting-container17 {
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: center;
  position: absolute;
  left: 248.98px;
  top: 394px;
}
.greeting-container18 {
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 248.98px;
  top: 404.98px;
}
.greeting-container19 {
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 248.98px;
  top: 413.99px;
}
.greeting-container20 {
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 248.98px;
  top: 422.99px;
}
.greeting-container21 {
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 248.98px;
  top: 432px;
}
.greeting-container22 {
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 248.98px;
  top: 442px;
}
.greeting-container23 {
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 248.98px;
  top: 450.98px;
}
.greeting-container24 {
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 247px;
  top: 460px;
}
.greeting-container25 {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 212px;
  top: 385px;
}
.group-34422 {
  width: 58px;
  height: 85.01px;
  position: static;
}
.group-344192 {
  width: 38px;
  height: 76.01px;
  position: static;
}
.greeting-container26 {
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: center;
  position: absolute;
  left: 425px;
  top: 394px;
}
.greeting-container27 {
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: center;
  position: absolute;
  left: 427px;
  top: 404.98px;
}
.greeting-container28 {
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 427px;
  top: 413.99px;
}
.greeting-container29 {
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 427px;
  top: 423px;
}
.greeting-container30 {
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 427px;
  top: 432px;
}
.greeting-container31 {
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 429px;
  top: 442px;
}
.greeting-container32 {
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 429px;
  top: 451px;
}
.greeting-container33 {
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 429px;
  top: 460.01px;
}
.greeting-container34 {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 414.3px;
  top: 385px;
}
.steps-list-container {
  display: flex;
  flex-direction: column;
  gap: 0px;
  align-items: flex-start;
  justify-content: flex-start;
  width: 492px;
  position: absolute;
  left: 67.98px;
  top: 517.99px;
}
.frame {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.fiber-manual-record {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 12px;
  height: 8px;
  position: relative;
  overflow: visible;
}
.step-instruction {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 9px;
  line-height: 9.1px;
  letter-spacing: -0.011899999999999999em;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.fiber-manual-record2 {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 12px;
  height: 8px;
  position: relative;
  overflow: visible;
}
.the-student-paid-125-for-the-non-refundable-application-for-admissions-fee-plus-675-for-the-course-therefore-the-student-paid-800-total-to-iau {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 9px;
  line-height: 9.1px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.fiber-manual-record3 {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 12px;
  height: 8px;
  position: relative;
  overflow: visible;
}
.the-3-unit-course-spans-8-lessons-weeks-the-student-attends-2-classes-and-withdraws-during-the-2nd-week {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 9px;
  line-height: 9.1px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.the-3-unit-course-spans-8-lessons-weeks-the-student-attends-2-classes-and-withdraws-during-the-2nd-week-span {
}
.fiber-manual-record4 {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 12px;
  height: 8px;
  position: relative;
  overflow: visible;
}
.the-student-is-entitled-to-a-pro-rata-refund-of-87-50-of-his-her-675-tuition-paid-which-is-590-63 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 9px;
  line-height: 9.1px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.steps-list-container2 {
  display: flex;
  flex-direction: column;
  gap: 0px;
  align-items: flex-start;
  justify-content: flex-start;
  width: 492px;
  position: absolute;
  left: 67.98px;
  top: 597px;
}
.fiber-manual-record5 {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 12px;
  height: 8px;
  position: relative;
  overflow: visible;
}
.the-non-refundable-application-for-admissions-fee-costs-125-while-a-3-unit-master-s-course-costs-975 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 9px;
  line-height: 9.1px;
  letter-spacing: -0.011899999999999999em;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.fiber-manual-record6 {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 12px;
  height: 8px;
  position: relative;
  overflow: visible;
}
.the-student-paid-125-for-the-non-refundable-application-for-admissions-fee-plus-975-for-the-course-therefore-the-student-paid-1-100-total-to-iau {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 9px;
  line-height: 9.1px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.fiber-manual-record7 {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 12px;
  height: 8px;
  position: relative;
  overflow: visible;
}
.the-3-unit-course-spans-8-lessons-weeks-the-student-attends-4-weeks-and-withdraws-during-the-4th-week {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 9px;
  line-height: 9.1px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.the-3-unit-course-spans-8-lessons-weeks-the-student-attends-4-weeks-and-withdraws-during-the-4th-week-span {
}
.fiber-manual-record8 {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 12px;
  height: 8px;
  position: relative;
  overflow: visible;
}
.the-student-is-entitled-to-a-pro-rata-refund-of-62-5-of-his-her-975-tuition-paid-which-is-609-38 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 9px;
  line-height: 9.1px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
  flex: 1;
}
