.new-program-2,
.new-program-2 * {
  box-sizing: border-box;
}
.new-program-2 {
  background: #ffffff;
  height: 792px;
  position: relative;
  overflow: hidden;
  width: 595px;
}
.border {
  position: absolute;
  inset: 0;
}
.middle-border {
  border-style: solid;
  border-color: #000000;
  border-width: 2.8px;
  width: 561.06px;
  height: 741.05px;
  position: absolute;
  left: 25.45px;
  top: 25.48px;
}
.outer-border {
  border-style: solid;
  border-color: #000000;
  border-width: 0.5px;
  width: 563.97px;
  height: 743.98px;
  position: absolute;
  left: 24.04px;
  top: 24.02px;
}
.inner-border {
  border-style: solid;
  border-color: #000000;
  border-width: 0.5px;
  width: 553.42px;
  height: 733.46px;
  position: absolute;
  left: 29.29px;
  top: 29.3px;
}
.container {
  position: absolute;
  inset: 0;
}
.container2 {
  width: 510.02px;
  height: 368.37px;
  position: static;
}
.header {
  width: 228.7px;
  height: 63.36px;
  position: static;
}
.date {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 10px;
  letter-spacing: -0.025em;
  font-weight: 700;
  position: absolute;
  left: calc(50% - 55.92px);
  top: 99.8px;
  width: 118.36px;
  height: 10.99px;
}
.date2 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 10px;
  letter-spacing: -0.025em;
  font-weight: 700;
  position: absolute;
  left: calc(50% - 44.46px);
  top: 120.42px;
  width: 91.28px;
  height: 10.99px;
}
.date3 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 10px;
  letter-spacing: -0.025em;
  font-weight: 700;
  position: absolute;
  left: calc(50% - 51.55px);
  top: 141.77px;
  width: 106.32px;
  height: 10.99px;
}
.date4 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 10px;
  letter-spacing: -0.022000000000000002em;
  font-weight: 700;
  position: absolute;
  left: calc(50% - 111.73px);
  top: 152.17px;
  width: 228.7px;
  height: 10.99px;
}
.header2 {
  width: 221.68px;
  height: 52.54px;
  position: static;
}
.date5 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 10px;
  letter-spacing: -0.025em;
  font-weight: 700;
  position: absolute;
  left: calc(50% - 82.12px);
  top: 37.63px;
  width: 167.51px;
  height: 21.99px;
}
.date6 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 10px;
  letter-spacing: -0.025em;
  font-weight: 700;
  position: absolute;
  left: calc(50% - 51.55px);
  top: 48.14px;
  width: 108.33px;
  height: 10.99px;
}
.date7 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 10px;
  letter-spacing: -0.023em;
  font-weight: 700;
  position: absolute;
  left: 50%;
  translate: -50%;
  top: 58.25px;
  width: 221.68px;
  height: 21.99px;
}
.date8 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 10px;
  letter-spacing: -0.02em;
  font-weight: 700;
  position: absolute;
  left: calc(50% - 84.44px);
  top: 68.94px;
  width: 172.53px;
  height: 10.99px;
}
.date9 {
  color: #003eff;
  text-align: left;
  font-family: "-", sans-serif;
  font-size: 9px;
  letter-spacing: 0undefined;
  font-weight: 400;
  position: absolute;
  left: calc(50% - 62.59px);
  top: 80.18px;
  width: 129.39px;
  height: 9.99px;
}
.date-9-span {
  font-family: "TwCenMt-Regular", sans-serif;
  letter-spacing: 0.03em;
  text-decoration: underline;
}
.date-9-span2 {
  font-family: "TwCenMt-Regular", sans-serif;
  letter-spacing: 0.03em;
}
.date-9-span3 {
  font-family: "TwCenMt-Bold", sans-serif;
  letter-spacing: 0.16em;
  font-weight: 700;
}
.date-9-span4 {
  font-family: "TwCenMt-Regular", sans-serif;
  letter-spacing: 0.04em;
  text-decoration: underline;
}
.container3 {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
  justify-content: flex-start;
  width: 508.55px;
  position: absolute;
  left: 53.48px;
  top: 182.44px;
}
.date10 {
  color: #000000;
  text-align: center;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 10px;
  letter-spacing: -0.025em;
  font-weight: 700;
  position: relative;
  align-self: stretch;
}
.greeting-container {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.greeting-text {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 9px;
  line-height: 9.5px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.student-information {
  display: flex;
  flex-direction: column;
  gap: 0px;
  align-items: flex-start;
  justify-content: flex-start;
  width: 510px;
  position: absolute;
  left: 53px;
  top: 350px;
}
.section-title {
  background: #000000;
  border-style: solid;
  border-color: #000000;
  border-width: 0.75px;
  padding: 10px 2px 10px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  height: 11px;
  position: relative;
}
.greeting-text2 {
  color: #ffffff;
  text-align: left;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 9px;
  line-height: 8.5px;
  letter-spacing: -0.0219em;
  font-weight: 700;
  position: relative;
  flex: 1;
}
.table-row {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.row-column {
  border-style: solid;
  border-color: #ffffff;
  border-width: 0px 0.75px 0.75px 0.75px;
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 32px;
  height: 45px;
  position: relative;
}
.greeting-text3 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 9px;
  line-height: 9.5px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
}
.row-column2 {
  border-style: solid;
  border-color: #ffffff;
  border-width: 0px 0.75px 0.75px 0px;
  padding: 0px 10px 0px 10px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 72px;
  height: 45px;
  position: relative;
}
.greeting-text4 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 9px;
  line-height: 9.5px;
  letter-spacing: -0.0019em;
  font-weight: 400;
  position: relative;
}
.row-column3 {
  border-style: solid;
  border-color: #ffffff;
  border-width: 0px 0.75px 0.75px 0px;
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 40px;
  height: 45px;
  position: relative;
}
.row-column4 {
  border-style: solid;
  border-color: #ffffff;
  border-width: 0px 0.75px 0.75px 0px;
  padding: 0px 4px 0px 4px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 160px;
  height: 45px;
  position: relative;
}
.frame-1171276313 {
  display: flex;
  flex-direction: column;
  gap: 0px;
  align-items: flex-start;
  justify-content: flex-start;
  flex-shrink: 0;
  position: relative;
}
.row-column5 {
  border-style: solid;
  border-color: #ffffff;
  border-width: 0px 0.75px 0.75px 0px;
  padding: 0px 8px 0px 8px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 46px;
  height: 9px;
  position: relative;
}
.greeting-text5 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 8.520000457763672px;
  letter-spacing: 0.008100000000000001em;
  font-weight: 400;
  position: relative;
}
.row-column6 {
  border-style: solid;
  border-color: #ffffff;
  border-width: 0px 0.75px 0.75px 0px;
  padding: 0px 8px 0px 8px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 46px;
  height: 14px;
  position: relative;
}
.row-column7 {
  border-style: solid;
  border-color: #ffffff;
  border-width: 0px 0.75px 0.75px 0px;
  padding: 0px 8px 0px 8px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 46px;
  height: 13px;
  position: relative;
}
.frame-1171276314 {
  display: flex;
  flex-direction: column;
  gap: 0px;
  align-items: flex-start;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 159px;
  position: relative;
}
.row-column8 {
  border-style: solid;
  border-color: #ffffff;
  border-width: 0px 0.75px 0.75px 0px;
  padding: 0px 7px 0px 7px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 160px;
  height: 9px;
  position: relative;
}
.row-column9 {
  border-style: solid;
  border-color: #ffffff;
  border-width: 0px 0.75px 0.75px 0px;
  padding: 0px 7px 0px 7px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 160px;
  height: 14px;
  position: relative;
}
.row-column10 {
  border-style: solid;
  border-color: #ffffff;
  border-width: 0px 0.75px 0.75px 0px;
  padding: 0px 7px 0px 7px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 160px;
  height: 13px;
  position: relative;
}
.container4 {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
  justify-content: flex-start;
  width: 509.55px;
  position: absolute;
  left: 53.46px;
  top: 247.94px;
}
.greeting-text6 {
  color: #000000;
  text-align: left;
  font-family: "-", sans-serif;
  font-size: 9px;
  line-height: 9.5px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.greeting-text-6-span {
  font-family: "TwCenMt-Regular", sans-serif;
}
.greeting-text-6-span2 {
  font-family: "TwCenMt-BoldItalic", sans-serif;
  font-weight: 700;
  font-style: italic;
}
.greeting-text7 {
  color: #000000;
  text-align: center;
  font-family: "-", sans-serif;
  font-size: 7px;
  line-height: 9.5px;
  letter-spacing: 0undefined;
  font-weight: 400;
  position: absolute;
  left: 50%;
  translate: -50%;
  top: 750.31px;
  width: 508.55px;
  height: 9.99px;
}
.greeting-text-7-span {
  font-family: "TwCenMt-Regular", sans-serif;
  letter-spacing: -0.011899999999999999em;
}
.greeting-text-7-span2 {
  font-family: "TwCenMt-Regular", sans-serif;
  letter-spacing: -0.0219em;
}
.greeting-text-7-span3 {
  font-family: "TwCenMt-Bold", sans-serif;
  letter-spacing: 0.008100000000000001em;
  font-weight: 700;
}
.greeting-text-7-span4 {
  font-family: "TwCenMt-Regular", sans-serif;
  letter-spacing: 0.008100000000000001em;
}
.greeting-container2 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 509px;
  position: absolute;
  left: 52.98px;
  top: 548px;
}
.greeting-text8 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 8.520000457763672px;
  letter-spacing: -0.0019em;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.greeting-container3 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 509px;
  position: absolute;
  left: 53px;
  top: 661px;
}
.greeting-container4 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 510px;
  position: absolute;
  left: 53px;
  top: 738px;
}
.greeting-text9 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 8.520000457763672px;
  letter-spacing: -0.0019em;
  font-weight: 400;
  position: relative;
  width: 510.04px;
}
.section-title2 {
  background: #000000;
  border-style: solid;
  border-color: #000000;
  border-width: 0.75px;
  padding: 10px 2px 10px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  width: 510px;
  height: 11px;
  position: absolute;
  left: 53px;
  top: 416px;
}
.section-title3 {
  background: #000000;
  border-style: solid;
  border-color: #000000;
  border-width: 0.75px;
  padding: 10px 2px 10px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  width: 510px;
  height: 11px;
  position: absolute;
  left: 53px;
  top: 531px;
}
.section-title4 {
  background: #000000;
  border-style: solid;
  border-color: #000000;
  border-width: 0.75px;
  padding: 10px 2px 10px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  width: 510px;
  height: 11px;
  position: absolute;
  left: 52.98px;
  top: 644px;
}
.section-title5 {
  background: #000000;
  border-style: solid;
  border-color: #000000;
  border-width: 0.75px;
  padding: 10px 2px 10px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  width: 510px;
  height: 11px;
  position: absolute;
  left: 53px;
  top: 721px;
}
.group-34438 {
  position: absolute;
  inset: 0;
}
.la-main-campus {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 9.960000038146973px;
  font-weight: 700;
  position: absolute;
  right: 77.9%;
  left: 9.53%;
  width: 12.57%;
  bottom: 44.82%;
  top: 53.79%;
  height: 1.39%;
}
.group-34436 {
  width: 503.93px;
  height: 30.8px;
  position: static;
}
.group-34434 {
  width: 116.06px;
  height: 29.8px;
  position: static;
}
.doctor-of-management-tuition {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 8.520000457763672px;
  letter-spacing: -0.0019em;
  font-weight: 700;
  position: absolute;
  right: 8.01%;
  left: 73.03%;
  width: 18.96%;
  bottom: 42.78%;
  top: 56.09%;
  height: 1.14%;
}
.group-34427 {
  width: 90.02px;
  height: 19.53px;
  position: static;
}
.frame-1171276318 {
  display: flex;
  flex-direction: row;
  gap: 5px;
  align-items: flex-start;
  justify-content: flex-start;
  position: absolute;
  left: 450.62px;
  top: 454.47px;
}
.frame-1171276319 {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  height: auto;
  position: relative;
  overflow: visible;
}
.per-unit-450 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 8.520000457763672px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
}
.frame-11712763192 {
  display: flex;
  flex-direction: row;
  gap: 5px;
  align-items: flex-start;
  justify-content: flex-start;
  position: absolute;
  left: 450.62px;
  top: 465px;
}
.frame-11712763193 {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  height: auto;
  position: relative;
  overflow: visible;
}
.per-3-unit-course-1-350 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 8.520000457763672px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
}
.group-34428 {
  width: 87.51px;
  height: 29.53px;
  position: static;
}
.undergraduate-tuition {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 8.520000457763672px;
  letter-spacing: -0.0019em;
  font-weight: 700;
  position: absolute;
  right: 76.5%;
  left: 9.65%;
  width: 13.84%;
  bottom: 42.87%;
  top: 55.99%;
  height: 1.14%;
}
.group-344272 {
  width: 83.02px;
  height: 19.53px;
  position: static;
}
.frame-11712763182 {
  display: flex;
  flex-direction: row;
  gap: 5px;
  align-items: flex-start;
  justify-content: flex-start;
  position: absolute;
  left: 63.56px;
  top: 453.47px;
}
.frame-11712763194 {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  height: auto;
  position: relative;
  overflow: visible;
}
.per-unit-300 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 8.520000457763672px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
}
.frame-11712763195 {
  display: flex;
  flex-direction: row;
  gap: 5px;
  align-items: flex-start;
  justify-content: flex-start;
  position: absolute;
  left: 63.56px;
  top: 464px;
}
.frame-11712763196 {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  height: auto;
  position: relative;
  overflow: visible;
}
.per-3-unit-course-900 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 8.520000457763672px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
}
.group-34432 {
  width: 93.32px;
  height: 29.53px;
  position: static;
}
.master-tuition {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 8.520000457763672px;
  letter-spacing: -0.0019em;
  font-weight: 700;
  position: absolute;
  right: 57.23%;
  left: 28.92%;
  width: 13.84%;
  bottom: 42.87%;
  top: 55.99%;
  height: 1.14%;
}
.frame-11712763183 {
  display: flex;
  flex-direction: row;
  gap: 5px;
  align-items: flex-start;
  justify-content: flex-start;
  position: absolute;
  left: 180.3px;
  top: 453.47px;
}
.frame-11712763197 {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  height: auto;
  position: relative;
  overflow: visible;
}
.per-unit-400 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 8.520000457763672px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
}
.frame-11712763198 {
  display: flex;
  flex-direction: row;
  gap: 5px;
  align-items: flex-start;
  justify-content: flex-start;
  position: absolute;
  left: 180.3px;
  top: 464px;
}
.frame-11712763199 {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  height: auto;
  position: relative;
  overflow: visible;
}
.per-3-unit-course-1-200 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 8.520000457763672px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
}
.group-34433 {
  width: 147.14px;
  height: 29.8px;
  position: static;
}
.doctor-of-business-administration-tuition {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 8.520000457763672px;
  letter-spacing: -0.0019em;
  font-weight: 700;
  position: absolute;
  right: 28.65%;
  left: 47.3%;
  width: 24.04%;
  bottom: 42.9%;
  top: 55.96%;
  height: 1.14%;
}
.frame-*********** {
  display: flex;
  flex-direction: row;
  gap: 5px;
  align-items: flex-start;
  justify-content: flex-start;
  position: absolute;
  left: 293.17px;
  top: 453.47px;
}
.frame-117127631910 {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  height: auto;
  position: relative;
  overflow: visible;
}
.per-unit-425 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 8.520000457763672px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
}
.frame-117127631911 {
  display: flex;
  flex-direction: row;
  gap: 5px;
  align-items: flex-start;
  justify-content: flex-start;
  position: absolute;
  left: 293.17px;
  top: 464px;
}
.frame-117127631912 {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  height: auto;
  position: relative;
  overflow: visible;
}
.per-3-unit-course-1-275 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 8.520000457763672px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
}
.group-34440 {
  position: absolute;
  inset: 0;
}
.orange-county-and-san-diego {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 9.960000038146973px;
  font-weight: 700;
  position: absolute;
  right: 69.56%;
  left: 9.53%;
  width: 20.91%;
  bottom: 38.48%;
  top: 60.14%;
  height: 1.39%;
}
.doctor-of-management-tuition2 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 8.520000457763672px;
  letter-spacing: -0.0019em;
  font-weight: 700;
  position: absolute;
  right: 8.01%;
  left: 73.03%;
  width: 18.96%;
  bottom: 36.43%;
  top: 62.43%;
  height: 1.14%;
}
.frame-11712763185 {
  display: flex;
  flex-direction: row;
  gap: 5px;
  align-items: flex-start;
  justify-content: flex-start;
  position: absolute;
  left: 450.62px;
  top: 504.74px;
}
.frame-117127631913 {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  height: auto;
  position: relative;
  overflow: visible;
}
.frame-117127631914 {
  display: flex;
  flex-direction: row;
  gap: 5px;
  align-items: flex-start;
  justify-content: flex-start;
  position: absolute;
  left: 450.62px;
  top: 515.27px;
}
.frame-117127631915 {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  height: auto;
  position: relative;
  overflow: visible;
}
.undergraduate-tuition2 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 8.520000457763672px;
  letter-spacing: -0.0019em;
  font-weight: 700;
  position: absolute;
  right: 76.5%;
  left: 9.65%;
  width: 13.84%;
  bottom: 36.52%;
  top: 62.34%;
  height: 1.14%;
}
.frame-11712763186 {
  display: flex;
  flex-direction: row;
  gap: 5px;
  align-items: flex-start;
  justify-content: flex-start;
  position: absolute;
  left: 63.56px;
  top: 503.74px;
}
.frame-117127631916 {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  height: auto;
  position: relative;
  overflow: visible;
}
.per-unit-225 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 8.520000457763672px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
}
.frame-117127631917 {
  display: flex;
  flex-direction: row;
  gap: 5px;
  align-items: flex-start;
  justify-content: flex-start;
  position: absolute;
  left: 63.56px;
  top: 514.27px;
}
.frame-117127631918 {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  height: auto;
  position: relative;
  overflow: visible;
}
.per-3-unit-course-675 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 8.520000457763672px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
}
.master-tuition2 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 8.520000457763672px;
  letter-spacing: -0.0019em;
  font-weight: 700;
  position: absolute;
  right: 57.23%;
  left: 28.92%;
  width: 13.84%;
  bottom: 36.52%;
  top: 62.34%;
  height: 1.14%;
}
.frame-11712763187 {
  display: flex;
  flex-direction: row;
  gap: 5px;
  align-items: flex-start;
  justify-content: flex-start;
  position: absolute;
  left: 180.3px;
  top: 503.74px;
}
.frame-117127631919 {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  height: auto;
  position: relative;
  overflow: visible;
}
.per-unit-350 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 8.520000457763672px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
}
.frame-117127631920 {
  display: flex;
  flex-direction: row;
  gap: 5px;
  align-items: flex-start;
  justify-content: flex-start;
  position: absolute;
  left: 180.3px;
  top: 514.27px;
}
.frame-117127631921 {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  height: auto;
  position: relative;
  overflow: visible;
}
.per-3-unit-course-1-050 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 8.520000457763672px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
}
.doctor-of-business-administration-tuition2 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 8.520000457763672px;
  letter-spacing: -0.0019em;
  font-weight: 700;
  position: absolute;
  right: 28.65%;
  left: 47.3%;
  width: 24.04%;
  bottom: 36.56%;
  top: 62.31%;
  height: 1.14%;
}
.frame-*********** {
  display: flex;
  flex-direction: row;
  gap: 5px;
  align-items: flex-start;
  justify-content: flex-start;
  position: absolute;
  left: 293.17px;
  top: 503.74px;
}
.frame-117127631922 {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  height: auto;
  position: relative;
  overflow: visible;
}
.per-unit-375 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 8.520000457763672px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
}
.frame-117127631923 {
  display: flex;
  flex-direction: row;
  gap: 5px;
  align-items: flex-start;
  justify-content: flex-start;
  position: absolute;
  left: 293.17px;
  top: 514.27px;
}
.frame-117127631924 {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  height: auto;
  position: relative;
  overflow: visible;
}
.per-3-unit-course-1-225 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 8.520000457763672px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
}
.group-344362 {
  position: absolute;
  inset: 0;
}
.group-344282 {
  width: 85.04px;
  height: 29.79px;
  position: static;
}
.fall-sep-dec {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 8.520000457763672px;
  letter-spacing: -0.0019em;
  font-weight: 400;
  position: absolute;
  right: 73.51%;
  left: 12.64%;
  width: 13.84%;
  bottom: 22.64%;
  top: 76.23%;
  height: 1.14%;
}
.group-344273 {
  width: 78.61px;
  height: 9px;
  position: static;
}
.group-34446 {
  width: 78.61px;
  height: 9px;
  position: static;
}
.vector {
  width: 3.02px;
  height: 3.02px;
  position: absolute;
  left: 83.81px;
  top: 618.5px;
  overflow: visible;
}
.session-1-sept-oct {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 8.520000457763672px;
  letter-spacing: -0.011899999999999999em;
  font-weight: 400;
  position: absolute;
  left: 95.42px;
  top: 613.78px;
}
.group-344283 {
  width: 77.61px;
  height: 9px;
  position: static;
}
.group-344462 {
  width: 77.61px;
  height: 9px;
  position: static;
}
.vector2 {
  width: 3.02px;
  height: 3.02px;
  position: absolute;
  left: 83.81px;
  top: 629.21px;
  overflow: visible;
}
.session-2-nov-dec {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 8.520000457763672px;
  letter-spacing: -0.011899999999999999em;
  font-weight: 400;
  position: absolute;
  left: 95.42px;
  top: 624.49px;
}
.group-34429 {
  width: 84.72px;
  height: 29.79px;
  position: static;
}
.summer-may-aug {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 8.520000457763672px;
  letter-spacing: -0.0019em;
  font-weight: 400;
  position: absolute;
  right: 46.05%;
  left: 40.11%;
  width: 13.84%;
  bottom: 22.64%;
  top: 76.23%;
  height: 1.14%;
}
.group-344274 {
  width: 77.61px;
  height: 9px;
  position: static;
}
.vector3 {
  width: 3.02px;
  height: 3.02px;
  position: absolute;
  left: 251.91px;
  top: 618.5px;
  overflow: visible;
}
.session-1-may-jun {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 8.520000457763672px;
  letter-spacing: -0.011899999999999999em;
  font-weight: 400;
  position: absolute;
  left: 263.52px;
  top: 613.78px;
}
.group-344284 {
  width: 73.61px;
  height: 9px;
  position: static;
}
.group-344463 {
  width: 73.61px;
  height: 9px;
  position: static;
}
.vector4 {
  width: 3.02px;
  height: 3.02px;
  position: absolute;
  left: 251.91px;
  top: 629.21px;
  overflow: visible;
}
.session-2-jul-aug {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 8.520000457763672px;
  letter-spacing: -0.011899999999999999em;
  font-weight: 400;
  position: absolute;
  left: 263.52px;
  top: 624.49px;
}
.group-34430 {
  width: 84.72px;
  height: 29.79px;
  position: static;
}
.spring-jan-apr {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 8.520000457763672px;
  letter-spacing: -0.0019em;
  font-weight: 400;
  position: absolute;
  right: 20.17%;
  left: 65.99%;
  width: 13.84%;
  bottom: 22.68%;
  top: 76.18%;
  height: 1.14%;
}
.group-344275 {
  width: 75.61px;
  height: 9px;
  position: static;
}
.group-344464 {
  width: 75.61px;
  height: 9px;
  position: static;
}
.vector5 {
  width: 3.02px;
  height: 3.02px;
  position: absolute;
  left: 410.28px;
  top: 618.18px;
  overflow: visible;
}
.session-1-jan-feb {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 8.520000457763672px;
  letter-spacing: -0.011899999999999999em;
  font-weight: 400;
  position: absolute;
  left: 421.88px;
  top: 613.46px;
}
.vector6 {
  width: 3.02px;
  height: 3.02px;
  position: absolute;
  left: 410.28px;
  top: 628.89px;
  overflow: visible;
}
.session-2-mar-apr {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 8.520000457763672px;
  letter-spacing: -0.011899999999999999em;
  font-weight: 400;
  position: absolute;
  left: 421.88px;
  top: 624.17px;
}
.group-344465 {
  position: absolute;
  inset: 0;
}
.vector7 {
  width: 3.02px;
  height: 3.02px;
  position: absolute;
  left: 72.51px;
  top: 695.1px;
  overflow: visible;
}
.undergraduate-12-units-per-mandatory-semester {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 8.520000457763672px;
  letter-spacing: -0.011899999999999999em;
  font-weight: 400;
  position: absolute;
  left: 89.86px;
  top: 690.6px;
}
.vector8 {
  width: 3.02px;
  height: 3.02px;
  position: absolute;
  left: 72.51px;
  top: 705.81px;
  overflow: visible;
}
.graduate-9-units-per-mandatory-semester {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 8.520000457763672px;
  letter-spacing: -0.011899999999999999em;
  font-weight: 400;
  position: absolute;
  left: 89.86px;
  top: 701.31px;
}
