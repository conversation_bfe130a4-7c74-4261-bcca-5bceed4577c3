.new-program-5,
.new-program-5 * {
  box-sizing: border-box;
}
.new-program-5 {
  background: #ffffff;
  height: 792px;
  position: relative;
  overflow: hidden;
  width: 595px;
}
.border {
  position: absolute;
  inset: 0;
}
.middle-border {
  border-style: solid;
  border-color: #000000;
  border-width: 2.8px;
  width: 561.06px;
  height: 741.05px;
  position: absolute;
  left: 25.45px;
  top: 25.48px;
}
.outer-border {
  border-style: solid;
  border-color: #000000;
  border-width: 0.5px;
  width: 563.97px;
  height: 743.98px;
  position: absolute;
  left: 24.04px;
  top: 24.02px;
}
.inner-border {
  border-style: solid;
  border-color: #000000;
  border-width: 0.5px;
  width: 553.42px;
  height: 733.46px;
  position: absolute;
  left: 29.29px;
  top: 29.3px;
}
.greeting-text {
  color: #000000;
  text-align: center;
  font-family: "-", sans-serif;
  font-size: 7px;
  line-height: 9.5px;
  letter-spacing: 0undefined;
  font-weight: 400;
  position: absolute;
  left: calc(50% - 252.45px);
  top: 747.33px;
  width: 508.55px;
  height: 9.99px;
}
.greeting-text-span {
  font-family: "TwCenMt-Regular", sans-serif;
  letter-spacing: -0.011899999999999999em;
}
.greeting-text-span2 {
  font-family: "TwCenMt-Regular", sans-serif;
  letter-spacing: -0.0219em;
}
.greeting-text-span3 {
  font-family: "TwCenMt-Bold", sans-serif;
  letter-spacing: 0.008100000000000001em;
  font-weight: 700;
}
.greeting-text-span4 {
  font-family: "TwCenMt-Regular", sans-serif;
  letter-spacing: 0.008100000000000001em;
}
.greeting-container {
  background: #000000;
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 53px;
  top: 53.02px;
}
.greeting-text2 {
  color: #ffffff;
  text-align: left;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 9px;
  line-height: 8.5px;
  letter-spacing: -0.0219em;
  font-weight: 700;
  position: relative;
  flex: 1;
}
.greeting-container2 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 53px;
  top: 71px;
}
.greeting-text3 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 9px;
  line-height: 9.5px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.greeting-container3 {
  background: #000000;
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 53px;
  top: 401.02px;
}
.greeting-container4 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 53px;
  top: 419px;
}
.greeting-container5 {
  background: #000000;
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 53px;
  top: 457.02px;
}
.greeting-container6 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 53px;
  top: 474px;
}
.greeting-container7 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 53px;
  top: 529px;
}
.greeting-text4 {
  color: #000000;
  text-align: left;
  font-family: "-", sans-serif;
  font-size: 9px;
  line-height: 9.5px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.greeting-text-4-span {
  font-family: "TwCenMt-Regular", sans-serif;
}
.greeting-text-4-span2 {
  font-family: "TwCenMt-Bold", sans-serif;
  font-weight: 700;
}
.greeting-container8 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 53px;
  top: 298px;
}
.steps-list-container {
  display: flex;
  flex-direction: column;
  gap: 9px;
  align-items: flex-start;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 53px;
  top: 337px;
}
.frame {
  display: flex;
  flex-direction: row;
  gap: 14px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.step-instruction {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 9px;
  line-height: 9.1px;
  letter-spacing: -0.011899999999999999em;
  font-weight: 400;
  position: relative;
}
.step-instruction2 {
  color: #000000;
  text-align: left;
  font-family: "-", sans-serif;
  font-size: 9px;
  line-height: 9.1px;
  letter-spacing: -0.0319em;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.step-instruction-2-span {
  font-family: "TwCenMt-BoldItalic", sans-serif;
  font-weight: 700;
  font-style: italic;
}
.step-instruction-2-span2 {
  font-family: "TwCenMt-Regular", sans-serif;
}
.frame2 {
  display: flex;
  flex-direction: row;
  gap: 11px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.step-instruction3 {
  color: #000000;
  text-align: left;
  font-family: "-", sans-serif;
  font-size: 9px;
  line-height: 9.1px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.step-instruction-3-span {
  font-family: "TwCenMt-BoldItalic", sans-serif;
  font-weight: 700;
  font-style: italic;
}
.step-instruction-3-span2 {
  font-family: "TwCenMt-Italic", sans-serif;
  font-style: italic;
}
.container {
  position: absolute;
  inset: 0;
}
.container2 {
  width: 234px;
  height: 23.88px;
  position: static;
}
.greeting-container9 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 113px;
  height: 23.88px;
  position: absolute;
  left: 54px;
  top: 109px;
}
.greeting-container10 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: center;
  width: 122px;
  height: 23.88px;
  position: absolute;
  left: 166px;
  top: 109px;
}
.greeting-text5 {
  color: #000000;
  text-align: center;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 8px;
  line-height: 9.5px;
  letter-spacing: -0.0019em;
  font-weight: 700;
  position: relative;
  flex: 1;
}
.container3 {
  width: 234px;
  height: 14.76px;
  position: static;
}
.greeting-container11 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 113px;
  height: 14.76px;
  position: absolute;
  left: 54px;
  top: 131.92px;
}
.greeting-container12 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: center;
  width: 122px;
  height: 14.76px;
  position: absolute;
  left: 166px;
  top: 131.92px;
}
.container4 {
  width: 234px;
  height: 15.38px;
  position: static;
}
.greeting-container13 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 113px;
  height: 15.38px;
  position: absolute;
  left: 54px;
  top: 145.71px;
}
.greeting-container14 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: center;
  width: 122px;
  height: 15.38px;
  position: absolute;
  left: 166px;
  top: 145.71px;
}
.container5 {
  width: 234px;
  height: 31.79px;
  position: static;
}
.greeting-container15 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 113px;
  height: 31.79px;
  position: absolute;
  left: 53.98px;
  top: 175.36px;
}
.greeting-container16 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: center;
  width: 122px;
  height: 31.79px;
  position: absolute;
  left: 165.98px;
  top: 175.36px;
}
.container6 {
  width: 234px;
  height: 18.14px;
  position: static;
}
.greeting-container17 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 113px;
  height: 18.14px;
  position: absolute;
  left: 54px;
  top: 206.12px;
}
.greeting-container18 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: center;
  width: 122px;
  height: 18.14px;
  position: absolute;
  left: 166px;
  top: 206.12px;
}
.container7 {
  width: 234px;
  height: 17.02px;
  position: static;
}
.greeting-container19 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 113px;
  height: 17.02px;
  position: absolute;
  left: 54px;
  top: 223.33px;
}
.greeting-container20 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: center;
  width: 122px;
  height: 17.02px;
  position: absolute;
  left: 166px;
  top: 223.33px;
}
.container8 {
  width: 234px;
  height: 17.98px;
  position: static;
}
.greeting-container21 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 113px;
  height: 17.98px;
  position: absolute;
  left: 54px;
  top: 239.41px;
}
.greeting-container22 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: center;
  width: 122px;
  height: 17.98px;
  position: absolute;
  left: 166px;
  top: 239.41px;
}
.container9 {
  width: 234px;
  height: 17.03px;
  position: static;
}
.greeting-container23 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 113px;
  height: 17.03px;
  position: absolute;
  left: 54px;
  top: 256.43px;
}
.greeting-container24 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: center;
  width: 122px;
  height: 17.03px;
  position: absolute;
  left: 166px;
  top: 256.43px;
}
.container10 {
  width: 234px;
  height: 17.34px;
  position: static;
}
.greeting-container25 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 113px;
  height: 17.34px;
  position: absolute;
  left: 54px;
  top: 272.45px;
}
.greeting-container26 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: center;
  width: 122px;
  height: 17.34px;
  position: absolute;
  left: 166px;
  top: 272.45px;
}
.container11 {
  width: 234px;
  height: 16.12px;
  position: static;
}
.greeting-container27 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 113px;
  height: 16.12px;
  position: absolute;
  left: 54px;
  top: 160.18px;
}
.greeting-container28 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: center;
  width: 122px;
  height: 16.12px;
  position: absolute;
  left: 166px;
  top: 160.18px;
}
.container12 {
  width: 253.02px;
  height: 24.29px;
  position: static;
}
.greeting-container29 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 158px;
  height: 24.29px;
  position: absolute;
  left: 306px;
  top: 108.18px;
}
.greeting-container30 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: center;
  width: 96px;
  height: 24.29px;
  position: absolute;
  left: 463.02px;
  top: 108.18px;
}
.greeting-text6 {
  color: #000000;
  text-align: right;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 8px;
  line-height: 9.5px;
  letter-spacing: -0.0019em;
  font-weight: 700;
  position: relative;
  flex: 1;
}
.container13 {
  width: 253.02px;
  height: 15.43px;
  position: static;
}
.greeting-container31 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 158px;
  height: 15.43px;
  position: absolute;
  left: 306px;
  top: 131.5px;
}
.greeting-container32 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: center;
  width: 96px;
  height: 15.43px;
  position: absolute;
  left: 463.02px;
  top: 131.5px;
}
.container14 {
  width: 253.02px;
  height: 16.09px;
  position: static;
}
.greeting-container33 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 158px;
  height: 16.09px;
  position: absolute;
  left: 306px;
  top: 145.88px;
}
.greeting-container34 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: center;
  width: 96px;
  height: 16.09px;
  position: absolute;
  left: 463.02px;
  top: 145.88px;
}
.container15 {
  width: 253.02px;
  height: 15.26px;
  position: static;
}
.greeting-container35 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 158px;
  height: 15.26px;
  position: absolute;
  left: 306px;
  top: 160.93px;
}
.greeting-container36 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: center;
  width: 96px;
  height: 15.26px;
  position: absolute;
  left: 463.02px;
  top: 160.93px;
}
.container16 {
  width: 253.02px;
  height: 48.97px;
  position: static;
}
.greeting-container37 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 158px;
  height: 48.97px;
  position: absolute;
  left: 306px;
  top: 175.21px;
}
.greeting-text7 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 9px;
  line-height: 9.5px;
  letter-spacing: -0.0219em;
  font-weight: 700;
  text-decoration: underline;
  position: relative;
  flex: 1;
}
.greeting-container38 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: center;
  width: 96px;
  height: 48.97px;
  position: absolute;
  left: 463.02px;
  top: 175.21px;
}
.greeting-text8 {
  color: #000000;
  text-align: right;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 8px;
  line-height: 9.5px;
  letter-spacing: -0.0019em;
  font-weight: 700;
  text-decoration: underline;
  position: relative;
  flex: 1;
}
.container17 {
  width: 253.02px;
  height: 34.22px;
  position: static;
}
.greeting-container39 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 158px;
  height: 34.22px;
  position: absolute;
  left: 306.04px;
  top: 223.2px;
}
.greeting-container40 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: center;
  width: 96px;
  height: 34.22px;
  position: absolute;
  left: 463.06px;
  top: 223.2px;
}
.container18 {
  width: 253.02px;
  height: 33.36px;
  position: static;
}
.greeting-container41 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 158px;
  height: 33.36px;
  position: absolute;
  left: 306.04px;
  top: 256.43px;
}
.greeting-container42 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: center;
  width: 96px;
  height: 33.36px;
  position: absolute;
  left: 463.06px;
  top: 256.43px;
}
.greeting-container43 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 53px;
  top: 576px;
}
.greeting-text-4-span {
  font-family: "TwCenMt-Regular", sans-serif;
}
.greeting-text-4-span2 {
  font-family: "TwCenMt-Bold", sans-serif;
  font-weight: 700;
}
.greeting-container44 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 52.98px;
  top: 604px;
}
.greeting-container45 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 53.32px;
  top: 667.12px;
}
.greeting-text9 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 9px;
  line-height: 9.5px;
  letter-spacing: -0.0219em;
  font-weight: 700;
  position: relative;
}
.greeting-container46 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 124px;
  top: 667.12px;
}
.greeting-text10 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 9px;
  line-height: 9.5px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
}
.greeting-container47 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 197px;
  top: 667.12px;
}
.greeting-container48 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 53px;
  top: 623px;
}
.vector-157 {
  width: 289.86px;
  height: 0px;
  position: absolute;
  left: 48.48px;
  top: 667.46px;
  transform: translate(0px, -0.25px);
  overflow: visible;
}
.greeting-container49 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 372.69px;
  top: 667.12px;
}
.greeting-container50 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 442.69px;
  top: 667.12px;
}
.greeting-container51 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 480.69px;
  top: 667.12px;
}
.vector-158 {
  width: 180.93px;
  height: 0px;
  position: absolute;
  left: 369.13px;
  top: 667.44px;
  overflow: visible;
}
.signature {
  width: 19.27%;
  height: 2.69%;
  position: absolute;
  right: 20.77%;
  left: 59.97%;
  bottom: 15.56%;
  top: 81.75%;
  object-fit: cover;
}
.date {
  color: #000000;
  text-align: left;
  font-family: "Cambria-Regular", sans-serif;
  font-size: 8px;
  letter-spacing: 0.03em;
  font-weight: 400;
  position: absolute;
  left: 494.1px;
  top: 651.96px;
}
