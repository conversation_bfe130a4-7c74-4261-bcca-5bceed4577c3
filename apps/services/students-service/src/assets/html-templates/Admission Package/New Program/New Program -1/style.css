.new-program-1,
.new-program-1 * {
  box-sizing: border-box;
}
.new-program-1 {
  background: #ffffff;
  height: 792px;
  position: relative;
  overflow: hidden;
  width: 595px;
}
.logo-with-eagle {
  height: auto;
  position: absolute;
  left: 286.04px;
  top: 20.31px;
  overflow: visible;
}
.date {
  color: #000000;
  text-align: left;
  font-family: "Calibri-Regular", sans-serif;
  font-size: 9.960000038146973px;
  font-weight: 400;
  position: absolute;
  left: 71.92px;
  top: 71.4px;
}
.subject-container {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 468px;
  position: absolute;
  left: 72px;
  top: 169px;
}
.subject-text {
  color: #000000;
  text-align: left;
  font-family: "Calibri-Regular", sans-serif;
  font-size: 9.960000038146973px;
  font-weight: 400;
  position: relative;
}
.greeting-container {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 468px;
  position: absolute;
  left: 72px;
  top: 193px;
}
.greeting-text {
  color: #000000;
  text-align: left;
  font-family: "Calibri-Regular", sans-serif;
  font-size: 10px;
  font-weight: 400;
  position: relative;
}
.congratulations-container {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 468px;
  position: absolute;
  left: 71.92px;
  top: 217.14px;
}
.congratulations-text {
  color: #000000;
  text-align: left;
  font-family: "Calibri-Regular", sans-serif;
  font-size: 10px;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.steps-list-container {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: flex-start;
  justify-content: flex-start;
  width: 449.58px;
  position: absolute;
  left: 90.13px;
  top: 413.6px;
}
.frame {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.step-number {
  color: #000000;
  text-align: left;
  font-family: "Calibri-Regular", sans-serif;
  font-size: 10px;
  font-weight: 400;
  position: relative;
}
.step-instruction {
  text-align: left;
  position: relative;
  width: 431.58px;
}
.step-instruction-span {
  color: #000000;
  font-family: "Calibri-Regular", sans-serif;
  font-size: 10px;
  font-weight: 400;
}
.step-instruction-span2 {
  color: #003eff;
  font-family: "Calibri-Regular", sans-serif;
  font-size: 10px;
  font-weight: 400;
  text-decoration: underline;
}
.step-instruction-span3 {
  color: #000000;
  font-family: "Georgia-Bold", sans-serif;
  font-size: 12px;
  font-weight: 700;
}
.step-instruction2 {
  color: #000000;
  text-align: left;
  font-family: "Calibri-Regular", sans-serif;
  font-size: 10px;
  font-weight: 400;
  position: relative;
  width: 431.58px;
}
.step-instruction3 {
  color: #000000;
  text-align: left;
  position: relative;
  width: 431.58px;
}
.step-instruction-3-span {
  font-family: "Calibri-Regular", sans-serif;
  font-size: 10px;
  font-weight: 400;
}
.step-instruction-3-span2 {
  font-family: "Georgia-Bold", sans-serif;
  font-size: 12px;
  font-weight: 700;
}
.enrollment-confirmation-container {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 468px;
  position: absolute;
  left: 72px;
  top: 353px;
}
.enrollment-confirmation-text {
  color: #000000;
  text-align: left;
  font-family: "-", sans-serif;
  font-size: 9.960000038146973px;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.enrollment-confirmation-text-span {
  font-family: "Calibri-Regular", sans-serif;
}
.enrollment-confirmation-text-span2 {
  font-family: "Calibri-Bold", sans-serif;
  font-weight: 700;
}
.welcome-container {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 468px;
  position: absolute;
  left: 71.92px;
  top: 493px;
}
.welcome-text {
  color: #000000;
  text-align: left;
  font-family: "Calibri-Regular", sans-serif;
  font-size: 9.960000038146973px;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.welcome-container2 {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 468px;
  position: absolute;
  left: 72px;
  top: 540.89px;
}
.closing-container {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 468px;
  position: absolute;
  left: 71.92px;
  top: 591px;
}
.closing-text {
  color: #000000;
  text-align: left;
  font-family: "Calibri-Regular", sans-serif;
  font-size: 10px;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.director-container {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 468px;
  position: absolute;
  left: 71.92px;
  top: 638px;
}
.director-information {
  color: #000000;
  text-align: left;
  font-family: "Calibri-Regular", sans-serif;
  font-size: 10px;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.steps-container {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 468px;
  position: absolute;
  left: 72px;
  top: 389px;
}
.steps-text {
  color: #000000;
  text-align: left;
  font-family: "Calibri-Regular", sans-serif;
  font-size: 9.960000038146973px;
  font-weight: 400;
  position: relative;
}
.program-container {
  position: absolute;
  inset: 0;
}
.program-details {
  color: #000000;
  text-align: center;
  font-family: "Calibri-Bold", sans-serif;
  font-size: 9.960000038146973px;
  font-weight: 700;
  position: absolute;
  left: 157px;
  top: 278.89px;
}
.session {
  color: #000000;
  text-align: center;
  font-family: "Calibri-Bold", sans-serif;
  font-size: 9.960000038146973px;
  letter-spacing: -0.01em;
  font-weight: 700;
  position: absolute;
  left: 267px;
  top: 303.34px;
}
.program-start-date {
  color: #000000;
  text-align: center;
  font-family: "Calibri-Bold", sans-serif;
  font-size: 9.960000038146973px;
  font-weight: 700;
  position: absolute;
  left: 245px;
  top: 315.34px;
}
.expected-end-date {
  color: #000000;
  text-align: center;
  font-family: "Calibri-Bold", sans-serif;
  font-size: 9.960000038146973px;
  font-weight: 700;
  position: absolute;
  left: 244.98px;
  top: 327.34px;
}
.review-container {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 468px;
  position: absolute;
  left: 71.92px;
  top: 242.51px;
}
.review-text {
  color: #000000;
  text-align: left;
  font-family: "Calibri-Regular", sans-serif;
  font-size: 9.960000038146973px;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.address-container {
  position: absolute;
  inset: 0;
}
.recipient-name {
  color: #000000;
  text-align: left;
  font-family: "Calibri-Regular", sans-serif;
  font-size: 9.960000038146973px;
  font-weight: 400;
  position: absolute;
  left: 71.92px;
  top: 108.44px;
}
.street-address {
  color: #000000;
  text-align: left;
  font-family: "Calibri-Regular", sans-serif;
  font-size: 9.960000038146973px;
  font-weight: 400;
  position: absolute;
  left: 71.92px;
  top: 120.44px;
}
.country {
  color: #000000;
  text-align: left;
  font-family: "Calibri-Regular", sans-serif;
  font-size: 9.960000038146973px;
  font-weight: 400;
  position: absolute;
  left: 71.92px;
  top: 132.44px;
}
.iau-stamp {
  border-radius: 53.7px;
  width: 17.55%;
  height: 107.4px;
  position: absolute;
  right: 47.54%;
  left: 34.91%;
  top: calc(50% - -182.28px);
  object-fit: cover;
  aspect-ratio: 1;
}
.signature {
  width: 23.7%;
  height: 3.3%;
  position: absolute;
  right: 64.55%;
  left: 11.75%;
  bottom: 20.05%;
  top: 76.64%;
  object-fit: cover;
}
.contact-information {
  color: #0067b1;
  text-align: left;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 11px;
  letter-spacing: 0.075em;
  font-weight: 700;
  position: absolute;
  left: 2.79px;
  top: 780.63px;
}
