.new-program-6,
.new-program-6 * {
  box-sizing: border-box;
}
.new-program-6 {
  background: #ffffff;
  height: 792px;
  position: relative;
  overflow: hidden;
  width: 595px;
}
.border {
  position: absolute;
  inset: 0;
}
.middle-border {
  border-style: solid;
  border-color: #000000;
  border-width: 2.8px;
  width: 561.06px;
  height: 741.05px;
  position: absolute;
  left: 25.45px;
  top: 25.48px;
}
.outer-border {
  border-style: solid;
  border-color: #000000;
  border-width: 0.5px;
  width: 563.97px;
  height: 743.98px;
  position: absolute;
  left: 24.04px;
  top: 24.02px;
}
.inner-border {
  border-style: solid;
  border-color: #000000;
  border-width: 0.5px;
  width: 553.42px;
  height: 733.46px;
  position: absolute;
  left: 29.29px;
  top: 29.3px;
}
.group-34423 {
  position: absolute;
  inset: 0;
}
.program-completion-plan-pcp {
  color: #4a68b1;
  text-align: center;
  font-family: "ArialCe-Bold", sans-serif;
  font-size: 10.5600004196167px;
  font-weight: 700;
  position: absolute;
  left: 103.83px;
  top: 55.39px;
}
.bachelor-of-business-administration-b-b-a {
  color: #000000;
  text-align: center;
  font-family: "ArialCe-Bold", sans-serif;
  font-size: 10.5600004196167px;
  letter-spacing: 0.005em;
  font-weight: 700;
  position: absolute;
  left: 108.72px;
  top: 69.13px;
}
.in-management-informationsystem-mba-mis {
  color: #000000;
  text-align: center;
  font-family: "ArialCe-Bold", sans-serif;
  font-size: 10.5600004196167px;
  letter-spacing: 0.005em;
  font-weight: 700;
  position: absolute;
  left: 108.72px;
  top: 82.13px;
}
.iau {
  color: #578cc9;
  text-align: center;
  font-family: "ArialCe-Bold", sans-serif;
  font-size: 10.5600004196167px;
  font-weight: 700;
  position: absolute;
  left: 82.32px;
  top: 55.39px;
}
.frame-1171276311 {
  display: flex;
  flex-direction: column;
  gap: 9px;
  align-items: flex-start;
  justify-content: flex-start;
  width: 468px;
  position: absolute;
  left: 80.51px;
  top: 107px;
}
.container {
  display: flex;
  flex-direction: column;
  gap: 0px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.header {
  background: #bfbfbf;
  border-style: solid;
  border-color: #000000;
  border-width: 0.72px;
  padding: 10px 2px 10px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  height: 11px;
  position: relative;
}
.title {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Bold", sans-serif;
  font-size: 7.559999942779541px;
  letter-spacing: 0.002em;
  font-weight: 700;
  position: relative;
}
.name-fields {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.first-name-field {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0.72px;
  padding: 2px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 152px;
  height: 12px;
  position: relative;
}
.first-name-label {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Regular", sans-serif;
  font-size: 6px;
  letter-spacing: 0.002em;
  font-weight: 400;
  position: relative;
}
.middle-name-field {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 2px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 85px;
  height: 12px;
  position: relative;
}
.middle-name-label {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Regular", sans-serif;
  font-size: 6px;
  letter-spacing: 0.002em;
  font-weight: 400;
  position: relative;
}
.last-name-field {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 2px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 231px;
  height: 12px;
  position: relative;
}
.last-name-label {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Regular", sans-serif;
  font-size: 6px;
  letter-spacing: 0.002em;
  font-weight: 400;
  position: relative;
}
.name-values {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.first-name-value {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0.72px;
  padding: 1px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 152px;
  height: 10px;
  position: relative;
}
.first-name-text {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Regular", sans-serif;
  font-size: 6px;
  letter-spacing: 0.002em;
  font-weight: 400;
  position: relative;
}
.middle-name-value {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 1px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 85px;
  height: 10px;
  position: relative;
}
.middle-name-text {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Regular", sans-serif;
  font-size: 6px;
  letter-spacing: 0.002em;
  font-weight: 400;
  position: relative;
}
.last-name-value {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 1px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 231px;
  height: 10px;
  position: relative;
}
.last-name-text {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Regular", sans-serif;
  font-size: 6px;
  letter-spacing: 0.002em;
  font-weight: 400;
  position: relative;
}
.course-list {
  display: flex;
  flex-direction: column;
  gap: 0px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.general-education {
  display: flex;
  flex-direction: column;
  gap: 0px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.section-title {
  background: #bfbfbf;
  border-style: solid;
  border-color: #000000;
  border-width: 0.72px;
  padding: 10px 2px 10px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  height: 11px;
  position: relative;
}
.section-description {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Bold", sans-serif;
  font-size: 7.559999942779541px;
  letter-spacing: 0.002em;
  font-weight: 700;
  position: relative;
}
.table-header {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.header-column {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0.72px;
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 154px;
  height: 16px;
  position: relative;
}
.column-title {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Bold", sans-serif;
  font-size: 6px;
  letter-spacing: 0.002em;
  font-weight: 700;
  position: relative;
}
.header-column2 {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 30px;
  height: 16px;
  position: relative;
}
.header-column3 {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 55px;
  height: 16px;
  position: relative;
}
.header-column4 {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 135px;
  height: 16px;
  position: relative;
}
.header-column5 {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 44px;
  height: 16px;
  position: relative;
}
.header-column6 {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 50px;
  height: 16px;
  position: relative;
}
.table-row {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.row-column {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0.72px;
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 10px;
  height: 12px;
  position: relative;
}
.row-number {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Regular", sans-serif;
  font-size: 6px;
  letter-spacing: 0.002em;
  font-weight: 400;
  position: relative;
}
.row-column2 {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 144px;
  height: 12px;
  position: relative;
}
.course-title {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Regular", sans-serif;
  font-size: 6px;
  letter-spacing: 0.002em;
  font-weight: 400;
  position: relative;
}
.row-column3 {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 30px;
  height: 12px;
  position: relative;
}
.semester-hours {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Regular", sans-serif;
  font-size: 6px;
  letter-spacing: 0.002em;
  font-weight: 400;
  position: relative;
}
.row-column4 {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 55px;
  height: 12px;
  position: relative;
}
.row-column5 {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 135px;
  height: 12px;
  position: relative;
}
.row-column6 {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 44px;
  height: 12px;
  position: relative;
}
.row-column7 {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 50px;
  height: 12px;
  position: relative;
}
.credit {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Regular", sans-serif;
  font-size: 6px;
  letter-spacing: 0.002em;
  font-weight: 400;
  position: relative;
}
.header-column7 {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0.72px;
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 184px;
  height: 16px;
  position: relative;
}
.row-column8 {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0px;
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 174px;
  height: 12px;
  position: relative;
}
.frame-1171276425 {
  display: flex;
  flex-direction: column;
  gap: 0px;
  align-items: flex-start;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 468px;
  position: relative;
}
.notes {
  background: #bfbfbf;
  border-style: solid;
  border-color: #000000;
  border-width: 0.72px;
  padding: 10px 2px 10px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  height: 11px;
  position: relative;
}
.notes-text {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Bold", sans-serif;
  font-size: 7.559999942779541px;
  letter-spacing: 0.002em;
  font-weight: 700;
  position: relative;
}
.notes-row {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  height: 10px;
  position: relative;
}
.notes-row2 {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0.72px 0.72px 0.72px;
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  flex: 1;
  height: 10px;
  position: relative;
}
.enrollment-instructions {
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  width: 467px;
  position: absolute;
  left: 81px;
  top: 533px;
}
.enrollment-instructions-text {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Italic", sans-serif;
  font-size: 6px;
  line-height: 8px;
  letter-spacing: 0.002em;
  font-weight: 400;
  font-style: italic;
  position: relative;
}
.conditional-credits {
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  width: 467px;
  position: absolute;
  left: 81px;
  top: 553px;
}
.conditional-credits-text {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Italic", sans-serif;
  font-size: 6px;
  line-height: 8px;
  letter-spacing: 0.002em;
  font-weight: 400;
  font-style: italic;
  position: relative;
}
.evaluator {
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 80px;
  top: 608px;
}
.evaluator-text {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Regular", sans-serif;
  font-size: 6px;
  line-height: 8px;
  letter-spacing: 0.002em;
  font-weight: 400;
  position: relative;
}
.evaluator-name {
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  width: 464px;
  position: absolute;
  left: 80.51px;
  top: 650.38px;
}
.evaluator-name-text {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Regular", sans-serif;
  font-size: 6px;
  line-height: 8px;
  letter-spacing: 0.002em;
  font-weight: 400;
  position: relative;
}
.director {
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  width: 464px;
  position: absolute;
  left: 80.51px;
  top: 661.57px;
}
.director-text {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Regular", sans-serif;
  font-size: 6px;
  line-height: 8px;
  letter-spacing: 0.002em;
  font-weight: 400;
  position: relative;
}
.office {
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  width: 464px;
  position: absolute;
  left: 80.51px;
  top: 672.75px;
}
.office-text {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Regular", sans-serif;
  font-size: 6px;
  line-height: 8px;
  letter-spacing: 0.002em;
  font-weight: 400;
  position: relative;
}
.report-end {
  padding: 0px 116px 0px 116px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  width: 467px;
  position: absolute;
  left: 81px;
  top: 576px;
}
.report-end-text {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-BoldItalic", sans-serif;
  font-size: 6px;
  line-height: 8px;
  letter-spacing: 0.002em;
  font-weight: 700;
  font-style: italic;
  position: relative;
}
.date {
  padding: 0px 169px 0px 169px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  width: 464px;
  position: absolute;
  left: 78px;
  top: 586px;
}
.date-text {
  color: #000000;
  text-align: left;
  font-family: "ArialCe-Regular", sans-serif;
  font-size: 6px;
  line-height: 8px;
  letter-spacing: 0.002em;
  font-weight: 400;
  position: relative;
}
.vector-157 {
  width: 467.5px;
  height: 0px;
  position: absolute;
  left: 81.16px;
  top: 584.62px;
  overflow: visible;
}
.signature {
  width: 17.14%;
  height: 2.39%;
  position: absolute;
  right: 69.71%;
  left: 13.16%;
  bottom: 18.96%;
  top: 78.65%;
  object-fit: cover;
}
