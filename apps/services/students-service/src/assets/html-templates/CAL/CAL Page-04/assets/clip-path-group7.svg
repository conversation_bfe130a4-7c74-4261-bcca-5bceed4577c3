<svg width="14" height="5" viewBox="0 0 14 5" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_11085_13098" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="14" height="5">
<path d="M0.621094 0.172363H13.3032V4.92486H0.621094V0.172363Z" fill="white"/>
</mask>
<g mask="url(#mask0_11085_13098)">
<mask id="mask1_11085_13098" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="14" height="5">
<path d="M12.2782 0.361133C11.4695 0.0573831 10.434 0.1428 9.11362 0.621966C7.79695 1.09947 6.37195 1.91697 5.27779 2.57572C4.36112 3.12697 3.42904 3.44197 2.50654 3.51113C2.37529 3.52113 2.24404 3.52613 2.1132 3.52613C1.61029 3.52613 1.11279 3.4528 0.626953 3.30822C0.954453 3.90697 1.36987 4.45155 1.8557 4.92363C3.09779 4.7003 5.00154 4.17655 6.01237 3.85697C6.55445 3.68572 7.11529 3.50822 7.6932 3.3478C8.64862 3.08322 9.44904 3.00988 10.0732 3.12988C10.8603 3.28155 11.199 3.70405 11.3445 4.03197C11.4428 4.25405 11.4836 4.50113 11.4682 4.76322C12.422 3.7728 13.0774 2.4928 13.2899 1.06863C12.9995 0.740716 12.6636 0.506133 12.2782 0.361133Z" fill="white"/>
</mask>
<g mask="url(#mask1_11085_13098)">
<mask id="mask2_11085_13098" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="14" height="5">
<path d="M12.2782 0.361133C11.4695 0.0573831 10.434 0.1428 9.11362 0.621966C7.79695 1.09947 6.37195 1.91697 5.27779 2.57572C4.36112 3.12697 3.42904 3.44197 2.50654 3.51113C2.37529 3.52113 2.24404 3.52613 2.1132 3.52613C1.61029 3.52613 1.11279 3.4528 0.626953 3.30822C0.954453 3.90697 1.36987 4.45155 1.8557 4.92363C3.09779 4.7003 5.00154 4.17655 6.01237 3.85697C6.55445 3.68572 7.11529 3.50822 7.6932 3.3478C8.64862 3.08322 9.44904 3.00988 10.0732 3.12988C10.8603 3.28155 11.199 3.70405 11.3445 4.03197C11.4428 4.25405 11.4836 4.50113 11.4682 4.76322C12.422 3.7728 13.0774 2.4928 13.2899 1.06863C12.9995 0.740716 12.6636 0.506133 12.2782 0.361133Z" fill="white"/>
</mask>
<g mask="url(#mask2_11085_13098)">
<mask id="mask3_11085_13098" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="-1" y="-4" width="16" height="13">
<path d="M14.734 4.44946L12.0544 -3.69971L-0.816406 0.532376L1.86276 8.68154L14.734 4.44946Z" fill="white"/>
</mask>
<g mask="url(#mask3_11085_13098)">
<path d="M14.6989 4.46004L12.0535 -3.58496L-0.783203 0.636291L1.86221 8.68129L14.6989 4.46004Z" fill="url(#paint0_radial_11085_13098)"/>
</g>
</g>
</g>
</g>
<defs>
<radialGradient id="paint0_radial_11085_13098" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(13.355 1.08943) rotate(161.798) scale(12.1029 12.1029)">
<stop stop-color="#40B93C"/>
<stop offset="0.125" stop-color="#3FB83C"/>
<stop offset="0.234375" stop-color="#3FB83C"/>
<stop offset="0.28125" stop-color="#3FB73C"/>
<stop offset="0.300781" stop-color="#3EB63C"/>
<stop offset="0.308594" stop-color="#3EB53C"/>
<stop offset="0.3125" stop-color="#3EB53C"/>
<stop offset="0.316406" stop-color="#3DB43C"/>
<stop offset="0.320312" stop-color="#3DB43C"/>
<stop offset="0.324219" stop-color="#3DB33C"/>
<stop offset="0.328125" stop-color="#3DB23C"/>
<stop offset="0.332031" stop-color="#3CB13C"/>
<stop offset="0.335938" stop-color="#3CB13C"/>
<stop offset="0.339844" stop-color="#3CB03C"/>
<stop offset="0.347656" stop-color="#3BAF3C"/>
<stop offset="0.351562" stop-color="#3BAF3C"/>
<stop offset="0.355469" stop-color="#3BAE3C"/>
<stop offset="0.359375" stop-color="#3AAD3C"/>
<stop offset="0.367188" stop-color="#3AAC3C"/>
<stop offset="0.375" stop-color="#3AAC3C"/>
<stop offset="0.382812" stop-color="#39AB3C"/>
<stop offset="0.390625" stop-color="#39AA3C"/>
<stop offset="0.394531" stop-color="#39A93C"/>
<stop offset="0.398438" stop-color="#39A73C"/>
<stop offset="0.40625" stop-color="#38A63C"/>
<stop offset="0.414062" stop-color="#38A53C"/>
<stop offset="0.421875" stop-color="#37A43C"/>
<stop offset="0.429688" stop-color="#37A43C"/>
<stop offset="0.4375" stop-color="#36A33C"/>
<stop offset="0.441406" stop-color="#36A23C"/>
<stop offset="0.445312" stop-color="#36A13C"/>
<stop offset="0.453125" stop-color="#35A03C"/>
<stop offset="0.460938" stop-color="#35A03B"/>
<stop offset="0.46875" stop-color="#349F3B"/>
<stop offset="0.476562" stop-color="#349E3B"/>
<stop offset="0.480469" stop-color="#339D3B"/>
<stop offset="0.484375" stop-color="#339C3B"/>
<stop offset="0.492188" stop-color="#339B3B"/>
<stop offset="0.507812" stop-color="#329A3B"/>
<stop offset="0.511719" stop-color="#32993B"/>
<stop offset="0.515625" stop-color="#32993B"/>
<stop offset="0.519531" stop-color="#31983B"/>
<stop offset="0.523438" stop-color="#31973B"/>
<stop offset="0.527344" stop-color="#31963B"/>
<stop offset="0.53125" stop-color="#31963B"/>
<stop offset="0.535156" stop-color="#30953B"/>
<stop offset="0.539062" stop-color="#30953B"/>
<stop offset="0.542969" stop-color="#30943B"/>
<stop offset="0.546875" stop-color="#30943B"/>
<stop offset="0.550781" stop-color="#30933B"/>
<stop offset="0.554688" stop-color="#2F933B"/>
<stop offset="0.558594" stop-color="#2F933B"/>
<stop offset="0.5625" stop-color="#2F923B"/>
<stop offset="0.566406" stop-color="#2F923B"/>
<stop offset="0.570312" stop-color="#2F913B"/>
<stop offset="0.574219" stop-color="#2E913B"/>
<stop offset="0.578125" stop-color="#2E903B"/>
<stop offset="0.582031" stop-color="#2E903A"/>
<stop offset="0.585938" stop-color="#2E8F3A"/>
<stop offset="0.589844" stop-color="#2E8F3A"/>
<stop offset="0.59375" stop-color="#2D8E3A"/>
<stop offset="0.597656" stop-color="#2D8E3A"/>
<stop offset="0.601562" stop-color="#2D8D3A"/>
<stop offset="0.605469" stop-color="#2D8C3A"/>
<stop offset="0.609375" stop-color="#2C8C3A"/>
<stop offset="0.613281" stop-color="#2C8B3A"/>
<stop offset="0.617188" stop-color="#2C8B3A"/>
<stop offset="0.621094" stop-color="#2C8A3A"/>
<stop offset="0.625" stop-color="#2C893A"/>
<stop offset="0.636719" stop-color="#2B883A"/>
<stop offset="0.644531" stop-color="#2B883A"/>
<stop offset="0.648438" stop-color="#2A873A"/>
<stop offset="0.652344" stop-color="#2A863A"/>
<stop offset="0.660156" stop-color="#2A8539"/>
<stop offset="0.667969" stop-color="#2A8539"/>
<stop offset="0.675781" stop-color="#298439"/>
<stop offset="0.683594" stop-color="#298339"/>
<stop offset="0.6875" stop-color="#288239"/>
<stop offset="0.691406" stop-color="#288139"/>
<stop offset="0.699219" stop-color="#288139"/>
<stop offset="0.707031" stop-color="#288039"/>
<stop offset="0.714844" stop-color="#277F39"/>
<stop offset="0.722656" stop-color="#277E39"/>
<stop offset="0.726562" stop-color="#277D39"/>
<stop offset="0.730469" stop-color="#267C39"/>
<stop offset="0.738281" stop-color="#267B38"/>
<stop offset="0.753906" stop-color="#267A38"/>
<stop offset="0.757812" stop-color="#257A38"/>
<stop offset="0.769531" stop-color="#257938"/>
<stop offset="0.773438" stop-color="#247838"/>
<stop offset="0.777344" stop-color="#247838"/>
<stop offset="0.78125" stop-color="#247838"/>
<stop offset="0.785156" stop-color="#247738"/>
<stop offset="0.789062" stop-color="#247738"/>
<stop offset="0.792969" stop-color="#247638"/>
<stop offset="0.796875" stop-color="#237637"/>
<stop offset="0.800781" stop-color="#237537"/>
<stop offset="0.804688" stop-color="#237537"/>
<stop offset="0.808594" stop-color="#237437"/>
<stop offset="0.8125" stop-color="#237437"/>
<stop offset="0.816406" stop-color="#227337"/>
<stop offset="0.824219" stop-color="#227237"/>
<stop offset="0.832031" stop-color="#217137"/>
<stop offset="0.839844" stop-color="#217037"/>
<stop offset="0.847656" stop-color="#217037"/>
<stop offset="0.851562" stop-color="#216F36"/>
<stop offset="0.855469" stop-color="#206E36"/>
<stop offset="0.863281" stop-color="#206D36"/>
<stop offset="0.878906" stop-color="#206D36"/>
<stop offset="0.882812" stop-color="#206C36"/>
<stop offset="0.886719" stop-color="#206C36"/>
<stop offset="0.890625" stop-color="#1F6B36"/>
<stop offset="0.894531" stop-color="#1F6B36"/>
<stop offset="0.898438" stop-color="#1F6B35"/>
<stop offset="0.902344" stop-color="#1F6A35"/>
<stop offset="0.90625" stop-color="#1F6A35"/>
<stop offset="0.910156" stop-color="#1E6935"/>
<stop offset="0.917969" stop-color="#1E6835"/>
<stop offset="0.925781" stop-color="#1E6735"/>
<stop offset="0.933594" stop-color="#1E6735"/>
<stop offset="0.9375" stop-color="#1D6635"/>
<stop offset="0.945312" stop-color="#1D6535"/>
<stop offset="0.949219" stop-color="#1D6535"/>
<stop offset="0.953125" stop-color="#1D6434"/>
<stop offset="1" stop-color="#1D6434"/>
</radialGradient>
</defs>
</svg>
