.cal-page-04,
.cal-page-04 * {
  box-sizing: border-box;
}
.cal-page-04 {
  background: #ffffff;
  height: 842px;
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: center;
}
.icon {
  width: 400px;
  height: 400px;
  position: absolute;
  left: 50%;
  translate: -50% -50%;
  top: 50%;
  object-fit: cover;
  aspect-ratio: 1;
}
.main-container {
  display: flex;
  flex-direction: column;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 595px;
  height: 842px;
}
.logo-and-address {
  border-style: solid;
  border-color: var(--light-theme-tertiary-color, #1952bb);
  border-width: 0px 0px 1px 0px;
  padding: 24px 48px 0px 48px;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: space-between;
  align-self: stretch;
  flex-shrink: 0;
  height: 90px;
  position: relative;
}
.horizontal-container {
  display: flex;
  flex-direction: row;
  gap: 24px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex: 1;
  position: relative;
}
.group {
  flex-shrink: 0;
  width: 52px;
  height: 52px;
  position: relative;
  overflow: visible;
  aspect-ratio: 1;
}
.name-with-address {
  display: flex;
  flex-direction: column;
  gap: 6px;
  align-items: flex-start;
  justify-content: center;
  flex: 1;
  position: relative;
}
.text-input {
  display: flex;
  flex-direction: row;
  gap: 14px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  position: relative;
}
.international-american-university {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-SemiBold", sans-serif;
  font-size: 16px;
  line-height: 24px;
  font-weight: 600;
  position: relative;
}
.address {
  display: flex;
  flex-direction: row;
  gap: 14px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  position: relative;
}
.phone {
  display: flex;
  flex-direction: row;
  gap: 4px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  height: 15px;
  position: relative;
}
.call {
  flex-shrink: 0;
  width: 13px;
  height: 13px;
  position: relative;
  overflow: visible;
  aspect-ratio: 1;
}
.text {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Regular", sans-serif;
  font-size: 10px;
  line-height: 15px;
  font-weight: 400;
  position: relative;
}
.phone2 {
  display: flex;
  flex-direction: row;
  gap: 4px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  position: relative;
}
.language {
  flex-shrink: 0;
  width: 13px;
  height: 13px;
  position: relative;
  overflow: visible;
  aspect-ratio: 1;
}
.mail {
  flex-shrink: 0;
  width: 13px;
  height: 13px;
  position: relative;
  overflow: visible;
  aspect-ratio: 1;
}
.header {
  padding: 0px 48px 0px 48px;
  display: flex;
  flex-direction: column;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex: 1;
  position: relative;
}
.container {
  display: flex;
  flex-direction: column;
  gap: 14px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex: 1;
  position: relative;
}
.heading {
  border-style: solid;
  border-color: var(--light-theme-grey07, #19191c);
  border-width: 0px 0px 1px 0px;
  padding: 10px 0px 10px 0px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.refund-policy {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-SemiBold", sans-serif;
  font-size: 16px;
  line-height: 24px;
  font-weight: 600;
  position: relative;
}
.paragraph {
  display: flex;
  flex-direction: column;
  gap: 14px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.paragraph2 {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.refund-procedure-at-apply-goal-hereinafter-agreement-prescribes-the-procedure-by-which-the-tuition-fees-paid-by-the-students-shall-be-refunded-by-apply-goal-apply-goal-is-a-global-recruitment-partner-of-iau-all-the-students-must-comply-with-the-below-policies {
  color: var(--light-theme-grey07, #19191c);
  text-align: justified;
  font-family: "Inter-Regular", sans-serif;
  font-size: 9px;
  line-height: 12px;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.not-refund {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.applygoal-will-not-refund-the-tuition-fees-paid-by-the-student-if {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-SemiBold", sans-serif;
  font-size: 11px;
  line-height: 13px;
  font-weight: 600;
  position: relative;
  flex: 1;
}
.list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.the-student-pays-the-tuition-fee-after-getting-an-f-i-visa-with-iau-s-form-1-20-enters-into-the-usa {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Regular", sans-serif;
  font-size: 9px;
  line-height: 12px;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.the-student-fails-to-check-in-with-iau-within-10-days-after-entering-the-us-fails-to-register-for-classes-by-deadline-dates-fails-to-commence-studies-on-the-official-start-dates-or-fails-to-comply-with-any-uscis-regulations-or-iau-policies-resulting-in-expulsion-or-withdrawal-from-iau {
  color: var(--light-theme-grey07, #19191c);
  text-align: justified;
  font-family: "Inter-Regular", sans-serif;
  font-size: 9px;
  line-height: 12px;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.refund {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.applygoal-will-refund-the-tuition-fees-paid-by-the-student-if {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-SemiBold", sans-serif;
  font-size: 11px;
  line-height: 13px;
  font-weight: 600;
  position: relative;
  flex: 1;
}
.iau-cancels-the-implementation-of-the-education-program-due-to-an-insufficient-number-of-students-in-this-case-the-student-is-entitled-to-have-the-tuition-fees-paid-in-advance-reimbursed-in-their-entirety {
  color: var(--light-theme-grey07, #19191c);
  text-align: justified;
  font-family: "Inter-Regular", sans-serif;
  font-size: 9px;
  line-height: 12px;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.the-implementation-of-the-education-program-is-temporarily-suspended-or-withdrawn-completely-without-offering-the-student-an-alternative-of-continuing-education-at-another-program-or-at-another-accredited-educational-institution-that-has-an-agreement-with-iau-in-this-case-the-student-is-entitled-to-receive-the-refund-of-the-tuition-fees-that-have-been-paid-for-the-non-delivered-classes-the-amenint-of-the-reimbursement-is-calculated-by-apply-goal {
  color: var(--light-theme-grey07, #19191c);
  text-align: justified;
  font-family: "Inter-Regular", sans-serif;
  font-size: 9px;
  line-height: 12px;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.the-student-withdraws-the-registration-enrollment-from-the-study-before-the-student-enters-the-usa-with-a-form-1-20-from-1-au-and-f-1-visa-for-iau-if-the-student-comes-to-the-usa-with-a-form-1-20-from-iau-and-f-1-visa-for-lau-then-students-cannot-request-a-refund-under-any-circumstances {
  color: var(--light-theme-grey07, #19191c);
  text-align: justified;
  font-family: "Inter-Regular", sans-serif;
  font-size: 9px;
  line-height: 12px;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.the-student-petitions-in-writing-for-a-refund-for-reasons-not-stipulated-in-this-agreement-in-this-case-apply-goal-will-assess-the-explanation-as-determined-by-its-board-of-directors {
  color: var(--light-theme-grey07, #19191c);
  text-align: justified;
  font-family: "Inter-Regular", sans-serif;
  font-size: 9px;
  line-height: 12px;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.apply-goal-will-refund-the-tuition-fees-paid-by-the-student-through-bank-transfer-directly-to-the-bank-account-specified-in-the-registration-form-submitted-by-the-student-student-to-provide-complete-and-precise-information-about-the-account-holder-and-the-corresponding-financial-institution {
  color: var(--light-theme-grey07, #19191c);
  text-align: justified;
  font-family: "Inter-Regular", sans-serif;
  font-size: 9px;
  line-height: 12px;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.refund-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.paragraph3 {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.text2 {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 98px;
  position: relative;
}
.company {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Medium", sans-serif;
  font-size: 9px;
  line-height: 12px;
  font-weight: 500;
  position: relative;
}
.text3 {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  position: relative;
}
.apply-goal-inc {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Medium", sans-serif;
  font-size: 9px;
  line-height: 12px;
  font-weight: 500;
  position: relative;
}
.account-number {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Medium", sans-serif;
  font-size: 9px;
  line-height: 12px;
  font-weight: 500;
  position: relative;
}
._325199210855 {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Medium", sans-serif;
  font-size: 9px;
  line-height: 12px;
  font-weight: 500;
  position: relative;
}
.ach-routing {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Medium", sans-serif;
  font-size: 9px;
  line-height: 12px;
  font-weight: 500;
  position: relative;
}
._121000358 {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Medium", sans-serif;
  font-size: 9px;
  line-height: 12px;
  font-weight: 500;
  position: relative;
}
.bank {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Medium", sans-serif;
  font-size: 9px;
  line-height: 12px;
  font-weight: 500;
  position: relative;
}
.bank-of-america {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Medium", sans-serif;
  font-size: 9px;
  line-height: 12px;
  font-weight: 500;
  position: relative;
}
.swift {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Medium", sans-serif;
  font-size: 9px;
  line-height: 12px;
  font-weight: 500;
  position: relative;
}
.bofaus-3-nxxx-usa-bofaus-6-sxxx-foreign {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Medium", sans-serif;
  font-size: 9px;
  line-height: 12px;
  font-weight: 500;
  position: relative;
}
.text4 {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 98px;
  position: relative;
}
.account-currency {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Medium", sans-serif;
  font-size: 9px;
  line-height: 12px;
  font-weight: 500;
  position: relative;
}
.usd {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Medium", sans-serif;
  font-size: 9px;
  line-height: 12px;
  font-weight: 500;
  position: relative;
}
.address2 {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Medium", sans-serif;
  font-size: 9px;
  line-height: 12px;
  font-weight: 500;
  position: relative;
}
._3320-w-olympic-blvd-los-angeles-ca-90019 {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Medium", sans-serif;
  font-size: 9px;
  line-height: 12px;
  font-weight: 500;
  position: relative;
}
.you-can-email-to-iaccounts-iaula-edu-to-enquiry-about-the-payment-refund-policy {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Regular", sans-serif;
  font-size: 9px;
  line-height: 12px;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.footer {
  background: var(--light-theme-primary-shade-04, #e9f0ff);
  padding: 0px 48px 24px 48px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  height: 90px;
  position: relative;
}
.container2 {
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.terms-container {
  border-style: solid;
  border-color: var(--light-theme-grey07, #19191c);
  border-width: 0px 0px 1px 0px;
  padding: 10px 0px 10px 0px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.terms-text {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Medium", sans-serif;
  font-size: 10px;
  line-height: 14px;
  font-weight: 500;
  position: relative;
}
.bank-logos {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 499px;
  height: 26px;
  position: relative;
}
.logo {
  flex-shrink: 0;
  width: 62px;
  height: 26px;
  position: relative;
  overflow: hidden;
}
.tracs-logo-main-1 {
  width: 64.52%;
  height: 76.92%;
  position: absolute;
  right: 17.74%;
  left: 17.74%;
  bottom: 11.54%;
  top: 11.54%;
  overflow: hidden;
  aspect-ratio: 2/1;
}
.clip-path-group {
  height: auto;
  position: absolute;
  left: 0.05px;
  top: 0px;
  overflow: visible;
}
.clip-path-group2 {
  height: auto;
  position: absolute;
  left: 3.96px;
  top: 3.93px;
  overflow: visible;
}
.clip-path-group3 {
  height: auto;
  position: absolute;
  left: 5.04px;
  top: 14.34px;
  overflow: visible;
}
.clip-path-group4 {
  height: auto;
  position: absolute;
  left: 3.36px;
  top: 12.86px;
  overflow: visible;
}
.clip-path-group5 {
  height: auto;
  position: absolute;
  left: 1.5px;
  top: 9.16px;
  overflow: visible;
}
.clip-path-group6 {
  height: auto;
  position: absolute;
  left: 1.38px;
  top: 6.29px;
  overflow: visible;
}
.clip-path-group7 {
  height: auto;
  position: absolute;
  left: 1.48px;
  top: 4.48px;
  overflow: visible;
}
.clip-path-group8 {
  height: auto;
  position: absolute;
  left: 2.12px;
  top: 10.1px;
  overflow: visible;
}
.clip-path-group9 {
  height: auto;
  position: absolute;
  left: 1.3px;
  top: 7.25px;
  overflow: visible;
}
.clip-path-group10 {
  height: auto;
  position: absolute;
  left: 5.72px;
  top: 13.92px;
  overflow: visible;
}
.clip-path-group11 {
  height: auto;
  position: absolute;
  left: 4.34px;
  top: 3.21px;
  overflow: visible;
}
.group2 {
  height: auto;
  position: absolute;
  left: 15.65px;
  top: 4.52px;
  overflow: visible;
}
.group3 {
  height: auto;
  position: absolute;
  left: 20.12px;
  top: 4.52px;
  overflow: visible;
}
.group4 {
  height: auto;
  position: absolute;
  left: 24.31px;
  top: 4.52px;
  overflow: visible;
}
.group5 {
  height: auto;
  position: absolute;
  left: 29.38px;
  top: 4.46px;
  overflow: visible;
}
.group6 {
  height: auto;
  position: absolute;
  left: 33.98px;
  top: 4.46px;
  overflow: visible;
}
.group7 {
  height: auto;
  position: absolute;
  left: 15.77px;
  top: 11.33px;
  overflow: visible;
}
.group8 {
  height: auto;
  position: absolute;
  left: 17.62px;
  top: 11.33px;
  overflow: visible;
}
.group9 {
  height: auto;
  position: absolute;
  left: 19.03px;
  top: 11.33px;
  overflow: visible;
}
.group10 {
  height: auto;
  position: absolute;
  left: 20.32px;
  top: 11.33px;
  overflow: visible;
}
.group11 {
  height: auto;
  position: absolute;
  left: 21.78px;
  top: 11.33px;
  overflow: visible;
}
.group12 {
  height: auto;
  position: absolute;
  left: 22.65px;
  top: 11.33px;
  overflow: visible;
}
.group13 {
  height: auto;
  position: absolute;
  left: 24.21px;
  top: 11.31px;
  overflow: visible;
}
.group14 {
  height: auto;
  position: absolute;
  left: 26.51px;
  top: 11.33px;
  overflow: visible;
}
.group15 {
  height: auto;
  position: absolute;
  left: 29.02px;
  top: 11.33px;
  overflow: visible;
}
.group16 {
  height: auto;
  position: absolute;
  left: 30.65px;
  top: 11.33px;
  overflow: visible;
}
.group17 {
  height: auto;
  position: absolute;
  left: 31.45px;
  top: 11.31px;
  overflow: visible;
}
.group18 {
  height: auto;
  position: absolute;
  left: 33.09px;
  top: 11.33px;
  overflow: visible;
}
.group19 {
  height: auto;
  position: absolute;
  left: 34.72px;
  top: 11.33px;
  overflow: visible;
}
.group20 {
  height: auto;
  position: absolute;
  left: 36.13px;
  top: 11.33px;
  overflow: visible;
}
.group21 {
  height: auto;
  position: absolute;
  left: 20.49px;
  top: 13.42px;
  overflow: visible;
}
.group22 {
  height: auto;
  position: absolute;
  left: 21.81px;
  top: 13.44px;
  overflow: visible;
}
.group23 {
  height: auto;
  position: absolute;
  left: 23.12px;
  top: 13.44px;
  overflow: visible;
}
.group24 {
  height: auto;
  position: absolute;
  left: 24.83px;
  top: 13.44px;
  overflow: visible;
}
.group25 {
  height: auto;
  position: absolute;
  left: 26.45px;
  top: 13.44px;
  overflow: visible;
}
.group26 {
  height: auto;
  position: absolute;
  left: 27.96px;
  top: 13.44px;
  overflow: visible;
}
.group27 {
  height: auto;
  position: absolute;
  left: 29.67px;
  top: 13.44px;
  overflow: visible;
}
.group28 {
  height: auto;
  position: absolute;
  left: 31.18px;
  top: 13.44px;
  overflow: visible;
}
.logo-of-the-united-states-immigration-and-customs-enforcement-agency-1 {
  width: 50px;
  height: 15px;
  position: absolute;
  left: 50%;
  translate: -50% -50%;
  top: 50%;
  overflow: hidden;
  aspect-ratio: 50/15;
}
.vector {
  width: 29.8%;
  height: 99.35%;
  position: absolute;
  right: 69.96%;
  left: 0.24%;
  bottom: 0.33%;
  top: 0.33%;
  overflow: visible;
}
.group29 {
  height: auto;
  position: absolute;
  left: 0.12px;
  top: 0.05px;
  overflow: visible;
}
.uk-enic-1 {
  width: 45.77px;
  height: 14px;
  position: absolute;
  left: 8.11px;
  top: 6px;
  overflow: hidden;
  aspect-ratio: 45.77/14;
}
.layer-1 {
  height: auto;
  position: absolute;
  left: 0px;
  top: 0px;
  overflow: visible;
}
.csaave {
  width: 46.67px;
  height: 20px;
  position: absolute;
  left: 7.66px;
  top: 3px;
  overflow: hidden;
  aspect-ratio: 46.67/20;
}
.layer-12 {
  height: auto;
  position: absolute;
  left: 0px;
  top: 0px;
  overflow: visible;
}
.bppe-logo-1 {
  width: 20px;
  height: 20px;
  position: absolute;
  left: 50%;
  translate: -50% -50%;
  top: 50%;
  overflow: hidden;
  aspect-ratio: 1;
}
.bppe-logo-12 {
  position: absolute;
  inset: 0;
}
.bg {
  width: 100%;
  height: 100%;
  position: absolute;
  right: 0%;
  left: 0%;
  bottom: 0%;
  top: 0%;
  overflow: visible;
}
.group30 {
  width: 92.65%;
  height: 51.91%;
  position: absolute;
  right: 3.7%;
  left: 3.65%;
  bottom: 43.93%;
  top: 4.15%;
  overflow: visible;
}
.group31 {
  width: 92.8%;
  height: 19.04%;
  position: absolute;
  right: 3.65%;
  left: 3.56%;
  bottom: 16.49%;
  top: 64.48%;
  overflow: visible;
}
.group32 {
  width: 18.53px;
  height: 1.21px;
  position: absolute;
  left: 0.73px;
  top: 18.04px;
  overflow: visible;
}
.sevp {
  height: auto;
  position: absolute;
  left: 18.89px;
  top: 6px;
  overflow: visible;
  aspect-ratio: 24.23/14;
}
.council-for-higher-education-accreditation-logo-1 {
  width: 45.65px;
  height: 14px;
  position: absolute;
  left: 50%;
  translate: -50% -50%;
  top: 50%;
  overflow: hidden;
  aspect-ratio: 45.65/14;
}
.group33 {
  height: auto;
  position: absolute;
  left: 0px;
  top: 0.02px;
  overflow: visible;
}
.acbsp-seeklogo-1 {
  width: 22px;
  height: 22px;
  position: absolute;
  left: 50%;
  translate: -50% -50%;
  top: 50%;
  overflow: hidden;
  aspect-ratio: 1;
}
.group34 {
  width: 76.08%;
  height: 97.13%;
  position: absolute;
  right: 11.96%;
  left: 11.96%;
  bottom: 1.43%;
  top: 1.43%;
  overflow: visible;
  aspect-ratio: 16.74/21.37;
}
