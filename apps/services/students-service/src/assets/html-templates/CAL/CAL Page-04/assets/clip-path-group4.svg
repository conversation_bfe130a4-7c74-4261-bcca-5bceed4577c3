<svg width="14" height="4" viewBox="0 0 14 4" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_11085_13055" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="14" height="4">
<path d="M0.00390625 0.22998H13.3652V3.80123H0.00390625V0.22998Z" fill="white"/>
</mask>
<g mask="url(#mask0_11085_13055)">
<mask id="mask1_11085_13055" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="14" height="4">
<path d="M4.80266 2.19237C3.47141 2.89028 1.62641 3.35528 0.00390625 2.70653C0.0589062 2.92737 0.12474 3.14445 0.20099 3.35612C0.326823 3.41487 0.460573 3.4707 0.602239 3.52153C1.98891 4.01778 3.43474 3.82278 4.89891 2.94153C7.63141 1.29695 10.4489 -0.116802 12.5402 0.669032C12.8368 0.780698 13.1127 0.934865 13.3656 1.12986C13.1247 0.920282 12.9002 0.697365 12.6106 0.568615C10.5693 -0.338885 7.62766 0.711532 4.80266 2.19237Z" fill="white"/>
</mask>
<g mask="url(#mask1_11085_13055)">
<mask id="mask2_11085_13055" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="14" height="4">
<path d="M4.80266 2.19237C3.47141 2.89028 1.62641 3.35528 0.00390625 2.70653C0.0589062 2.92737 0.12474 3.14445 0.20099 3.35612C0.326823 3.41487 0.460573 3.4707 0.602239 3.52153C1.98891 4.01778 3.43474 3.82278 4.89891 2.94153C7.63141 1.29695 10.4489 -0.116802 12.5402 0.669032C12.8368 0.780698 13.1127 0.934865 13.3656 1.12986C13.1247 0.920282 12.9002 0.697365 12.6106 0.568615C10.5693 -0.338885 7.62766 0.711532 4.80266 2.19237Z" fill="white"/>
</mask>
<g mask="url(#mask2_11085_13055)">
<path d="M0.00390625 0.22998V3.80123H13.3652V0.22998H0.00390625Z" fill="url(#paint0_radial_11085_13055)"/>
</g>
</g>
</g>
<defs>
<radialGradient id="paint0_radial_11085_13055" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(6.68477 2.01043) scale(4.88878)">
<stop stop-color="#D3D2D2"/>
<stop offset="0.00390625" stop-color="#D2D1D2"/>
<stop offset="0.0078125" stop-color="#D2D1D1"/>
<stop offset="0.0117188" stop-color="#D1D0D1"/>
<stop offset="0.015625" stop-color="#D1D0D0"/>
<stop offset="0.0195312" stop-color="#D0CFD0"/>
<stop offset="0.0234375" stop-color="#D0CECE"/>
<stop offset="0.0273438" stop-color="#CFCECE"/>
<stop offset="0.03125" stop-color="#CFCDCD"/>
<stop offset="0.0351562" stop-color="#CECDCD"/>
<stop offset="0.0390625" stop-color="#CECDCD"/>
<stop offset="0.0429688" stop-color="#CDCCCC"/>
<stop offset="0.046875" stop-color="#CDCCCC"/>
<stop offset="0.0507812" stop-color="#CCCBCB"/>
<stop offset="0.0546875" stop-color="#CCCBCB"/>
<stop offset="0.0585938" stop-color="#CBCACA"/>
<stop offset="0.0625" stop-color="#CAC9C9"/>
<stop offset="0.0664062" stop-color="#C9C8C9"/>
<stop offset="0.0703125" stop-color="#C9C8C8"/>
<stop offset="0.0742188" stop-color="#C8C7C8"/>
<stop offset="0.078125" stop-color="#C8C7C7"/>
<stop offset="0.0820312" stop-color="#C7C6C7"/>
<stop offset="0.0859375" stop-color="#C7C6C6"/>
<stop offset="0.0898438" stop-color="#C6C6C6"/>
<stop offset="0.09375" stop-color="#C6C5C6"/>
<stop offset="0.0976562" stop-color="#C6C5C5"/>
<stop offset="0.101562" stop-color="#C5C4C5"/>
<stop offset="0.105469" stop-color="#C5C4C4"/>
<stop offset="0.109375" stop-color="#C4C3C3"/>
<stop offset="0.113281" stop-color="#C4C3C3"/>
<stop offset="0.117188" stop-color="#C3C2C2"/>
<stop offset="0.121094" stop-color="#C3C2C2"/>
<stop offset="0.125" stop-color="#C2C1C1"/>
<stop offset="0.128906" stop-color="#C2C1C1"/>
<stop offset="0.132812" stop-color="#C1C0C0"/>
<stop offset="0.136719" stop-color="#C1C0C0"/>
<stop offset="0.140625" stop-color="#C0BFBF"/>
<stop offset="0.144531" stop-color="#C0BEBE"/>
<stop offset="0.148438" stop-color="#BFBDBE"/>
<stop offset="0.152344" stop-color="#BEBDBD"/>
<stop offset="0.15625" stop-color="#BEBCBD"/>
<stop offset="0.160156" stop-color="#BDBCBC"/>
<stop offset="0.164062" stop-color="#BDBBBC"/>
<stop offset="0.167969" stop-color="#BCBBBB"/>
<stop offset="0.171875" stop-color="#BCBABB"/>
<stop offset="0.175781" stop-color="#BBBABA"/>
<stop offset="0.179688" stop-color="#BBB9BA"/>
<stop offset="0.183594" stop-color="#BAB9B9"/>
<stop offset="0.1875" stop-color="#B9B8B9"/>
<stop offset="0.191406" stop-color="#B9B8B8"/>
<stop offset="0.195312" stop-color="#B8B8B8"/>
<stop offset="0.199219" stop-color="#B8B7B7"/>
<stop offset="0.203125" stop-color="#B8B7B7"/>
<stop offset="0.207031" stop-color="#B7B6B6"/>
<stop offset="0.210938" stop-color="#B7B6B6"/>
<stop offset="0.214844" stop-color="#B6B5B5"/>
<stop offset="0.21875" stop-color="#B6B5B5"/>
<stop offset="0.222656" stop-color="#B5B4B4"/>
<stop offset="0.226562" stop-color="#B5B3B4"/>
<stop offset="0.230469" stop-color="#B4B2B3"/>
<stop offset="0.234375" stop-color="#B3B2B2"/>
<stop offset="0.238281" stop-color="#B3B1B2"/>
<stop offset="0.242188" stop-color="#B2B1B1"/>
<stop offset="0.246094" stop-color="#B2B0B1"/>
<stop offset="0.25" stop-color="#B1B0B0"/>
<stop offset="0.253906" stop-color="#B1AFB0"/>
<stop offset="0.257812" stop-color="#B0AFAF"/>
<stop offset="0.261719" stop-color="#B0AEAF"/>
<stop offset="0.265625" stop-color="#AFAEAE"/>
<stop offset="0.269531" stop-color="#AFADAE"/>
<stop offset="0.273438" stop-color="#AEADAD"/>
<stop offset="0.28125" stop-color="#ADACAC"/>
<stop offset="0.285156" stop-color="#ADACAC"/>
<stop offset="0.292969" stop-color="#ACABAB"/>
<stop offset="0.296875" stop-color="#ACAAAA"/>
<stop offset="0.300781" stop-color="#ABAAAA"/>
<stop offset="0.304688" stop-color="#ABA9A9"/>
<stop offset="0.308594" stop-color="#AAA8A9"/>
<stop offset="0.3125" stop-color="#AAA8A8"/>
<stop offset="0.316406" stop-color="#A8A7A7"/>
<stop offset="0.320312" stop-color="#A8A6A7"/>
<stop offset="0.324219" stop-color="#A8A6A6"/>
<stop offset="0.328125" stop-color="#A7A5A6"/>
<stop offset="0.332031" stop-color="#A7A5A5"/>
<stop offset="0.335938" stop-color="#A6A4A5"/>
<stop offset="0.339844" stop-color="#A6A4A5"/>
<stop offset="0.34375" stop-color="#A5A3A4"/>
<stop offset="0.347656" stop-color="#A5A3A4"/>
<stop offset="0.351562" stop-color="#A4A2A3"/>
<stop offset="0.355469" stop-color="#A3A2A2"/>
<stop offset="0.359375" stop-color="#A3A1A2"/>
<stop offset="0.363281" stop-color="#A2A1A1"/>
<stop offset="0.367188" stop-color="#A2A0A0"/>
<stop offset="0.371094" stop-color="#A1A0A0"/>
<stop offset="0.378906" stop-color="#A09F9F"/>
<stop offset="0.382812" stop-color="#A09E9F"/>
<stop offset="0.386719" stop-color="#A09E9E"/>
<stop offset="0.390625" stop-color="#9F9D9E"/>
<stop offset="0.394531" stop-color="#9F9D9D"/>
<stop offset="0.398438" stop-color="#9E9C9D"/>
<stop offset="0.402344" stop-color="#9E9C9C"/>
<stop offset="0.40625" stop-color="#9D9B9C"/>
<stop offset="0.410156" stop-color="#9D9B9B"/>
<stop offset="0.414062" stop-color="#9C9A9B"/>
<stop offset="0.417969" stop-color="#9C9A9A"/>
<stop offset="0.421875" stop-color="#9B999A"/>
<stop offset="0.425781" stop-color="#9B9999"/>
<stop offset="0.429688" stop-color="#9A9899"/>
<stop offset="0.433594" stop-color="#9A9898"/>
<stop offset="0.4375" stop-color="#999797"/>
<stop offset="0.441406" stop-color="#989697"/>
<stop offset="0.445312" stop-color="#979696"/>
<stop offset="0.449219" stop-color="#979596"/>
<stop offset="0.453125" stop-color="#969595"/>
<stop offset="0.457031" stop-color="#969494"/>
<stop offset="0.460938" stop-color="#959394"/>
<stop offset="0.464844" stop-color="#959393"/>
<stop offset="0.46875" stop-color="#949393"/>
<stop offset="0.472656" stop-color="#949293"/>
<stop offset="0.476562" stop-color="#939292"/>
<stop offset="0.480469" stop-color="#939192"/>
<stop offset="0.484375" stop-color="#929191"/>
<stop offset="0.488281" stop-color="#929091"/>
<stop offset="0.492188" stop-color="#929090"/>
<stop offset="0.496094" stop-color="#918F90"/>
<stop offset="0.503906" stop-color="#908E8F"/>
<stop offset="0.507812" stop-color="#908E8E"/>
<stop offset="0.511719" stop-color="#8F8D8E"/>
<stop offset="0.515625" stop-color="#8F8D8D"/>
<stop offset="0.519531" stop-color="#8E8C8D"/>
<stop offset="0.523438" stop-color="#8D8B8C"/>
<stop offset="0.527344" stop-color="#8D8B8B"/>
<stop offset="0.53125" stop-color="#8C8A8B"/>
<stop offset="0.535156" stop-color="#8C898A"/>
<stop offset="0.539062" stop-color="#8B8989"/>
<stop offset="0.542969" stop-color="#8B8889"/>
<stop offset="0.546875" stop-color="#8A8888"/>
<stop offset="0.550781" stop-color="#8A8788"/>
<stop offset="0.554688" stop-color="#898787"/>
<stop offset="0.558594" stop-color="#898687"/>
<stop offset="0.5625" stop-color="#888686"/>
<stop offset="0.566406" stop-color="#878586"/>
<stop offset="0.570312" stop-color="#878585"/>
<stop offset="0.574219" stop-color="#868585"/>
<stop offset="0.578125" stop-color="#868485"/>
<stop offset="0.582031" stop-color="#858484"/>
<stop offset="0.585938" stop-color="#858384"/>
<stop offset="0.589844" stop-color="#858383"/>
<stop offset="0.59375" stop-color="#848283"/>
<stop offset="0.597656" stop-color="#848282"/>
<stop offset="0.601562" stop-color="#838182"/>
<stop offset="0.605469" stop-color="#828081"/>
<stop offset="0.609375" stop-color="#828080"/>
<stop offset="0.613281" stop-color="#817F80"/>
<stop offset="0.617188" stop-color="#817E7F"/>
<stop offset="0.621094" stop-color="#817E7F"/>
<stop offset="0.625" stop-color="#807D7E"/>
<stop offset="0.628906" stop-color="#807D7D"/>
<stop offset="0.632812" stop-color="#7F7C7D"/>
<stop offset="0.636719" stop-color="#7F7C7C"/>
<stop offset="0.640625" stop-color="#7E7B7C"/>
<stop offset="0.644531" stop-color="#7D7B7B"/>
<stop offset="0.648438" stop-color="#7D7A7B"/>
<stop offset="0.652344" stop-color="#7C7A7A"/>
<stop offset="0.660156" stop-color="#7B7979"/>
<stop offset="0.664062" stop-color="#7B7979"/>
<stop offset="0.667969" stop-color="#7A7879"/>
<stop offset="0.671875" stop-color="#7A7878"/>
<stop offset="0.675781" stop-color="#7A7778"/>
<stop offset="0.679688" stop-color="#797777"/>
<stop offset="0.683594" stop-color="#797677"/>
<stop offset="0.6875" stop-color="#787676"/>
<stop offset="0.691406" stop-color="#777476"/>
<stop offset="0.695312" stop-color="#777475"/>
<stop offset="0.699219" stop-color="#767375"/>
<stop offset="0.703125" stop-color="#767374"/>
<stop offset="0.707031" stop-color="#757274"/>
<stop offset="0.710938" stop-color="#757273"/>
<stop offset="0.714844" stop-color="#747273"/>
<stop offset="0.71875" stop-color="#747172"/>
<stop offset="0.722656" stop-color="#737171"/>
<stop offset="0.726562" stop-color="#727070"/>
<stop offset="0.730469" stop-color="#726F70"/>
<stop offset="0.734375" stop-color="#716F6F"/>
<stop offset="0.738281" stop-color="#716E6F"/>
<stop offset="0.742188" stop-color="#706E6E"/>
<stop offset="0.746094" stop-color="#6F6D6D"/>
<stop offset="0.753906" stop-color="#6F6D6D"/>
<stop offset="0.757812" stop-color="#6E6C6D"/>
<stop offset="0.761719" stop-color="#6E6C6C"/>
<stop offset="0.765625" stop-color="#6D6B6C"/>
<stop offset="0.769531" stop-color="#6D6B6B"/>
<stop offset="0.773438" stop-color="#6C6A6B"/>
<stop offset="0.777344" stop-color="#6C696A"/>
<stop offset="0.78125" stop-color="#6C696A"/>
<stop offset="0.785156" stop-color="#6B6869"/>
<stop offset="0.789062" stop-color="#6B6869"/>
<stop offset="0.792969" stop-color="#6A6768"/>
<stop offset="0.796875" stop-color="#6A6768"/>
<stop offset="0.800781" stop-color="#696667"/>
<stop offset="0.804688" stop-color="#696666"/>
<stop offset="0.808594" stop-color="#686566"/>
<stop offset="0.8125" stop-color="#676465"/>
<stop offset="0.816406" stop-color="#666464"/>
<stop offset="0.820312" stop-color="#666364"/>
<stop offset="0.824219" stop-color="#656363"/>
<stop offset="0.828125" stop-color="#656263"/>
<stop offset="0.832031" stop-color="#646262"/>
<stop offset="0.835938" stop-color="#646162"/>
<stop offset="0.839844" stop-color="#636161"/>
<stop offset="0.84375" stop-color="#636061"/>
<stop offset="0.847656" stop-color="#626060"/>
<stop offset="0.851562" stop-color="#625F60"/>
<stop offset="0.855469" stop-color="#615F5F"/>
<stop offset="0.863281" stop-color="#605E5F"/>
<stop offset="0.871094" stop-color="#5F5D5E"/>
<stop offset="0.878906" stop-color="#5F5C5D"/>
<stop offset="0.882812" stop-color="#5E5B5C"/>
<stop offset="0.886719" stop-color="#5E5B5C"/>
<stop offset="0.890625" stop-color="#5D5A5B"/>
<stop offset="0.894531" stop-color="#5D5A5A"/>
<stop offset="0.898438" stop-color="#5C5959"/>
<stop offset="0.902344" stop-color="#5B5859"/>
<stop offset="0.90625" stop-color="#5B5858"/>
<stop offset="0.910156" stop-color="#5B5758"/>
<stop offset="0.914062" stop-color="#5A5757"/>
<stop offset="0.917969" stop-color="#5A5657"/>
<stop offset="0.921875" stop-color="#595656"/>
<stop offset="0.925781" stop-color="#595556"/>
<stop offset="0.929688" stop-color="#585455"/>
<stop offset="0.933594" stop-color="#585455"/>
<stop offset="0.9375" stop-color="#575354"/>
<stop offset="0.941406" stop-color="#565354"/>
<stop offset="0.945312" stop-color="#555253"/>
<stop offset="0.949219" stop-color="#555253"/>
<stop offset="0.953125" stop-color="#545252"/>
<stop offset="0.957031" stop-color="#535152"/>
<stop offset="0.964844" stop-color="#535051"/>
<stop offset="0.972656" stop-color="#524F50"/>
<stop offset="0.976562" stop-color="#524F4F"/>
<stop offset="0.980469" stop-color="#514E4E"/>
<stop offset="0.984375" stop-color="#514D4E"/>
<stop offset="0.988281" stop-color="#504D4D"/>
<stop offset="0.992188" stop-color="#504C4D"/>
<stop offset="1" stop-color="#4F4C4D"/>
</radialGradient>
</defs>
</svg>
