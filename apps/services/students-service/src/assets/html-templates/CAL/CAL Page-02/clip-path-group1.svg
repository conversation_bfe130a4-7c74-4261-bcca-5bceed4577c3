<svg width="8" height="3" viewBox="0 0 8 3" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_11085_12093" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="-1" width="8" height="4">
<path d="M0.462891 -0.00146484H7.62831V2.12312H0.462891V-0.00146484Z" fill="white"/>
</mask>
<g mask="url(#mask0_11085_12093)">
<mask id="mask1_11085_12093" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="-1" width="8" height="4">
<path d="M1.31553 2.10471C1.82678 2.15055 2.52262 2.00888 3.3272 1.69555C3.87387 1.48263 4.40804 1.25221 4.92429 1.02971C5.8572 0.627214 6.7547 0.239714 7.62887 -0.00195312C6.73762 0.16763 5.67429 0.35263 4.71137 0.67763C4.17887 0.857214 3.80054 0.88138 3.24012 1.05555C0.912201 1.7793 0.779285 1.3168 1.03053 0.702214C1.08512 0.569297 1.1572 0.420964 1.15762 0.28013C0.456368 0.83763 0.381368 1.43721 0.535951 1.70513C0.668035 1.93513 0.929701 2.07013 1.31553 2.10471Z" fill="white"/>
</mask>
<g mask="url(#mask1_11085_12093)">
<mask id="mask2_11085_12093" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="-1" width="8" height="4">
<path d="M1.31553 2.10471C1.82678 2.15055 2.52262 2.00888 3.3272 1.69555C3.87387 1.48263 4.40804 1.25221 4.92429 1.02971C5.8572 0.627214 6.7547 0.239714 7.62887 -0.00195312C6.73762 0.16763 5.67429 0.35263 4.71137 0.67763C4.17887 0.857214 3.80054 0.88138 3.24012 1.05555C0.912201 1.7793 0.779285 1.3168 1.03053 0.702214C1.08512 0.569297 1.1572 0.420964 1.15762 0.28013C0.456368 0.83763 0.381368 1.43721 0.535951 1.70513C0.668035 1.93513 0.929701 2.07013 1.31553 2.10471Z" fill="white"/>
</mask>
<g mask="url(#mask2_11085_12093)">
<path d="M0.462891 -0.00244141V2.12214H7.62831V-0.00244141H0.462891Z" fill="url(#paint0_radial_11085_12093)"/>
</g>
</g>
</g>
<defs>
<radialGradient id="paint0_radial_11085_12093" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(4.04819 1.05506) scale(2.63962)">
<stop stop-color="#51ADE5"/>
<stop offset="0.00390625" stop-color="#50ACE5"/>
<stop offset="0.0078125" stop-color="#50ACE4"/>
<stop offset="0.0117188" stop-color="#50ABE4"/>
<stop offset="0.015625" stop-color="#4FABE4"/>
<stop offset="0.0195312" stop-color="#4FAAE3"/>
<stop offset="0.0234375" stop-color="#4FA9E2"/>
<stop offset="0.0273438" stop-color="#4FA8E2"/>
<stop offset="0.03125" stop-color="#4FA7E1"/>
<stop offset="0.0351562" stop-color="#4FA7E1"/>
<stop offset="0.0390625" stop-color="#4EA7E1"/>
<stop offset="0.0429688" stop-color="#4EA6E0"/>
<stop offset="0.046875" stop-color="#4EA6E0"/>
<stop offset="0.0507812" stop-color="#4EA5E0"/>
<stop offset="0.0546875" stop-color="#4EA5E0"/>
<stop offset="0.0585938" stop-color="#4EA4DF"/>
<stop offset="0.0625" stop-color="#4DA3DF"/>
<stop offset="0.0664062" stop-color="#4DA3DF"/>
<stop offset="0.0703125" stop-color="#4CA2DE"/>
<stop offset="0.0742188" stop-color="#4CA1DE"/>
<stop offset="0.078125" stop-color="#4CA1DE"/>
<stop offset="0.0820312" stop-color="#4CA0DD"/>
<stop offset="0.0859375" stop-color="#4CA0DD"/>
<stop offset="0.0898438" stop-color="#4C9FDD"/>
<stop offset="0.09375" stop-color="#4B9FDC"/>
<stop offset="0.0976562" stop-color="#4B9EDC"/>
<stop offset="0.101562" stop-color="#4B9EDC"/>
<stop offset="0.105469" stop-color="#4B9DDB"/>
<stop offset="0.109375" stop-color="#4B9DDB"/>
<stop offset="0.113281" stop-color="#4B9CDA"/>
<stop offset="0.117188" stop-color="#4A9CDA"/>
<stop offset="0.121094" stop-color="#4A9BDA"/>
<stop offset="0.125" stop-color="#4A9BD9"/>
<stop offset="0.128906" stop-color="#4A9AD9"/>
<stop offset="0.132812" stop-color="#4A9AD9"/>
<stop offset="0.136719" stop-color="#4A99D8"/>
<stop offset="0.140625" stop-color="#4999D8"/>
<stop offset="0.144531" stop-color="#4998D7"/>
<stop offset="0.148438" stop-color="#4997D7"/>
<stop offset="0.152344" stop-color="#4896D6"/>
<stop offset="0.15625" stop-color="#4895D6"/>
<stop offset="0.160156" stop-color="#4895D6"/>
<stop offset="0.164062" stop-color="#4894D6"/>
<stop offset="0.167969" stop-color="#4894D5"/>
<stop offset="0.171875" stop-color="#4893D5"/>
<stop offset="0.175781" stop-color="#4793D5"/>
<stop offset="0.179688" stop-color="#4793D4"/>
<stop offset="0.183594" stop-color="#4792D4"/>
<stop offset="0.1875" stop-color="#4792D4"/>
<stop offset="0.191406" stop-color="#4791D3"/>
<stop offset="0.195312" stop-color="#4791D3"/>
<stop offset="0.199219" stop-color="#4791D2"/>
<stop offset="0.203125" stop-color="#4790D2"/>
<stop offset="0.207031" stop-color="#478FD2"/>
<stop offset="0.210938" stop-color="#468FD2"/>
<stop offset="0.214844" stop-color="#468ED1"/>
<stop offset="0.21875" stop-color="#468ED1"/>
<stop offset="0.222656" stop-color="#468DD0"/>
<stop offset="0.226562" stop-color="#468CD0"/>
<stop offset="0.230469" stop-color="#458BD0"/>
<stop offset="0.234375" stop-color="#458BD0"/>
<stop offset="0.238281" stop-color="#458ACF"/>
<stop offset="0.242188" stop-color="#448ACF"/>
<stop offset="0.246094" stop-color="#4489CF"/>
<stop offset="0.25" stop-color="#4489CE"/>
<stop offset="0.253906" stop-color="#4488CE"/>
<stop offset="0.257812" stop-color="#4488CE"/>
<stop offset="0.265625" stop-color="#4487CD"/>
<stop offset="0.269531" stop-color="#4486CD"/>
<stop offset="0.273438" stop-color="#4386CC"/>
<stop offset="0.277344" stop-color="#4385CB"/>
<stop offset="0.285156" stop-color="#4385CB"/>
<stop offset="0.289062" stop-color="#4384CA"/>
<stop offset="0.292969" stop-color="#4284CA"/>
<stop offset="0.296875" stop-color="#4283C9"/>
<stop offset="0.304688" stop-color="#4282C9"/>
<stop offset="0.308594" stop-color="#4182C9"/>
<stop offset="0.3125" stop-color="#4181C9"/>
<stop offset="0.316406" stop-color="#4180C8"/>
<stop offset="0.324219" stop-color="#417FC8"/>
<stop offset="0.328125" stop-color="#417FC7"/>
<stop offset="0.332031" stop-color="#417EC7"/>
<stop offset="0.335938" stop-color="#417EC7"/>
<stop offset="0.339844" stop-color="#407DC7"/>
<stop offset="0.34375" stop-color="#407DC6"/>
<stop offset="0.347656" stop-color="#407CC6"/>
<stop offset="0.351562" stop-color="#407CC5"/>
<stop offset="0.355469" stop-color="#407BC5"/>
<stop offset="0.359375" stop-color="#407BC5"/>
<stop offset="0.363281" stop-color="#407AC4"/>
<stop offset="0.367188" stop-color="#3F7AC4"/>
<stop offset="0.371094" stop-color="#3F79C4"/>
<stop offset="0.378906" stop-color="#3E79C3"/>
<stop offset="0.382812" stop-color="#3E78C3"/>
<stop offset="0.390625" stop-color="#3E77C2"/>
<stop offset="0.394531" stop-color="#3E77C2"/>
<stop offset="0.398438" stop-color="#3E76C1"/>
<stop offset="0.40625" stop-color="#3E76C1"/>
<stop offset="0.410156" stop-color="#3E75C1"/>
<stop offset="0.414062" stop-color="#3D75C0"/>
<stop offset="0.417969" stop-color="#3D74C0"/>
<stop offset="0.421875" stop-color="#3D73C0"/>
<stop offset="0.425781" stop-color="#3D73BF"/>
<stop offset="0.429688" stop-color="#3D72BF"/>
<stop offset="0.4375" stop-color="#3D71BE"/>
<stop offset="0.441406" stop-color="#3D71BD"/>
<stop offset="0.445312" stop-color="#3C70BD"/>
<stop offset="0.453125" stop-color="#3C6FBD"/>
<stop offset="0.457031" stop-color="#3B6FBC"/>
<stop offset="0.460938" stop-color="#3B6EBC"/>
<stop offset="0.464844" stop-color="#3B6EBB"/>
<stop offset="0.472656" stop-color="#3B6DBB"/>
<stop offset="0.476562" stop-color="#3B6DBB"/>
<stop offset="0.480469" stop-color="#3B6CBA"/>
<stop offset="0.484375" stop-color="#3B6CBA"/>
<stop offset="0.492188" stop-color="#3B6BB9"/>
<stop offset="0.496094" stop-color="#3A6AB9"/>
<stop offset="0.503906" stop-color="#3A6AB9"/>
<stop offset="0.507812" stop-color="#3A69B9"/>
<stop offset="0.511719" stop-color="#3A69B8"/>
<stop offset="0.515625" stop-color="#3A68B8"/>
<stop offset="0.519531" stop-color="#3968B7"/>
<stop offset="0.523438" stop-color="#3967B7"/>
<stop offset="0.527344" stop-color="#3967B7"/>
<stop offset="0.53125" stop-color="#3966B6"/>
<stop offset="0.535156" stop-color="#3966B6"/>
<stop offset="0.539062" stop-color="#3965B6"/>
<stop offset="0.542969" stop-color="#3965B6"/>
<stop offset="0.546875" stop-color="#3864B5"/>
<stop offset="0.550781" stop-color="#3864B5"/>
<stop offset="0.554688" stop-color="#3864B4"/>
<stop offset="0.558594" stop-color="#3863B4"/>
<stop offset="0.5625" stop-color="#3862B4"/>
<stop offset="0.566406" stop-color="#3862B3"/>
<stop offset="0.570312" stop-color="#3861B3"/>
<stop offset="0.582031" stop-color="#3860B3"/>
<stop offset="0.585938" stop-color="#3760B2"/>
<stop offset="0.597656" stop-color="#375FB1"/>
<stop offset="0.601562" stop-color="#365FB0"/>
<stop offset="0.605469" stop-color="#365EB0"/>
<stop offset="0.609375" stop-color="#365EB0"/>
<stop offset="0.613281" stop-color="#365DAF"/>
<stop offset="0.617188" stop-color="#365DAF"/>
<stop offset="0.621094" stop-color="#365CAF"/>
<stop offset="0.625" stop-color="#365CAE"/>
<stop offset="0.628906" stop-color="#365BAE"/>
<stop offset="0.636719" stop-color="#365BAE"/>
<stop offset="0.640625" stop-color="#365AAD"/>
<stop offset="0.644531" stop-color="#355AAD"/>
<stop offset="0.648438" stop-color="#3559AD"/>
<stop offset="0.65625" stop-color="#3558AC"/>
<stop offset="0.664062" stop-color="#3557AC"/>
<stop offset="0.667969" stop-color="#3557AC"/>
<stop offset="0.671875" stop-color="#3456AB"/>
<stop offset="0.679688" stop-color="#3456AB"/>
<stop offset="0.683594" stop-color="#3455AA"/>
<stop offset="0.6875" stop-color="#3455AA"/>
<stop offset="0.691406" stop-color="#3454A9"/>
<stop offset="0.699219" stop-color="#3453A9"/>
<stop offset="0.703125" stop-color="#3352A9"/>
<stop offset="0.71875" stop-color="#3352A8"/>
<stop offset="0.722656" stop-color="#3352A7"/>
<stop offset="0.726562" stop-color="#3351A7"/>
<stop offset="0.730469" stop-color="#3351A6"/>
<stop offset="0.734375" stop-color="#3250A6"/>
<stop offset="0.742188" stop-color="#3250A5"/>
<stop offset="0.75" stop-color="#324FA4"/>
<stop offset="0.753906" stop-color="#324EA4"/>
<stop offset="0.757812" stop-color="#324EA4"/>
<stop offset="0.761719" stop-color="#324DA4"/>
<stop offset="0.765625" stop-color="#324DA4"/>
<stop offset="0.769531" stop-color="#314CA3"/>
<stop offset="0.773438" stop-color="#314CA3"/>
<stop offset="0.777344" stop-color="#314CA3"/>
<stop offset="0.78125" stop-color="#314BA2"/>
<stop offset="0.785156" stop-color="#314BA2"/>
<stop offset="0.789062" stop-color="#314AA2"/>
<stop offset="0.792969" stop-color="#314AA1"/>
<stop offset="0.796875" stop-color="#314AA1"/>
<stop offset="0.800781" stop-color="#3149A1"/>
<stop offset="0.804688" stop-color="#3149A1"/>
<stop offset="0.808594" stop-color="#3149A0"/>
<stop offset="0.8125" stop-color="#3048A0"/>
<stop offset="0.816406" stop-color="#30489F"/>
<stop offset="0.820312" stop-color="#30479F"/>
<stop offset="0.828125" stop-color="#30469F"/>
<stop offset="0.835938" stop-color="#30469E"/>
<stop offset="0.84375" stop-color="#2F459E"/>
<stop offset="0.847656" stop-color="#2F459D"/>
<stop offset="0.851562" stop-color="#2F459D"/>
<stop offset="0.855469" stop-color="#2F449D"/>
<stop offset="0.859375" stop-color="#2F449D"/>
<stop offset="0.863281" stop-color="#2F449C"/>
<stop offset="0.871094" stop-color="#2F439C"/>
<stop offset="0.878906" stop-color="#2F429B"/>
<stop offset="0.882812" stop-color="#2F429B"/>
<stop offset="0.886719" stop-color="#2F419A"/>
<stop offset="0.890625" stop-color="#2F419A"/>
<stop offset="0.894531" stop-color="#2E419A"/>
<stop offset="0.898438" stop-color="#2E4099"/>
<stop offset="0.902344" stop-color="#2E4099"/>
<stop offset="0.90625" stop-color="#2E3F98"/>
<stop offset="0.917969" stop-color="#2E3E98"/>
<stop offset="0.921875" stop-color="#2E3E97"/>
<stop offset="0.925781" stop-color="#2E3E97"/>
<stop offset="0.933594" stop-color="#2D3D97"/>
<stop offset="0.9375" stop-color="#2D3C96"/>
<stop offset="0.941406" stop-color="#2D3C96"/>
<stop offset="0.945312" stop-color="#2D3B96"/>
<stop offset="0.949219" stop-color="#2D3B95"/>
<stop offset="0.960938" stop-color="#2D3A95"/>
<stop offset="0.964844" stop-color="#2D3A94"/>
<stop offset="0.96875" stop-color="#2C3A94"/>
<stop offset="0.976562" stop-color="#2C3993"/>
<stop offset="0.980469" stop-color="#2C3993"/>
<stop offset="0.984375" stop-color="#2C3992"/>
<stop offset="1" stop-color="#2C3892"/>
</radialGradient>
</defs>
</svg>
