.cal-page-03,
.cal-page-03 * {
  box-sizing: border-box;
}
.cal-page-03 {
  background: #ffffff;
  height: 842px;
  position: relative;
  overflow: hidden;
}
.main-container {
  display: flex;
  flex-direction: column;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 595px;
  height: 842px;
  position: absolute;
  left: 50%;
  translate: -50% -50%;
  top: 50%;
}
.logo-and-address {
  border-style: solid;
  border-color: var(--light-theme-tertiary-color, #1952bb);
  border-width: 0px 0px 1px 0px;
  padding: 24px 48px 0px 48px;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: space-between;
  align-self: stretch;
  flex-shrink: 0;
  height: 90px;
  position: relative;
}
.horizontal-container {
  display: flex;
  flex-direction: row;
  gap: 24px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex: 1;
  position: relative;
}
.group {
  flex-shrink: 0;
  width: 52px;
  height: 52px;
  position: relative;
  overflow: visible;
  aspect-ratio: 1;
}
.name-with-address {
  display: flex;
  flex-direction: column;
  gap: 6px;
  align-items: flex-start;
  justify-content: center;
  flex: 1;
  position: relative;
}
.text-input {
  display: flex;
  flex-direction: row;
  gap: 14px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  position: relative;
}
.international-american-university {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-SemiBold", sans-serif;
  font-size: 16px;
  line-height: 24px;
  font-weight: 600;
  position: relative;
}
.address {
  display: flex;
  flex-direction: row;
  gap: 14px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  position: relative;
}
.phone {
  display: flex;
  flex-direction: row;
  gap: 4px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  height: 15px;
  position: relative;
}
.call {
  flex-shrink: 0;
  width: 13px;
  height: 13px;
  position: relative;
  overflow: visible;
  aspect-ratio: 1;
}
.text {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Regular", sans-serif;
  font-size: 10px;
  line-height: 15px;
  font-weight: 400;
  position: relative;
}
.phone2 {
  display: flex;
  flex-direction: row;
  gap: 4px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  position: relative;
}
.language {
  flex-shrink: 0;
  width: 13px;
  height: 13px;
  position: relative;
  overflow: visible;
  aspect-ratio: 1;
}
.mail {
  flex-shrink: 0;
  width: 13px;
  height: 13px;
  position: relative;
  overflow: visible;
  aspect-ratio: 1;
}
.container {
  padding: 0px 48px 0px 48px;
  display: flex;
  flex-direction: column;
  gap: 14px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex: 1;
  position: relative;
}
.container2 {
  display: flex;
  flex-direction: column;
  gap: 14px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex: 1;
  position: relative;
}
.heading {
  border-style: solid;
  border-color: var(--light-theme-grey07, #19191c);
  border-width: 0px 0px 1px 0px;
  padding: 10px 0px 10px 0px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.payment-invoice {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-SemiBold", sans-serif;
  font-size: 16px;
  line-height: 24px;
  font-weight: 600;
  position: relative;
}
.invoice {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.invoice-header {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.bill-to-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.bill-to-container {
  display: flex;
  flex-direction: row;
  gap: 16px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.bill-to-info {
  border-radius: 4px;
  border-style: solid;
  border-color: transparent;
  border-width: 1px;
  display: flex;
  flex-direction: column;
  gap: 0px;
  align-items: flex-start;
  justify-content: flex-start;
  flex: 1;
  position: relative;
}
.bill-to-label {
  padding: 6px 0px 6px 0px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
}
.bill-to-text {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-SemiBold", sans-serif;
  font-size: 10px;
  line-height: 15px;
  font-weight: 600;
  position: relative;
}
.bill-to-details {
  display: flex;
  flex-direction: column;
  gap: 0px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.student-info {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.student-label-container {
  padding: 6px 0px 6px 0px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
}
.student-label {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Regular", sans-serif;
  font-size: 10px;
  line-height: 15px;
  font-weight: 400;
  position: relative;
}
.student-value-container {
  padding: 6px 0px 6px 0px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  flex: 1;
  position: relative;
  overflow: hidden;
}
.student-value {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Regular", sans-serif;
  font-size: 10px;
  line-height: 14px;
  font-weight: 400;
  position: relative;
}
.passport-info {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.passport-label-container {
  padding: 6px 0px 6px 0px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 68px;
  position: relative;
  overflow: hidden;
}
.passport-label {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Regular", sans-serif;
  font-size: 10px;
  line-height: 15px;
  font-weight: 400;
  position: relative;
}
.passport-value-container {
  padding: 6px 0px 6px 0px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  flex: 1;
  position: relative;
  overflow: hidden;
}
.passport-value {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Regular", sans-serif;
  font-size: 10px;
  line-height: 14px;
  font-weight: 400;
  position: relative;
}
.bill-to-info2 {
  border-radius: 4px;
  border-style: solid;
  border-color: transparent;
  border-width: 1px;
  display: flex;
  flex-direction: column;
  gap: 0px;
  align-items: flex-start;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 140px;
  position: relative;
}
.invoice2 {
  padding: 6px 0px 6px 0px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex: 1;
  position: relative;
  overflow: hidden;
}
.student-label2 {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-SemiBold", sans-serif;
  font-size: 10px;
  line-height: 15px;
  font-weight: 600;
  position: relative;
}
.passport-label-container2 {
  padding: 6px 0px 6px 0px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-end;
  flex-shrink: 0;
  width: 48px;
  position: relative;
  overflow: hidden;
}
.passport-value2 {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Regular", sans-serif;
  font-size: 10px;
  line-height: 15px;
  font-weight: 400;
  position: relative;
}
.student-label-container2 {
  padding: 6px 0px 6px 0px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 48px;
  position: relative;
  overflow: hidden;
}
.student-value2 {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Regular", sans-serif;
  font-size: 10px;
  line-height: 15px;
  font-weight: 400;
  position: relative;
}
.program-info {
  background: var(--light-theme-primary-shade-01, #f4f7fe);
  border-radius: 4px;
  border-style: solid;
  border-color: rgba(25, 82, 187, 0.2);
  border-width: 1px;
  display: flex;
  flex-direction: column;
  gap: 0px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.program-date-info {
  border-style: solid;
  border-color: rgba(25, 82, 187, 0.2);
  border-width: 0px 0px 1px 0px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 499px;
  position: relative;
}
.program-date-label-container {
  padding: 6px 8px 6px 8px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 80px;
  position: relative;
  overflow: hidden;
}
.program-date-label {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Regular", sans-serif;
  font-size: 10px;
  line-height: 15px;
  font-weight: 400;
  position: relative;
}
.program-date-value-container {
  padding: 6px 8px 6px 8px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 386px;
  position: relative;
  overflow: hidden;
}
.program-date-value {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Regular", sans-serif;
  font-size: 10px;
  line-height: 15px;
  font-weight: 400;
  position: relative;
}
.intake-info {
  border-style: solid;
  border-color: rgba(25, 82, 187, 0.2);
  border-width: 0px 0px 1px 0px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 499px;
  position: relative;
}
.intake-label-container {
  padding: 6px 8px 6px 8px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 80px;
  position: relative;
  overflow: hidden;
}
.intake-label {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Regular", sans-serif;
  font-size: 10px;
  line-height: 15px;
  font-weight: 400;
  position: relative;
}
.intake-value-container {
  padding: 6px 8px 6px 8px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 386px;
  position: relative;
  overflow: hidden;
}
.intake-value {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Regular", sans-serif;
  font-size: 10px;
  line-height: 15px;
  font-weight: 400;
  position: relative;
}
.year-info {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 499px;
  position: relative;
}
.year-label-container {
  padding: 6px 8px 6px 8px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 80px;
  position: relative;
  overflow: hidden;
}
.year-label {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Regular", sans-serif;
  font-size: 10px;
  line-height: 15px;
  font-weight: 400;
  position: relative;
}
.year-value-container {
  padding: 6px 8px 6px 8px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 386px;
  position: relative;
  overflow: hidden;
}
.year-value {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Regular", sans-serif;
  font-size: 10px;
  line-height: 15px;
  font-weight: 400;
  position: relative;
}
.fee-details-container {
  border-radius: 4px;
  border-style: solid;
  border-color: rgba(25, 82, 187, 0.2);
  border-width: 1px;
  display: flex;
  flex-direction: column;
  gap: 0px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.fee-header {
  border-style: solid;
  border-color: rgba(25, 82, 187, 0.2);
  border-width: 0px 0px 1px 0px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 499px;
  position: relative;
}
.quantity-header-container {
  border-style: solid;
  border-color: rgba(25, 82, 187, 0.2);
  border-width: 0px 1px 0px 0px;
  padding: 6px 8px 6px 8px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 74px;
  position: relative;
  overflow: hidden;
}
.quantity-header {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Regular", sans-serif;
  font-size: 10px;
  line-height: 15px;
  font-weight: 400;
  position: relative;
}
.description-header-container {
  border-style: solid;
  border-color: rgba(25, 82, 187, 0.2);
  border-width: 0px 1px 0px 0px;
  padding: 6px 8px 6px 8px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 227px;
  position: relative;
  overflow: hidden;
}
.description-header {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Regular", sans-serif;
  font-size: 10px;
  line-height: 15px;
  font-weight: 400;
  position: relative;
}
.price-header-container {
  border-style: solid;
  border-color: rgba(25, 82, 187, 0.2);
  border-width: 0px 1px 0px 0px;
  padding: 6px 8px 6px 8px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 97px;
  position: relative;
  overflow: hidden;
}
.price-header {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Regular", sans-serif;
  font-size: 10px;
  line-height: 15px;
  font-weight: 400;
  position: relative;
}
.amount-header-container {
  padding: 6px 8px 6px 8px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 101px;
  position: relative;
  overflow: hidden;
}
.amount-header {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Regular", sans-serif;
  font-size: 10px;
  line-height: 15px;
  font-weight: 400;
  position: relative;
}
.tuition-fee-info {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 499px;
  position: relative;
}
.tuition-fee-quantity-container {
  border-style: solid;
  border-color: rgba(25, 82, 187, 0.2);
  border-width: 0px 1px 0px 0px;
  padding: 6px 8px 6px 8px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 74px;
  position: relative;
  overflow: hidden;
}
.tuition-fee-quantity {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Regular", sans-serif;
  font-size: 10px;
  line-height: 15px;
  font-weight: 400;
  position: relative;
}
.tuition-fee-description-container {
  border-style: solid;
  border-color: rgba(25, 82, 187, 0.2);
  border-width: 0px 1px 0px 0px;
  padding: 6px 8px 6px 8px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 227px;
  position: relative;
  overflow: hidden;
}
.tuition-fee-description {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Regular", sans-serif;
  font-size: 10px;
  line-height: 15px;
  font-weight: 400;
  position: relative;
}
.tuition-fee-price-container {
  border-style: solid;
  border-color: rgba(25, 82, 187, 0.2);
  border-width: 0px 1px 0px 0px;
  padding: 6px 8px 6px 8px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-end;
  flex-shrink: 0;
  width: 97px;
  position: relative;
  overflow: hidden;
}
.tuition-fee-price {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Regular", sans-serif;
  font-size: 10px;
  line-height: 15px;
  font-weight: 400;
  position: relative;
}
.tuition-fee-amount-container {
  padding: 6px 8px 6px 8px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-end;
  flex-shrink: 0;
  width: 101px;
  position: relative;
  overflow: hidden;
}
.tuition-fee-amount {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Regular", sans-serif;
  font-size: 10px;
  line-height: 15px;
  font-weight: 400;
  position: relative;
}
.empty-fee-info {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 499px;
  position: relative;
}
.empty-fee-quantity-container {
  border-style: solid;
  border-color: rgba(25, 82, 187, 0.2);
  border-width: 0px 1px 0px 0px;
  padding: 6px 8px 6px 8px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 74px;
  height: 144px;
  position: relative;
  overflow: hidden;
}
.empty-fee-description-container {
  border-style: solid;
  border-color: rgba(25, 82, 187, 0.2);
  border-width: 0px 1px 0px 0px;
  padding: 6px 8px 6px 8px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 227px;
  height: 144px;
  position: relative;
  overflow: hidden;
}
.empty-fee-price-container {
  border-style: solid;
  border-color: rgba(25, 82, 187, 0.2);
  border-width: 0px 1px 0px 0px;
  padding: 6px 8px 6px 8px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-end;
  flex-shrink: 0;
  width: 97px;
  height: 144px;
  position: relative;
  overflow: hidden;
}
.empty-fee-amount-container {
  padding: 6px 8px 6px 8px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-end;
  flex-shrink: 0;
  width: 101px;
  height: 144px;
  position: relative;
  overflow: hidden;
}
.container3 {
  display: flex;
  flex-direction: row;
  gap: 12px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.container4 {
  border-radius: 4px;
  border-style: solid;
  border-color: rgba(25, 82, 187, 0.2);
  border-width: 1px;
  padding: 10px 0px 10px 0px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: flex-start;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 246px;
  position: relative;
  overflow: hidden;
}
.paragraph {
  padding: 0px 10px 0px 10px;
  display: flex;
  flex-direction: row;
  gap: 8px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.text2 {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 74px;
  position: relative;
}
.company {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Regular", sans-serif;
  font-size: 8px;
  line-height: 10px;
  font-weight: 400;
  position: relative;
}
.text3 {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  position: relative;
}
.apply-goal {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Regular", sans-serif;
  font-size: 8px;
  line-height: 10px;
  font-weight: 400;
  position: relative;
}
.bank {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Regular", sans-serif;
  font-size: 8px;
  line-height: 10px;
  font-weight: 400;
  position: relative;
}
.bank-of-america {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Regular", sans-serif;
  font-size: 8px;
  line-height: 10px;
  font-weight: 400;
  position: relative;
}
.address2 {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Regular", sans-serif;
  font-size: 8px;
  line-height: 10px;
  font-weight: 400;
  position: relative;
}
.p-o-box-15284-wilmington-de-1985 {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Regular", sans-serif;
  font-size: 8px;
  line-height: 10px;
  font-weight: 400;
  position: relative;
}
.account-number {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Regular", sans-serif;
  font-size: 8px;
  line-height: 10px;
  font-weight: 400;
  position: relative;
}
._325157409983 {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Regular", sans-serif;
  font-size: 8px;
  line-height: 10px;
  font-weight: 400;
  position: relative;
}
.routing {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Regular", sans-serif;
  font-size: 8px;
  line-height: 10px;
  font-weight: 400;
  position: relative;
}
.text4 {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  flex: 1;
  position: relative;
}
._122000661-*********-paper-electronic {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Regular", sans-serif;
  font-size: 8px;
  line-height: 10px;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.swift-code {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Regular", sans-serif;
  font-size: 8px;
  line-height: 10px;
  font-weight: 400;
  position: relative;
}
.bofaus-3-nxxx {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Regular", sans-serif;
  font-size: 8px;
  line-height: 10px;
  font-weight: 400;
  position: relative;
}
.payment-reference {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Regular", sans-serif;
  font-size: 8px;
  line-height: 10px;
  font-weight: 400;
  position: relative;
}
.nishat-tasnim-jayefa-iaula-113199 {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Regular", sans-serif;
  font-size: 8px;
  line-height: 10px;
  font-weight: 400;
  position: relative;
}
.bill-to-info3 {
  background: var(--light-theme-white, #ffffff);
  border-radius: 4px;
  border-style: solid;
  border-color: rgba(25, 82, 187, 0.2);
  border-width: 1px;
  display: flex;
  flex-direction: column;
  gap: 0px;
  align-items: flex-start;
  justify-content: flex-start;
  flex: 1;
  position: relative;
}
.student-info2 {
  border-style: solid;
  border-color: rgba(25, 82, 187, 0.2);
  border-width: 0px 0px 1px 0px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.student-label-container3 {
  border-style: solid;
  border-color: rgba(25, 82, 187, 0.2);
  border-width: 0px 1px 0px 0px;
  padding: 6px 8px 6px 8px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  flex: 1;
  position: relative;
  overflow: hidden;
}
.student-value-container2 {
  padding: 6px 8px 6px 8px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-end;
  flex-shrink: 0;
  width: 101px;
  position: relative;
  overflow: hidden;
}
.passport-info2 {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.passport-label-container3 {
  border-style: solid;
  border-color: rgba(25, 82, 187, 0.2);
  border-width: 0px 1px 0px 0px;
  padding: 6px 8px 6px 8px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  flex: 1;
  position: relative;
  overflow: hidden;
}
.passport-value-container2 {
  padding: 6px 8px 6px 8px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-end;
  flex-shrink: 0;
  width: 101px;
  position: relative;
  overflow: hidden;
}
.footer {
  background: var(--light-theme-primary-shade-04, #e9f0ff);
  padding: 0px 48px 24px 48px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  height: 90px;
  position: relative;
}
.container5 {
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.terms-container {
  border-style: solid;
  border-color: var(--light-theme-grey07, #19191c);
  border-width: 0px 0px 1px 0px;
  padding: 10px 0px 10px 0px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.terms-text {
  color: var(--light-theme-grey07, #19191c);
  text-align: left;
  font-family: "Inter-Medium", sans-serif;
  font-size: 10px;
  line-height: 14px;
  font-weight: 500;
  position: relative;
}
.bank-logos {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 499px;
  height: 26px;
  position: relative;
}
.logo {
  flex-shrink: 0;
  width: 62px;
  height: 26px;
  position: relative;
  overflow: hidden;
}
.tracs-logo-main-1 {
  width: 64.52%;
  height: 76.92%;
  position: absolute;
  right: 17.74%;
  left: 17.74%;
  bottom: 11.54%;
  top: 11.54%;
  overflow: hidden;
  aspect-ratio: 2/1;
}
.clip-path-group {
  height: auto;
  position: absolute;
  left: 0.05px;
  top: 0px;
  overflow: visible;
}
.clip-path-group2 {
  height: auto;
  position: absolute;
  left: 3.96px;
  top: 3.93px;
  overflow: visible;
}
.clip-path-group3 {
  height: auto;
  position: absolute;
  left: 5.04px;
  top: 14.34px;
  overflow: visible;
}
.clip-path-group4 {
  height: auto;
  position: absolute;
  left: 3.36px;
  top: 12.86px;
  overflow: visible;
}
.clip-path-group5 {
  height: auto;
  position: absolute;
  left: 1.5px;
  top: 9.16px;
  overflow: visible;
}
.clip-path-group6 {
  height: auto;
  position: absolute;
  left: 1.38px;
  top: 6.29px;
  overflow: visible;
}
.clip-path-group7 {
  height: auto;
  position: absolute;
  left: 1.48px;
  top: 4.48px;
  overflow: visible;
}
.clip-path-group8 {
  height: auto;
  position: absolute;
  left: 2.12px;
  top: 10.1px;
  overflow: visible;
}
.clip-path-group9 {
  height: auto;
  position: absolute;
  left: 1.3px;
  top: 7.25px;
  overflow: visible;
}
.clip-path-group10 {
  height: auto;
  position: absolute;
  left: 5.72px;
  top: 13.92px;
  overflow: visible;
}
.clip-path-group11 {
  height: auto;
  position: absolute;
  left: 4.34px;
  top: 3.21px;
  overflow: visible;
}
.group2 {
  height: auto;
  position: absolute;
  left: 15.65px;
  top: 4.52px;
  overflow: visible;
}
.group3 {
  height: auto;
  position: absolute;
  left: 20.12px;
  top: 4.52px;
  overflow: visible;
}
.group4 {
  height: auto;
  position: absolute;
  left: 24.31px;
  top: 4.52px;
  overflow: visible;
}
.group5 {
  height: auto;
  position: absolute;
  left: 29.38px;
  top: 4.46px;
  overflow: visible;
}
.group6 {
  height: auto;
  position: absolute;
  left: 33.98px;
  top: 4.46px;
  overflow: visible;
}
.group7 {
  height: auto;
  position: absolute;
  left: 15.77px;
  top: 11.33px;
  overflow: visible;
}
.group8 {
  height: auto;
  position: absolute;
  left: 17.62px;
  top: 11.33px;
  overflow: visible;
}
.group9 {
  height: auto;
  position: absolute;
  left: 19.03px;
  top: 11.33px;
  overflow: visible;
}
.group10 {
  height: auto;
  position: absolute;
  left: 20.32px;
  top: 11.33px;
  overflow: visible;
}
.group11 {
  height: auto;
  position: absolute;
  left: 21.78px;
  top: 11.33px;
  overflow: visible;
}
.group12 {
  height: auto;
  position: absolute;
  left: 22.65px;
  top: 11.33px;
  overflow: visible;
}
.group13 {
  height: auto;
  position: absolute;
  left: 24.21px;
  top: 11.31px;
  overflow: visible;
}
.group14 {
  height: auto;
  position: absolute;
  left: 26.51px;
  top: 11.33px;
  overflow: visible;
}
.group15 {
  height: auto;
  position: absolute;
  left: 29.02px;
  top: 11.33px;
  overflow: visible;
}
.group16 {
  height: auto;
  position: absolute;
  left: 30.65px;
  top: 11.33px;
  overflow: visible;
}
.group17 {
  height: auto;
  position: absolute;
  left: 31.45px;
  top: 11.31px;
  overflow: visible;
}
.group18 {
  height: auto;
  position: absolute;
  left: 33.09px;
  top: 11.33px;
  overflow: visible;
}
.group19 {
  height: auto;
  position: absolute;
  left: 34.72px;
  top: 11.33px;
  overflow: visible;
}
.group20 {
  height: auto;
  position: absolute;
  left: 36.13px;
  top: 11.33px;
  overflow: visible;
}
.group21 {
  height: auto;
  position: absolute;
  left: 20.49px;
  top: 13.42px;
  overflow: visible;
}
.group22 {
  height: auto;
  position: absolute;
  left: 21.81px;
  top: 13.44px;
  overflow: visible;
}
.group23 {
  height: auto;
  position: absolute;
  left: 23.12px;
  top: 13.44px;
  overflow: visible;
}
.group24 {
  height: auto;
  position: absolute;
  left: 24.83px;
  top: 13.44px;
  overflow: visible;
}
.group25 {
  height: auto;
  position: absolute;
  left: 26.45px;
  top: 13.44px;
  overflow: visible;
}
.group26 {
  height: auto;
  position: absolute;
  left: 27.96px;
  top: 13.44px;
  overflow: visible;
}
.group27 {
  height: auto;
  position: absolute;
  left: 29.67px;
  top: 13.44px;
  overflow: visible;
}
.group28 {
  height: auto;
  position: absolute;
  left: 31.18px;
  top: 13.44px;
  overflow: visible;
}
.logo-of-the-united-states-immigration-and-customs-enforcement-agency-1 {
  width: 50px;
  height: 15px;
  position: absolute;
  left: 50%;
  translate: -50% -50%;
  top: 50%;
  overflow: hidden;
  aspect-ratio: 50/15;
}
.vector {
  width: 29.8%;
  height: 99.35%;
  position: absolute;
  right: 69.96%;
  left: 0.24%;
  bottom: 0.33%;
  top: 0.33%;
  overflow: visible;
}
.group29 {
  height: auto;
  position: absolute;
  left: 0.12px;
  top: 0.05px;
  overflow: visible;
}
.uk-enic-1 {
  width: 45.77px;
  height: 14px;
  position: absolute;
  left: 8.11px;
  top: 6px;
  overflow: hidden;
  aspect-ratio: 45.77/14;
}
.layer-1 {
  height: auto;
  position: absolute;
  left: 0px;
  top: 0px;
  overflow: visible;
}
.csaave {
  width: 46.67px;
  height: 20px;
  position: absolute;
  left: 7.66px;
  top: 3px;
  overflow: hidden;
  aspect-ratio: 46.67/20;
}
.layer-12 {
  height: auto;
  position: absolute;
  left: 0px;
  top: 0px;
  overflow: visible;
}
.bppe-logo-1 {
  width: 20px;
  height: 20px;
  position: absolute;
  left: 50%;
  translate: -50% -50%;
  top: 50%;
  overflow: hidden;
  aspect-ratio: 1;
}
.bppe-logo-12 {
  position: absolute;
  inset: 0;
}
.bg {
  width: 100%;
  height: 100%;
  position: absolute;
  right: 0%;
  left: 0%;
  bottom: 0%;
  top: 0%;
  overflow: visible;
}
.group30 {
  width: 92.65%;
  height: 51.91%;
  position: absolute;
  right: 3.7%;
  left: 3.65%;
  bottom: 43.93%;
  top: 4.15%;
  overflow: visible;
}
.group31 {
  width: 92.8%;
  height: 19.04%;
  position: absolute;
  right: 3.65%;
  left: 3.56%;
  bottom: 16.49%;
  top: 64.48%;
  overflow: visible;
}
.group32 {
  width: 18.53px;
  height: 1.21px;
  position: absolute;
  left: 0.73px;
  top: 18.04px;
  overflow: visible;
}
.sevp {
  height: auto;
  position: absolute;
  left: 18.89px;
  top: 6px;
  overflow: visible;
  aspect-ratio: 24.23/14;
}
.council-for-higher-education-accreditation-logo-1 {
  width: 45.65px;
  height: 14px;
  position: absolute;
  left: 50%;
  translate: -50% -50%;
  top: 50%;
  overflow: hidden;
  aspect-ratio: 45.65/14;
}
.group33 {
  height: auto;
  position: absolute;
  left: 0px;
  top: 0.02px;
  overflow: visible;
}
.acbsp-seeklogo-1 {
  width: 22px;
  height: 22px;
  position: absolute;
  left: 50%;
  translate: -50% -50%;
  top: 50%;
  overflow: hidden;
  aspect-ratio: 1;
}
.group34 {
  width: 76.08%;
  height: 97.13%;
  position: absolute;
  right: 11.96%;
  left: 11.96%;
  bottom: 1.43%;
  top: 1.43%;
  overflow: visible;
  aspect-ratio: 16.74/21.37;
}
.icon {
  flex-shrink: 0;
  width: 400px;
  height: 400px;
  position: absolute;
  left: 50%;
  translate: -50% -50%;
  top: 50%;
  object-fit: cover;
  aspect-ratio: 1;
}
