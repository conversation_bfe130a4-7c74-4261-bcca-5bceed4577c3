import { QueryInterface, DataTypes } from 'sequelize';

export async function up(queryInterface: QueryInterface) {
  // Remove the unique constraint on email
  await queryInterface.removeConstraint(
    'student_personal_info',
    'student_personal_info_email_key'
  );
}

export async function down(queryInterface: QueryInterface) {
  // Add back the unique constraint on email
  await queryInterface.addConstraint('student_personal_info', {
    fields: ['email'],
    type: 'unique',
    name: 'student_personal_info_email_key'
  });
} 