import { Module, OnModuleInit } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { getDatabaseConfig } from '@apply-goal-backend/database';
import { Student } from '../student/models/student.model';
import { StudentPersonalInfo } from '../student/models/student-personal-info.model';
import { StudentAcademicRecord } from '../student/models/student-academic-record.model';
import { StudentProficiencyRecord } from '../student/models/student-proficiency-record.model';
import { StudentPublication } from '../student/models/student-publication.model';
import { StudentSocialLink } from '../student/models/student-social-link.model';
import { StudentOtherActivity } from '../student/models/student-other-activity.model';
import { StudentAcademicBackground } from '../student/models/student-academic.model';
import { Enrollment } from '../student/models/enrollment.model';
import { Grade } from '../student/models/grade.model';
import { EmergencyContact } from '../student/models/emergency-contact.model';
import { StudentDocument } from '../student/models/student-document.model';
import { Sequelize } from 'sequelize-typescript';

const models = [
  Student,
  StudentPersonalInfo,
  StudentAcademicRecord,
  StudentProficiencyRecord,
  StudentPublication,
  StudentSocialLink,
  StudentOtherActivity,
  StudentAcademicBackground,
  Enrollment,
  Grade,
  EmergencyContact,
  StudentDocument,
];

@Module({
  imports: [
    SequelizeModule.forRoot({
      ...getDatabaseConfig('students'),
      models,
      autoLoadModels: true,
      synchronize: false, // We're using migrations, so set this to false
    }),
    SequelizeModule.forFeature(models),
  ],
  exports: [SequelizeModule],
})
export class DatabaseModule implements OnModuleInit {
  constructor(private readonly sequelize: Sequelize) {}
  
  onModuleInit() {
    // Any initialization logic can go here
  }
} 