import { Injectable, Logger } from '@nestjs/common';
import { join } from 'path';
import { Sequelize } from 'sequelize-typescript';
import { Umzug, SequelizeStorage } from 'umzug';

@Injectable()
export class MigrationService {
  private readonly logger = new Logger(MigrationService.name);
  private readonly umzug: Umzug;

  constructor(private readonly sequelize: Sequelize) {
    this.umzug = new Umzug({
      migrations: {
        glob: join(__dirname, '../../student/migrations/*.ts'),
      },
      context: sequelize.getQueryInterface(),
      storage: new SequelizeStorage({ sequelize }),
      logger: console,
    });
  }

  async syncDatabase() {
    try {
      this.logger.log('Starting database sync...');
      await this.sequelize.sync({ force: true });
      this.logger.log('Database sync completed successfully');
    } catch (error) {
      this.logger.error('Database sync failed:', error);
      throw error;
    }
  }

  async migrate() {
    try {
      this.logger.log('Starting database migration...');
      await this.syncDatabase(); // First sync the database
      
      this.logger.log(`Looking for migrations in: ${join(__dirname, '../../student/migrations/*.ts')}`);
      const pending = await this.umzug.pending();
      this.logger.log(`Found ${pending.length} pending migrations`);
      
      const executed = await this.umzug.up();
      this.logger.log(`Executed ${executed.length} migrations`);
      
      this.logger.log('Migrations executed successfully');
      return executed;
    } catch (error) {
      this.logger.error('Migration failed:', error);
      throw error;
    }
  }

  async getMigrationStatus() {
    try {
      const pending = await this.umzug.pending();
      const executed = await this.umzug.executed();

      return {
        pending: pending.map(m => m.name),
        executed: executed.map(m => m.name),
        isUpToDate: pending.length === 0
      };
    } catch (error) {
      this.logger.error('Failed to get migration status:', error);
      throw error;
    }
  }

  async createStudentConstraints(): Promise<void> {
    try {
      this.logger.log('Creating student table constraints...');

      // Add unique constraint on userId to prevent multiple students per user
      // Note: This will only apply to non-null userId values
      await this.sequelize.query(`
        ALTER TABLE students 
        ADD CONSTRAINT unique_student_user_id 
        UNIQUE ("userId");
      `);

      // Add unique constraint on email in student_personal_info
      await this.sequelize.query(`
        ALTER TABLE student_personal_info 
        ADD CONSTRAINT unique_student_email 
        UNIQUE (email);
      `);

      this.logger.log('Student table constraints created successfully');
    } catch (error) {
      this.logger.error('Error creating student constraints:', error);
      // Don't throw error if constraints already exist
      if (!error.message.includes('already exists')) {
        throw error;
      }
    }
  }

  async runMigrations(): Promise<any> {
    try {
      this.logger.log('Starting database migration...');
      await this.syncDatabase(); // First sync the database
      
      this.logger.log(`Looking for migrations in: ${join(__dirname, '../../student/migrations/*.ts')}`);
      const pending = await this.umzug.pending();
      this.logger.log(`Found ${pending.length} pending migrations`);
      
      const executed = await this.umzug.up();
      this.logger.log(`Executed ${executed.length} migrations`);
      
      this.logger.log('Migrations executed successfully');
      return executed;
    } catch (error) {
      this.logger.error('Migration failed:', error);
      throw error;
    }
  }
} 