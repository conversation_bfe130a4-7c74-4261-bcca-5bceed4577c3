import { Controller, Post, Get } from '@nestjs/common';
import { MigrationService } from './migration.service';
import { HttpException, HttpStatus } from '@nestjs/common';
import { Logger } from '@nestjs/common';

@Controller('migrate')
export class MigrationController {
  private readonly logger = new Logger(MigrationController.name);

  constructor(private readonly migrationService: MigrationService) {}

  @Post()
  async migrate() {
    await this.migrationService.migrate();
    return { message: 'Migration completed successfully' };
  }

  @Post('sync')
  async sync() {
    await this.migrationService.syncDatabase();
    return { message: 'Database sync completed successfully' };
  }

  @Post('create-student-constraints')
  async createStudentConstraints() {
    try {
      await this.migrationService.createStudentConstraints();
      return { success: true, message: 'Student constraints created successfully' };
    } catch (error) {
      this.logger.error('Failed to create student constraints:', error);
      throw new HttpException(
        error.message || 'Failed to create student constraints',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('run')
  async run() {
    await this.migrationService.runMigrations();
    return { message: 'Migration run completed successfully' };
  }

  @Get('status')
  async getMigrationStatus() {
    const status = await this.migrationService.getMigrationStatus();
    return { status };
  }
} 