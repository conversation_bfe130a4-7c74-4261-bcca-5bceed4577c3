import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { ClientGrpc, Client, Transport } from '@nestjs/microservices';
import { firstValueFrom, timeout, catchError } from 'rxjs';
import { join } from 'path';
import { Student } from '../student/models/student.model';
import { StudentPersonalInfo } from '../student/models/student-personal-info.model';
import { StudentAcademicBackground } from '../student/models/student-academic.model';
import { Application } from '../application/application.model';
import { ApplicationStage } from '../application/application-stage.model';
import {
  ApplicationDocumentRequirement,
  DocumentStatus
} from '../application/application-document-requirement.model';
import { ApplicationProgressRecord } from '../application/application-progress-record.model';
import { ApplicationPayment } from '../application/application-payment.model';
import { ApplicationNote } from '../application/application-note.model';
import { ApplicationActivity } from '../application/application-activity.model';
import {
  ApplicationStatus,
  PaymentStatus
} from '../application/application.model';
import { SEED_CONFIG } from './seed.config';
import { StudentDocument } from '../student/models/student-document.model';

interface AuthService {
  createStudent(data: any): any;
  register(data: any): any;
}

@Injectable()
export class SeedService implements OnModuleInit {
  private readonly logger = new Logger(SeedService.name);
  private authService: AuthService;

  @Client({
    transport: Transport.GRPC,
    options: {
      package: 'auth',
      protoPath: join(process.cwd(), 'libs/shared/dto/src/lib/auth/auth.proto'),
      url: process.env.AUTH_SERVICE_URL || 'auth-service:50052'
    }
  })
  private authClient: ClientGrpc;

  constructor(
    @InjectModel(Student)
    private studentModel: typeof Student,
    @InjectModel(StudentPersonalInfo)
    private studentPersonalInfoModel: typeof StudentPersonalInfo,
    @InjectModel(StudentAcademicBackground)
    private studentAcademicModel: typeof StudentAcademicBackground,
    @InjectModel(Application)
    private applicationModel: typeof Application,
    @InjectModel(ApplicationStage)
    private applicationStageModel: typeof ApplicationStage,
    @InjectModel(ApplicationDocumentRequirement)
    private applicationDocumentRequirementModel: typeof ApplicationDocumentRequirement,
    @InjectModel(ApplicationProgressRecord)
    private applicationProgressRecordModel: typeof ApplicationProgressRecord,
    @InjectModel(ApplicationPayment)
    private applicationPaymentModel: typeof ApplicationPayment,
    @InjectModel(ApplicationNote)
    private applicationNoteModel: typeof ApplicationNote,
    @InjectModel(ApplicationActivity)
    private applicationActivityModel: typeof ApplicationActivity,
    @InjectModel(StudentDocument)
    private studentDocumentModel: typeof StudentDocument
  ) {}

  onModuleInit() {
    this.authService = this.authClient.getService<AuthService>('AuthService');
    this.logger.log('✅ Auth service client initialized');
  }

  async seedAll() {
    this.logger.log('Starting comprehensive seeding process...');
    this.logger.log(`📊 Seed Config: ${JSON.stringify(SEED_CONFIG, null, 2)}`);

    try {
      // Clear existing data if configured
      if (SEED_CONFIG.clearExistingData) {
        await this.clearAllData();
      } else {
        this.logger.log('⏭️ Skipping data clearing (disabled by config)');
      }

      // Seed in order with dynamic ID mapping
      await this.seedStudents();
      const applications = await this.seedApplications();
      await this.seedApplicationStages(applications);
      await this.seedApplicationDocumentRequirements(applications);
      await this.seedApplicationProgressRecords(applications);
      await this.seedApplicationPayments(applications);
      await this.seedApplicationNotes(applications);
      await this.seedApplicationActivities(applications);

      this.logger.log('✅ All seed data created successfully!');
      return { success: true, message: 'All seed data created successfully' };
    } catch (error) {
      this.logger.error('❌ Error during seeding:', error);
      throw error;
    }
  }

  private async clearAllData() {
    this.logger.log('Clearing existing data...');

    // Clear in reverse order of dependencies
    await this.applicationActivityModel.destroy({ where: {}, force: true });
    await this.applicationNoteModel.destroy({ where: {}, force: true });
    await this.applicationPaymentModel.destroy({ where: {}, force: true });
    await this.applicationProgressRecordModel.destroy({
      where: {},
      force: true
    });
    await this.applicationDocumentRequirementModel.destroy({
      where: {},
      force: true
    });
    await this.applicationStageModel.destroy({ where: {}, force: true });
    await this.applicationModel.destroy({ where: {}, force: true });
    // Student-related dependents
    await this.studentDocumentModel.destroy({ where: {}, force: true });
    await this.studentAcademicModel.destroy({ where: {}, force: true });
    await this.studentPersonalInfoModel.destroy({ where: {}, force: true });
    await this.studentModel.destroy({ where: {}, force: true });

    this.logger.log('✅ Existing data cleared');
  }

  async seedStudents() {
    this.logger.log('Seeding students...');

    const studentsData = [
      {
        studentId: '*********',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '******-0101',
        dateOfBirth: new Date('2000-03-15'),
        nationality: 'Canadian',
        passportNumber: 'CA123456789',
        organizationId: '1',
        applicationType: 'F-1 initial',
        personalInfo: {
          firstName: 'John',
          lastName: 'Doe',
          nameInNative: 'John Doe',
          email: '<EMAIL>',
          phone: '******-0101',
          guardianPhone: '******-0102',
          dateOfBirth: new Date('2000-03-15'),
          gender: 'male',
          fatherName: 'Robert Doe',
          motherName: 'Jane Doe',
          nid: '12345678901234',
          passport: 'CA123456789',
          presentAddress: {
            address: '123 Main St.',
            country: 'Canada',
            state: 'Ontario',
            city: 'Toronto',
            postalCode: 'M5V 3A8'
          },
          permanentAddress: {
            address: '456 Home St.',
            country: 'Canada',
            state: 'Ontario',
            city: 'Toronto',
            postalCode: 'M5V 3A8'
          },
          maritalStatus: {
            status: 'single',
            spouseName: '',
            spousePhone: '',
            spousePassport: ''
          },
          sponsor: {
            name: 'Robert Doe',
            relation: 'father',
            phone: '******-0102',
            nid: '3224242424242'
          },
          emergencyContact: {
            lastName: 'Doe',
            middleName: '',
            firstName: 'Jane',
            phoneHome: '******-0103',
            phoneMobile: '******-0104',
            relation: 'mother'
          },
          preferredSubject: ['Computer Science', 'Engineering'],
          preferredCountry: ['Canada', 'USA'],
          socialLinks: [
            {
              platform: 'linkedin',
              url: 'https://linkedin.com/in/johndoe'
            }
          ],
          reference: 'University Professor',
          note: 'Excellent student with strong academic background'
        },
        academic: {
          academic: [
            {
              foreignDegree: false,
              nameOfExam: 'High School Diploma',
              institute: 'Toronto High School',
              subject: 'Science',
              board: 'Ontario School Board',
              grade: 'A+',
              passingYear: '2018'
            }
          ],
          proficiency: [
            {
              nameOfExam: 'IELTS',
              score: {
                overall: 7.5,
                R: 8.0,
                L: 7.5,
                W: 7.0,
                S: 7.5
              },
              examDate: '2024-06-15',
              expiryDate: '2026-06-15',
              note: 'Good overall score'
            }
          ],
          publications: [],
          otherActivities: [
            {
              subject: 'Programming',
              certificationLink: 'https://cert.example.com/programming',
              startDate: '2023-01-01',
              endDate: '2023-12-31'
            }
          ]
        }
      },
      {
        studentId: '*********',
        firstName: 'Sarah',
        lastName: 'Johnson',
        email: '<EMAIL>',
        phone: '******-0201',
        dateOfBirth: new Date('1999-07-22'),
        nationality: 'American',
        passportNumber: 'US987654321',
        organizationId: '1',
        applicationType: 'Transfer',
        personalInfo: {
          firstName: 'Sarah',
          lastName: 'Johnson',
          nameInNative: 'Sarah Johnson',
          email: '<EMAIL>',
          phone: '******-0201',
          guardianPhone: '******-0202',
          dateOfBirth: new Date('1999-07-22'),
          gender: 'female',
          fatherName: 'Michael Johnson',
          motherName: 'Lisa Johnson',
          nid: '98765432109876',
          passport: 'US987654321',
          presentAddress: {
            address: '789 Oak Ave.',
            country: 'USA',
            state: 'California',
            city: 'Los Angeles',
            postalCode: '90210'
          },
          permanentAddress: {
            address: '456 Pine St.',
            country: 'USA',
            state: 'California',
            city: 'Los Angeles',
            postalCode: '90210'
          },
          maritalStatus: {
            status: 'single',
            spouseName: '',
            spousePhone: '',
            spousePassport: ''
          },
          sponsor: {
            name: 'Michael Johnson',
            relation: 'father',
            phone: '******-0202',
            nid: '121212123132323'
          },
          emergencyContact: {
            lastName: 'Johnson',
            middleName: '',
            firstName: 'Lisa',
            phoneHome: '******-0203',
            phoneMobile: '******-0204',
            relation: 'mother'
          },
          preferredSubject: ['Business Administration', 'Marketing'],
          preferredCountry: ['USA', 'Canada'],
          socialLinks: [
            {
              platform: 'linkedin',
              url: 'https://linkedin.com/in/sarahjohnson'
            }
          ],
          reference: 'Business Professor',
          note: 'Strong business background with leadership experience'
        },
        academic: {
          academic: [
            {
              foreignDegree: false,
              nameOfExam: 'Bachelor of Business Administration',
              institute: 'University of California',
              subject: 'Business Administration',
              board: 'University of California',
              grade: '3.8 GPA',
              passingYear: '2023'
            }
          ],
          proficiency: [
            {
              nameOfExam: 'TOEFL',
              score: {
                overall: 105,
                R: 28,
                L: 27,
                W: 25,
                S: 25
              },
              examDate: '2024-05-20',
              expiryDate: '2026-05-20',
              note: 'Excellent English proficiency'
            }
          ],
          publications: [
            {
              subject: 'Digital Marketing Trends',
              journal: 'Business Review',
              publicationDate: '2023-12-01',
              link: 'https://journal.example.com/article'
            }
          ],
          otherActivities: [
            {
              subject: 'Leadership',
              certificationLink: 'https://cert.example.com/leadership',
              startDate: '2022-09-01',
              endDate: '2023-05-31'
            }
          ]
        }
      },
      {
        studentId: '*********',
        firstName: 'Ahmed',
        lastName: 'Hassan',
        email: '<EMAIL>',
        phone: '+************',
        dateOfBirth: new Date('2001-11-08'),
        nationality: 'Saudi',
        passportNumber: 'SA456789123',
        organizationId: '1',
        applicationType: 'Transfer',
        personalInfo: {
          firstName: 'Ahmed',
          lastName: 'Hassan',
          nameInNative: 'أحمد حسن',
          email: '<EMAIL>',
          phone: '+************',
          guardianPhone: '+************',
          dateOfBirth: new Date('2001-11-08'),
          gender: 'male',
          fatherName: 'Mohammed Hassan',
          motherName: 'Fatima Hassan',
          nid: '45678912345678',
          passport: 'SA456789123',
          presentAddress: {
            address: '123 King Fahd Rd.',
            country: 'Saudi Arabia',
            state: 'Riyadh',
            city: 'Riyadh',
            postalCode: '11451'
          },
          permanentAddress: {
            address: '456 Prince Sultan St.',
            country: 'Saudi Arabia',
            state: 'Riyadh',
            city: 'Riyadh',
            postalCode: '11451'
          },
          maritalStatus: {
            status: 'single',
            spouseName: '',
            spousePhone: '',
            spousePassport: ''
          },
          sponsor: {
            name: 'Mohammed Hassan',
            relation: 'father',
            phone: '+************',
            email: '<EMAIL>'
          },
          emergencyContact: {
            lastName: 'Hassan',
            middleName: '',
            firstName: 'Fatima',
            phoneHome: '+************',
            phoneMobile: '+************',
            relation: 'mother'
          },
          preferredSubject: ['Computer Science', 'Information Technology'],
          preferredCountry: ['Saudi Arabia', 'UAE'],
          socialLinks: [
            {
              platform: 'linkedin',
              url: 'https://linkedin.com/in/ahmedhassan'
            }
          ],
          reference: 'Computer Science Professor',
          note: 'Strong technical skills with programming experience'
        },
        academic: {
          academic: [
            {
              foreignDegree: false,
              nameOfExam: 'High School Diploma',
              institute: 'Riyadh High School',
              subject: 'Science',
              board: 'Saudi Ministry of Education',
              grade: '95%',
              passingYear: '2020'
            }
          ],
          proficiency: [
            {
              nameOfExam: 'IELTS',
              score: {
                overall: 6.5,
                R: 7.0,
                L: 6.5,
                W: 6.0,
                S: 6.5
              },
              examDate: '2024-08-10',
              expiryDate: '2026-08-10',
              note: 'Good overall score'
            }
          ],
          publications: [],
          otherActivities: [
            {
              subject: 'Web Development',
              certificationLink: 'https://cert.example.com/webdev',
              startDate: '2023-03-01',
              endDate: '2023-12-31'
            }
          ]
        }
      }
    ];

    for (const studentData of studentsData) {
      const { personalInfo, academic, ...studentBasic } = studentData;

      try {
        // Create student
        const student = await this.studentModel.create(studentBasic);
        this.logger.log(
          `✅ Student created: ${student.studentId} (${studentBasic.email})`
        );

        // Create personal info
        if (personalInfo) {
          await this.studentPersonalInfoModel.create({
            ...personalInfo,
            studentId: student.id
          });
        }

        // Create academic info
        if (academic) {
          await this.studentAcademicModel.create({
            ...academic,
            studentId: student.id
          });
        }

        // Create user in auth-service
        try {
          this.logger.log(
            `🔄 Attempting to create user in auth-service for: ${studentBasic.email}`
          );

          const authRequest = {
            name: `${studentBasic.firstName} ${studentBasic.lastName}`,
            email: studentBasic.email,
            password: 'TempPassword123!', // Temporary password
            phone: studentBasic.phone,
            nationality: '', // Can be derived from country if needed
            organizationName: 'ApplyGoal', // Default organization
            roleName: 'Student', // Default role for students
            departmentName: 'Students', // Default department
            ipAddress: '127.0.0.1',
            userAgent: 'SeedService',
            activateImmediately: true // ← Make users active immediately
          };

          this.logger.log(
            `📤 Auth request data:`,
            JSON.stringify(authRequest, null, 2)
          );

          const authResponse = await firstValueFrom(
            this.authService.register(authRequest).pipe(
              timeout(30000), // 30 second timeout
              catchError((error) => {
                this.logger.error(
                  `❌ gRPC call failed for ${studentBasic.email}:`,
                  error.message
                );
                throw error;
              })
            )
          );

          this.logger.log(
            `📥 Auth response received:`,
            JSON.stringify(authResponse, null, 2)
          );

          if (authResponse && (authResponse as any).success) {
            this.logger.log(
              `✅ User created in auth-service for: ${studentBasic.email}`
            );
          } else {
            this.logger.warn(
              `⚠️ Failed to create user in auth-service for: ${
                studentBasic.email
              } - ${(authResponse as any)?.message || 'Unknown error'}`
            );
          }
        } catch (authError) {
          this.logger.error(
            `❌ Error creating user in auth-service for ${studentBasic.email}:`,
            (authError as Error).message
          );
          this.logger.error(`❌ Error stack:`, (authError as Error).stack);
        }
      } catch (error) {
        this.logger.error(
          `❌ Error creating student ${studentData.studentId}:`,
          error.message
        );
      }
    }

    this.logger.log('✅ Students seeding completed');
  }

  async seedApplications() {
    this.logger.log('Seeding applications...');

    const applicationsData = [
      {
        studentId: '*********',
        universityId: 1,
        universityCountryId: 1,
        universityCountryCampus: 1,
        programId: 1,
        intakeId: 1,
        courseId: 1,
        applicationId: 'APP-2025-001',
        currentStage: 'collecting_documents',
        overallProgress: 15,
        status: ApplicationStatus.APPLIED,
        paymentStatus: PaymentStatus.UNPAID,
        applicationType: 'F-1 initial',
        totalAmount: 500.0,
        paidAmount: 0.0,
        refundAmount: 0.0,
        deliveryMethod: 'online',
        note: 'Application in document collection phase',
        appliedAt: new Date('2025-01-15')
      },
      {
        studentId: '*********',
        universityId: 1,
        universityCountryId: 1,
        universityCountryCampus: 1,
        programId: 2,
        intakeId: 1,
        courseId: 3,
        applicationId: 'APP-2025-002',
        currentStage: 'submitted',
        overallProgress: 35,
        status: ApplicationStatus.APPLIED,
        paymentStatus: PaymentStatus.PAID,
        applicationType: 'Transfer',
        totalAmount: 750.0,
        paidAmount: 750.0,
        refundAmount: 0.0,
        deliveryMethod: 'online',
        note: 'Application submitted and under review',
        appliedAt: new Date('2025-01-20'),
        i20Url: 'https://www.sldttc.org/allpdf/21583473018.pdf',
        studentI20Url: 'https://www.sldttc.org/allpdf/21583473018_student.pdf',
        savisId: '1234567890',
        i94Url: 'https://morth.nic.in/sites/default/files/dd12-13_0.pdf',
        i797cUrl:
          'https://www.aeee.in/wp-content/uploads/2020/08/Sample-pdf.pdf'
      },
      {
        studentId: '*********',
        universityId: 1,
        universityCountryId: 1,
        universityCountryCampus: 2,
        programId: 1,
        intakeId: 2,
        courseId: 2,
        applicationId: 'APP-2025-003',
        currentStage: 'interview',
        overallProgress: 65,
        status: ApplicationStatus.APPLIED,
        paymentStatus: PaymentStatus.PAID,
        applicationType: 'New Program',
        totalAmount: 600.0,
        paidAmount: 600.0,
        refundAmount: 0.0,
        deliveryMethod: 'online',
        note: 'Interview scheduled for next week',
        appliedAt: new Date('2025-01-25')
      },
      {
        studentId: '*********',
        universityId: 1,
        universityCountryId: 1,
        universityCountryCampus: 2,
        programId: 4,
        intakeId: 1,
        courseId: 7,
        applicationId: 'APP-2025-004',
        currentStage: 'enrollment',
        overallProgress: 100,
        status: ApplicationStatus.APPROVED,
        paymentStatus: PaymentStatus.PAID,
        applicationType: 'F-1 initial',
        totalAmount: 800.0,
        paidAmount: 800.0,
        refundAmount: 0.0,
        deliveryMethod: 'online',
        note: 'Successfully enrolled in program',
        appliedAt: new Date('2025-01-30')
      }
    ];

    const createdApplications = [];
    for (const appData of applicationsData) {
      const createdApp = await this.applicationModel.create(appData);
      createdApplications.push(createdApp);
    }

    this.logger.log('✅ Applications seeded successfully');
    return createdApplications;
  }

  async seedApplicationStages(applications: any[]) {
    this.logger.log('Seeding application stages...');

    // Map application IDs by their applicationId field
    const appMap = new Map();
    applications.forEach((app) => {
      appMap.set(app.applicationId, app.id);
    });

    const stagesData = [
      // Application 1 - collecting_documents stage
      {
        applicationId: appMap.get('APP-2025-001'),
        stageName: 'collecting_documents',
        stageOrder: 1,
        status: 'in_progress',
        progressPercentage: 60,
        requirementsMet: 3,
        totalRequirements: 5,
        startDate: new Date('2025-01-15')
      },
      {
        applicationId: appMap.get('APP-2025-001'),
        stageName: 'submitted',
        stageOrder: 2,
        status: 'pending',
        progressPercentage: 0,
        requirementsMet: 0,
        totalRequirements: 3
      },
      {
        applicationId: appMap.get('APP-2025-001'),
        stageName: 'application',
        stageOrder: 3,
        status: 'pending',
        progressPercentage: 0,
        requirementsMet: 0,
        totalRequirements: 2
      },
      {
        applicationId: appMap.get('APP-2025-001'),
        stageName: 'review',
        stageOrder: 4,
        status: 'pending',
        progressPercentage: 0,
        requirementsMet: 0,
        totalRequirements: 4
      },
      {
        applicationId: appMap.get('APP-2025-001'),
        stageName: 'interview',
        stageOrder: 5,
        status: 'pending',
        progressPercentage: 0,
        requirementsMet: 0,
        totalRequirements: 1
      },
      {
        applicationId: appMap.get('APP-2025-001'),
        stageName: 'offer_letter',
        stageOrder: 6,
        status: 'pending',
        progressPercentage: 0,
        requirementsMet: 0,
        totalRequirements: 1
      },
      {
        applicationId: appMap.get('APP-2025-001'),
        stageName: 'visa_application',
        stageOrder: 7,
        status: 'pending',
        progressPercentage: 0,
        requirementsMet: 0,
        totalRequirements: 3
      },
      {
        applicationId: appMap.get('APP-2025-001'),
        stageName: 'enrollment',
        stageOrder: 8,
        status: 'pending',
        progressPercentage: 0,
        requirementsMet: 0,
        totalRequirements: 2
      },

      // Application 2 - submitted stage
      {
        applicationId: appMap.get('APP-2025-002'),
        stageName: 'collecting_documents',
        stageOrder: 1,
        status: 'completed',
        progressPercentage: 100,
        requirementsMet: 5,
        totalRequirements: 5,
        completedDate: new Date('2025-01-20')
      },
      {
        applicationId: appMap.get('APP-2025-002'),
        stageName: 'submitted',
        stageOrder: 2,
        status: 'completed',
        progressPercentage: 100,
        requirementsMet: 3,
        totalRequirements: 3,
        completedDate: new Date('2025-01-25')
      },
      {
        applicationId: appMap.get('APP-2025-002'),
        stageName: 'application',
        stageOrder: 3,
        status: 'completed',
        progressPercentage: 100,
        requirementsMet: 2,
        totalRequirements: 2,
        completedDate: new Date('2025-01-30')
      },
      {
        applicationId: appMap.get('APP-2025-002'),
        stageName: 'review',
        stageOrder: 4,
        status: 'in_progress',
        progressPercentage: 75,
        requirementsMet: 3,
        totalRequirements: 4
      },
      {
        applicationId: appMap.get('APP-2025-002'),
        stageName: 'interview',
        stageOrder: 5,
        status: 'pending',
        progressPercentage: 0,
        requirementsMet: 0,
        totalRequirements: 1
      },
      {
        applicationId: appMap.get('APP-2025-002'),
        stageName: 'offer_letter',
        stageOrder: 6,
        status: 'pending',
        progressPercentage: 0,
        requirementsMet: 0,
        totalRequirements: 1
      },
      {
        applicationId: appMap.get('APP-2025-002'),
        stageName: 'visa_application',
        stageOrder: 7,
        status: 'pending',
        progressPercentage: 0,
        requirementsMet: 0,
        totalRequirements: 3
      },
      {
        applicationId: appMap.get('APP-2025-002'),
        stageName: 'enrollment',
        stageOrder: 8,
        status: 'pending',
        progressPercentage: 0,
        requirementsMet: 0,
        totalRequirements: 2
      },

      // Application 3 - interview stage
      {
        applicationId: appMap.get('APP-2025-003'),
        stageName: 'collecting_documents',
        stageOrder: 1,
        status: 'completed',
        progressPercentage: 100,
        requirementsMet: 5,
        totalRequirements: 5,
        completedDate: new Date('2025-01-25')
      },
      {
        applicationId: appMap.get('APP-2025-003'),
        stageName: 'submitted',
        stageOrder: 2,
        status: 'completed',
        progressPercentage: 100,
        requirementsMet: 3,
        totalRequirements: 3,
        completedDate: new Date('2025-01-30')
      },
      {
        applicationId: appMap.get('APP-2025-003'),
        stageName: 'application',
        stageOrder: 3,
        status: 'completed',
        progressPercentage: 100,
        requirementsMet: 2,
        totalRequirements: 2,
        completedDate: new Date('2025-02-05')
      },
      {
        applicationId: appMap.get('APP-2025-003'),
        stageName: 'review',
        stageOrder: 4,
        status: 'completed',
        progressPercentage: 100,
        requirementsMet: 4,
        totalRequirements: 4,
        completedDate: new Date('2025-02-10')
      },
      {
        applicationId: appMap.get('APP-2025-003'),
        stageName: 'interview',
        stageOrder: 5,
        status: 'in_progress',
        progressPercentage: 50,
        requirementsMet: 1,
        totalRequirements: 2
      },
      {
        applicationId: appMap.get('APP-2025-003'),
        stageName: 'offer_letter',
        stageOrder: 6,
        status: 'pending',
        progressPercentage: 0,
        requirementsMet: 0,
        totalRequirements: 1
      },
      {
        applicationId: appMap.get('APP-2025-003'),
        stageName: 'visa_application',
        stageOrder: 7,
        status: 'pending',
        progressPercentage: 0,
        requirementsMet: 0,
        totalRequirements: 3
      },
      {
        applicationId: appMap.get('APP-2025-003'),
        stageName: 'enrollment',
        stageOrder: 8,
        status: 'pending',
        progressPercentage: 0,
        requirementsMet: 0,
        totalRequirements: 2
      },

      // Application 4 - enrollment stage (completed)
      {
        applicationId: appMap.get('APP-2025-004'),
        stageName: 'collecting_documents',
        stageOrder: 1,
        status: 'completed',
        progressPercentage: 100,
        requirementsMet: 5,
        totalRequirements: 5,
        completedDate: new Date('2025-01-30')
      },
      {
        applicationId: appMap.get('APP-2025-004'),
        stageName: 'submitted',
        stageOrder: 2,
        status: 'completed',
        progressPercentage: 100,
        requirementsMet: 3,
        totalRequirements: 3,
        completedDate: new Date('2025-02-05')
      },
      {
        applicationId: appMap.get('APP-2025-004'),
        stageName: 'application',
        stageOrder: 3,
        status: 'completed',
        progressPercentage: 100,
        requirementsMet: 2,
        totalRequirements: 2,
        completedDate: new Date('2025-02-10')
      },
      {
        applicationId: appMap.get('APP-2025-004'),
        stageName: 'review',
        stageOrder: 4,
        status: 'completed',
        progressPercentage: 100,
        requirementsMet: 4,
        totalRequirements: 4,
        completedDate: new Date('2025-02-15')
      },
      {
        applicationId: appMap.get('APP-2025-004'),
        stageName: 'interview',
        stageOrder: 5,
        status: 'completed',
        progressPercentage: 100,
        requirementsMet: 2,
        totalRequirements: 2,
        completedDate: new Date('2025-02-20')
      },
      {
        applicationId: appMap.get('APP-2025-004'),
        stageName: 'offer_letter',
        stageOrder: 6,
        status: 'completed',
        progressPercentage: 100,
        requirementsMet: 1,
        totalRequirements: 1,
        completedDate: new Date('2025-02-25')
      },
      {
        applicationId: appMap.get('APP-2025-004'),
        stageName: 'visa_application',
        stageOrder: 7,
        status: 'completed',
        progressPercentage: 100,
        requirementsMet: 3,
        totalRequirements: 3,
        completedDate: new Date('2025-03-01')
      },
      {
        applicationId: appMap.get('APP-2025-004'),
        stageName: 'enrollment',
        stageOrder: 8,
        status: 'completed',
        progressPercentage: 100,
        requirementsMet: 2,
        totalRequirements: 2,
        completedDate: new Date('2025-03-05')
      }
    ];

    for (const stageData of stagesData) {
      await this.applicationStageModel.create(stageData);
    }

    this.logger.log('✅ Application stages seeded successfully');
  }

  async seedApplicationDocumentRequirements(applications: any[]) {
    this.logger.log('Seeding application document requirements...');

    const appMap = new Map<string, { id: number; studentId: string }>();
    applications.forEach((app) => {
      appMap.set(app.applicationId, { id: app.id, studentId: app.studentId });
    });

    const documentRequirementsData = [
      // Application 1
      {
        applicationId: appMap.get('APP-2025-001')?.id,
        studentId: appMap.get('APP-2025-001')?.studentId,
        documentTitle: 'identity',
        documentName: 'passport',
        url: 'https://morth.nic.in/sites/default/files/dd12-13_0.pdf',
        documentDescription: 'Valid passport copy',
        isRequired: true,
        allowedFormats: ['pdf', 'jpg', 'png'],
        documentStatus: DocumentStatus.UPLOADED,
        requiredByDate: new Date('2025-01-25'),
        uploadedAt: new Date('2025-01-16'),
        verifiedAt: new Date('2025-01-17'),
        verifiedBy: 1,
        acceptedByApplyGoal: false,
        acceptedByApplyGoalUserId: null,
        acceptedByUniversity: false,
        acceptedByUniversityUserId: null,
        rejectionReason: null
      },
      {
        applicationId: appMap.get('APP-2025-001')?.id,
        studentId: appMap.get('APP-2025-001')?.studentId,
        documentTitle: 'academic',
        documentName: 'transcript',
        url: null,
        documentDescription: 'Academic transcript with grades',
        isRequired: true,
        allowedFormats: ['pdf'],
        documentStatus: DocumentStatus.MISSING,
        requiredByDate: new Date('2025-01-25'),
        uploadedAt: new Date('2025-01-17'),
        verifiedAt: null,
        verifiedBy: null,
        acceptedByApplyGoal: false,
        acceptedByApplyGoalUserId: null,
        acceptedByUniversity: false,
        acceptedByUniversityUserId: null,
        rejectionReason: null
      },
      {
        applicationId: appMap.get('APP-2025-001')?.id,
        studentId: appMap.get('APP-2025-001')?.studentId,
        documentTitle: 'proficiency',
        documentName: 'ielts',
        url: 'https://morth.nic.in/sites/default/files/dd12-13_0.pdf',
        documentDescription: 'IELTS test score report',
        isRequired: true,
        allowedFormats: ['pdf', 'jpg', 'png'],
        documentStatus: DocumentStatus.UPLOADED,
        requiredByDate: new Date('2025-01-30'),
        uploadedAt: null,
        verifiedAt: null,
        verifiedBy: null,
        acceptedByApplyGoal: false,
        acceptedByApplyGoalUserId: null,
        acceptedByUniversity: false,
        acceptedByUniversityUserId: null,
        rejectionReason: null
      },
      {
        applicationId: appMap.get('APP-2025-001')?.id,
        studentId: appMap.get('APP-2025-001')?.studentId,
        documentTitle: 'academic',
        documentName: 'sop',
        url: null,
        documentDescription: 'Statement of Purpose (SOP)',
        isRequired: true,
        allowedFormats: ['pdf', 'doc', 'docx'],
        documentStatus: DocumentStatus.MISSING,
        requiredByDate: new Date('2025-01-25'),
        uploadedAt: new Date('2025-01-18'),
        verifiedAt: new Date('2025-01-19'),
        verifiedBy: 1,
        acceptedByApplyGoal: false,
        acceptedByApplyGoalUserId: null,
        acceptedByUniversity: false,
        acceptedByUniversityUserId: null,
        rejectionReason: null
      },
      {
        applicationId: appMap.get('APP-2025-001')?.id,
        studentId: appMap.get('APP-2025-001')?.studentId,
        documentTitle: 'academic',
        documentName: '10th grade',
        url: 'https://morth.nic.in/sites/default/files/dd12-13_0.pdf',
        documentDescription: 'Statement of Purpose (SOP)',
        isRequired: true,
        allowedFormats: ['pdf', 'doc', 'docx'],
        documentStatus: DocumentStatus.MISSING,
        requiredByDate: new Date('2025-01-25'),
        uploadedAt: new Date('2025-01-18'),
        verifiedAt: new Date('2025-01-19'),
        verifiedBy: 1,
        acceptedByApplyGoal: false,
        acceptedByApplyGoalUserId: null,
        acceptedByUniversity: false,
        acceptedByUniversityUserId: null,
        rejectionReason: null
      },      {
        applicationId: appMap.get('APP-2025-001')?.id,
        studentId: appMap.get('APP-2025-001')?.studentId,
        documentTitle: 'academic',
        documentName: '12th grade',
        url: 'https://morth.nic.in/sites/default/files/dd12-13_0.pdf',
        documentDescription: 'Statement of Purpose (SOP)',
        isRequired: true,
        allowedFormats: ['pdf', 'doc', 'docx'],
        documentStatus: DocumentStatus.MISSING,
        requiredByDate: new Date('2025-01-25'),
        uploadedAt: new Date('2025-01-18'),
        verifiedAt: new Date('2025-01-19'),
        verifiedBy: 1,
        acceptedByApplyGoal: false,
        acceptedByApplyGoalUserId: null,
        acceptedByUniversity: false,
        acceptedByUniversityUserId: null,
        rejectionReason: null
      },
      {
        applicationId: appMap.get('APP-2025-001')?.id,
        studentId: appMap.get('APP-2025-001')?.studentId,
        documentTitle: 'academic',
        documentName: 'lor',
        url: null,
        documentDescription: 'Letter of Recommendation (LOR)',
        isRequired: true,
        allowedFormats: ['pdf', 'doc', 'docx'],
        documentStatus: DocumentStatus.REQUESTED,
        requiredByDate: new Date('2025-01-30'),
        uploadedAt: null,
        verifiedAt: null,
        verifiedBy: null,
        acceptedByApplyGoal: false,
        acceptedByApplyGoalUserId: null,
        acceptedByUniversity: false,
        acceptedByUniversityUserId: null,
        rejectionReason: null
      },
      // Application 2
      {
        applicationId: appMap.get('APP-2025-002')?.id,
        studentId: appMap.get('APP-2025-002')?.studentId,
        documentTitle: 'identity',
        documentName: 'passport',
        url: 'https://cgbdsydney.gov.bd/wp-content/uploads/2023/09/E-Passport-Documents.pdf',
        documentDescription: 'Valid passport copy',
        isRequired: true,
        allowedFormats: ['pdf', 'jpg', 'png'],
        documentStatus: DocumentStatus.UPLOADED,
        requiredByDate: new Date('2025-01-25'),
        uploadedAt: new Date('2025-01-20'),
        verifiedAt: new Date('2025-01-21'),
        verifiedBy: 1,
        acceptedByApplyGoal: false,
        acceptedByApplyGoalUserId: null,
        acceptedByUniversity: false,
        acceptedByUniversityUserId: null,
        rejectionReason: null
      },
      {
        applicationId: appMap.get('APP-2025-002')?.id,
        studentId: appMap.get('APP-2025-002')?.studentId,
        documentTitle: 'identity',
        documentName: 'profile',
        url: 'https://cdn.pixabay.com/photo/2021/08/11/11/15/man-6538205_640.jpg',
        documentDescription: 'TOEFL test score report',
        isRequired: true,
        allowedFormats: ['pdf', 'jpg', 'png'],
        documentStatus: DocumentStatus.UPLOADED,
        requiredByDate: new Date('2025-01-25'),
        uploadedAt: new Date('2025-01-22'),
        verifiedAt: new Date('2025-01-23'),
        verifiedBy: 1,
        acceptedByApplyGoal: false,
        acceptedByApplyGoalUserId: null,
        acceptedByUniversity: false,
        acceptedByUniversityUserId: null,
        rejectionReason: null
      },
      {
        applicationId: appMap.get('APP-2025-002')?.id,
        studentId: appMap.get('APP-2025-002')?.studentId,
        documentTitle: 'academic',
        documentName: 'transcript',
        url: null,
        documentDescription: 'Academic transcript with grades',
        isRequired: true,
        allowedFormats: ['pdf'],
        documentStatus: DocumentStatus.UPLOADED,
        requiredByDate: new Date('2025-01-25'),
        uploadedAt: new Date('2025-01-21'),
        verifiedAt: new Date('2025-01-22'),
        verifiedBy: 1,
        acceptedByApplyGoal: false,
        acceptedByApplyGoalUserId: null,
        acceptedByUniversity: false,
        acceptedByUniversityUserId: null,
        rejectionReason: null
      },
      {
        applicationId: appMap.get('APP-2025-002')?.id,
        studentId: appMap.get('APP-2025-002')?.studentId,
        documentTitle: 'proficiency',
        documentName: 'toefl',
        url: 'https://ontheline.trincoll.edu/images/bookdown/sample-local-pdf.pdf',
        documentDescription: 'TOEFL test score report',
        isRequired: true,
        allowedFormats: ['pdf', 'jpg', 'png'],
        documentStatus: DocumentStatus.UPLOADED,
        requiredByDate: new Date('2025-01-25'),
        uploadedAt: new Date('2025-01-22'),
        verifiedAt: new Date('2025-01-23'),
        verifiedBy: 1,
        acceptedByApplyGoal: false,
        acceptedByApplyGoalUserId: null,
        acceptedByUniversity: false,
        acceptedByUniversityUserId: null,
        rejectionReason: null
      },
      {
        applicationId: appMap.get('APP-2025-002')?.id,
        studentId: appMap.get('APP-2025-002')?.studentId,
        documentTitle: 'proficiency',
        documentName: 'ielts',
        url: 'https://greenseducation.com/wp-content/uploads/2020/03/cambridge-ielts-8.pdf',
        documentDescription: 'TOEFL test score report',
        isRequired: true,
        allowedFormats: ['pdf', 'jpg', 'png'],
        documentStatus: DocumentStatus.UPLOADED,
        requiredByDate: new Date('2025-01-25'),
        uploadedAt: new Date('2025-01-22'),
        verifiedAt: new Date('2025-01-23'),
        verifiedBy: 1,
        acceptedByApplyGoal: false,
        acceptedByApplyGoalUserId: null,
        acceptedByUniversity: false,
        acceptedByUniversityUserId: null,
        rejectionReason: null
      },
      {
        applicationId: appMap.get('APP-2025-002')?.id,
        studentId: appMap.get('APP-2025-002')?.studentId,
        documentTitle: 'academic',
        documentName: '12th grade',
        url: 'https://morth.nic.in/sites/default/files/dd12-13_0.pdf',
        documentDescription: 'TOEFL test score report',
        isRequired: true,
        allowedFormats: ['pdf', 'jpg', 'png'],
        documentStatus: DocumentStatus.UPLOADED,
        requiredByDate: new Date('2025-01-25'),
        uploadedAt: new Date('2025-01-22'),
        verifiedAt: new Date('2025-01-23'),
        verifiedBy: 1,
        acceptedByApplyGoal: false,
        acceptedByApplyGoalUserId: null,
        acceptedByUniversity: false,
        acceptedByUniversityUserId: null,
        rejectionReason: null
      },
      {
        applicationId: appMap.get('APP-2025-002')?.id,
        studentId: appMap.get('APP-2025-002')?.studentId,
        documentTitle: 'academic',
        documentName: '10th grade',
        url: 'https://ontheline.trincoll.edu/images/bookdown/sample-local-pdf.pdf',
        documentDescription: 'TOEFL test score report',
        isRequired: true,
        allowedFormats: ['pdf', 'jpg', 'png'],
        documentStatus: DocumentStatus.UPLOADED,
        requiredByDate: new Date('2025-01-25'),
        uploadedAt: new Date('2025-01-22'),
        verifiedAt: new Date('2025-01-23'),
        verifiedBy: 1,
        acceptedByApplyGoal: false,
        acceptedByApplyGoalUserId: null,
        acceptedByUniversity: false,
        acceptedByUniversityUserId: null,
        rejectionReason: null
      },
      {
        applicationId: appMap.get('APP-2025-002')?.id,
        studentId: appMap.get('APP-2025-002')?.studentId,
        documentTitle: 'academic',
        documentName: 'sop',
        url: null,
        documentDescription: 'Statement of Purpose (SOP)',
        isRequired: true,
        allowedFormats: ['pdf', 'doc', 'docx'],
        documentStatus: DocumentStatus.UPLOADED,
        requiredByDate: new Date('2025-01-25'),
        uploadedAt: new Date('2025-01-23'),
        verifiedAt: new Date('2025-01-24'),
        verifiedBy: 1,
        acceptedByApplyGoal: false,
        acceptedByApplyGoalUserId: null,
        acceptedByUniversity: false,
        acceptedByUniversityUserId: null,
        rejectionReason: null
      },
      {
        applicationId: appMap.get('APP-2025-002')?.id,
        studentId: appMap.get('APP-2025-002')?.studentId,
        documentTitle: 'academic',
        documentName: 'lor',
        url: null,
        documentDescription: 'Letter of Recommendation (LOR)',
        isRequired: true,
        allowedFormats: ['pdf', 'doc', 'docx'],
        documentStatus: DocumentStatus.UPLOADED,
        requiredByDate: new Date('2025-01-25'),
        uploadedAt: new Date('2025-01-24'),
        verifiedAt: new Date('2025-01-25'),
        verifiedBy: 1,
        acceptedByApplyGoal: false,
        acceptedByApplyGoalUserId: null,
        acceptedByUniversity: false,
        acceptedByUniversityUserId: null,
        rejectionReason: null
      }
    ];

    for (const doc of documentRequirementsData) {
      await this.applicationDocumentRequirementModel.create(doc);
    }

    this.logger.log('✅ Application document requirements seeded successfully');
  }

  async seedApplicationProgressRecords(applications: any[]) {
    this.logger.log('Seeding application progress records...');

    // Map application IDs by their applicationId field
    const appMap = new Map();
    applications.forEach((app) => {
      appMap.set(app.applicationId, app.id);
    });

    const progressRecordsData = [
      {
        applicationId: appMap.get('APP-2025-001'),
        recordType: 'document_uploaded',
        title: 'Passport Uploaded',
        description: 'Student uploaded valid passport document',
        status: 'completed',
        recordDate: new Date('2025-01-16T10:30:00Z'),
        amount: null,
        currency: 'USD',
        proofLinks: ['https://docs.example.com/passport.pdf'],
        attachments: [],
        createdBy: 1
      },
      {
        applicationId: appMap.get('APP-2025-001'),
        recordType: 'document_uploaded',
        title: 'Academic Transcript Uploaded',
        description: 'Student uploaded academic transcript',
        status: 'completed',
        recordDate: new Date('2025-01-17T14:15:00Z'),
        amount: null,
        currency: 'USD',
        proofLinks: ['https://docs.example.com/transcript.pdf'],
        attachments: [],
        createdBy: 1
      },
      {
        applicationId: appMap.get('APP-2025-001'),
        recordType: 'document_uploaded',
        title: 'Personal Statement Uploaded',
        description: 'Student uploaded personal statement',
        status: 'completed',
        recordDate: new Date('2025-01-18T16:45:00Z'),
        amount: null,
        currency: 'USD',
        proofLinks: ['https://docs.example.com/statement.pdf'],
        attachments: [],
        createdBy: 1
      },
      {
        applicationId: appMap.get('APP-2025-002'),
        recordType: 'stage_completed',
        title: 'Documents Collection Completed',
        description: 'All required documents have been uploaded and verified',
        status: 'completed',
        recordDate: new Date('2025-01-25T09:00:00Z'),
        amount: null,
        currency: 'USD',
        proofLinks: [],
        attachments: [],
        createdBy: 1
      },
      {
        applicationId: appMap.get('APP-2025-002'),
        recordType: 'stage_completed',
        title: 'Application Submitted',
        description: 'Application submitted to university for review',
        status: 'completed',
        recordDate: new Date('2025-01-25T11:30:00Z'),
        amount: null,
        currency: 'USD',
        proofLinks: [],
        attachments: [],
        createdBy: 1
      },
      {
        applicationId: appMap.get('APP-2025-003'),
        recordType: 'interview_scheduled',
        title: 'Interview Scheduled',
        description: 'Interview scheduled for February 10th at 2 PM',
        status: 'in_progress',
        recordDate: new Date('2025-01-28T09:15:00Z'),
        amount: null,
        currency: 'USD',
        proofLinks: ['https://meet.example.com/interview-link'],
        attachments: [],
        createdBy: 1
      }
    ];

    for (const progressData of progressRecordsData) {
      await this.applicationProgressRecordModel.create(progressData);
    }

    this.logger.log('✅ Application progress records seeded successfully');
  }

  async seedApplicationPayments(applications: any[]) {
    this.logger.log('Seeding application payments...');

    // Map application IDs by their applicationId field
    const appMap = new Map();
    applications.forEach((app) => {
      appMap.set(app.applicationId, app.id);
    });

    const paymentsData = [
      {
        applicationId: appMap.get('APP-2025-002'),
        paymentType: 'application_fee',
        amount: 500.0,
        currency: 'USD',
        paymentMethod: 'credit_card',
        transactionReference: 'TXN-2025-001',
        status: 'confirmed',
        paymentDate: new Date('2025-01-20T11:30:00Z'),
        confirmedAt: new Date('2025-01-20T11:35:00Z'),
        confirmedBy: 1,
        receiptPath: '/receipts/txn-2025-001.pdf',
        notes: 'Application fee paid successfully'
      },
      {
        applicationId: appMap.get('APP-2025-002'),
        paymentType: 'processing_fee',
        amount: 250.0,
        currency: 'USD',
        paymentMethod: 'credit_card',
        transactionReference: 'TXN-2025-002',
        status: 'confirmed',
        paymentDate: new Date('2025-01-21T14:20:00Z'),
        confirmedAt: new Date('2025-01-21T14:25:00Z'),
        confirmedBy: 1,
        receiptPath: '/receipts/txn-2025-002.pdf',
        notes: 'Processing fee paid'
      },
      {
        applicationId: appMap.get('APP-2025-003'),
        paymentType: 'application_fee',
        amount: 600.0,
        currency: 'USD',
        paymentMethod: 'bank_transfer',
        transactionReference: 'TXN-2025-003',
        status: 'confirmed',
        paymentDate: new Date('2025-01-22T16:45:00Z'),
        confirmedAt: new Date('2025-01-23T09:00:00Z'),
        confirmedBy: 1,
        receiptPath: '/receipts/txn-2025-003.pdf',
        notes: 'Application fee paid via bank transfer'
      },
      {
        applicationId: appMap.get('APP-2025-004'),
        paymentType: 'application_fee',
        amount: 500.0,
        currency: 'USD',
        paymentMethod: 'credit_card',
        transactionReference: 'TXN-2025-004',
        status: 'confirmed',
        paymentDate: new Date('2025-01-30T10:15:00Z'),
        confirmedAt: new Date('2025-01-30T10:20:00Z'),
        confirmedBy: 1,
        receiptPath: '/receipts/txn-2025-004.pdf',
        notes: 'Application fee paid'
      },
      {
        applicationId: appMap.get('APP-2025-004'),
        paymentType: 'enrollment_fee',
        amount: 300.0,
        currency: 'USD',
        paymentMethod: 'credit_card',
        transactionReference: 'TXN-2025-005',
        status: 'confirmed',
        paymentDate: new Date('2025-02-15T10:20:00Z'),
        confirmedAt: new Date('2025-02-15T10:25:00Z'),
        confirmedBy: 1,
        receiptPath: '/receipts/txn-2025-005.pdf',
        notes: 'Enrollment fee paid'
      }
    ];

    for (const paymentData of paymentsData) {
      await this.applicationPaymentModel.create(paymentData);
    }

    this.logger.log('✅ Application payments seeded successfully');
  }

  async seedApplicationNotes(applications: any[]) {
    this.logger.log('Seeding application notes...');

    // Map application IDs by their applicationId field
    const appMap = new Map();
    applications.forEach((app) => {
      appMap.set(app.applicationId, app.id);
    });

    const notesData = [
      {
        applicationId: appMap.get('APP-2025-001'),
        noteType: 'internal',
        title: 'Document Upload Reminder',
        content:
          'Student needs to provide IELTS score by next week. Please follow up.',
        createdBy: 1,
        isPrivate: true,
        createdAt: new Date('2025-01-18T15:30:00Z')
      },
      {
        applicationId: appMap.get('APP-2025-001'),
        noteType: 'student',
        title: 'Document Upload Confirmation',
        content:
          'I have uploaded my passport and academic transcript. Working on IELTS score.',
        createdBy: 1,
        isPrivate: false,
        createdAt: new Date('2025-01-17T12:00:00Z')
      },
      {
        applicationId: appMap.get('APP-2025-002'),
        noteType: 'student',
        title: 'Payment Confirmation',
        content:
          'Application fee paid successfully. Transaction ID: TXN-2025-001',
        createdBy: 2,
        isPrivate: false,
        createdAt: new Date('2025-01-20T12:00:00Z')
      },
      {
        applicationId: appMap.get('APP-2025-002'),
        noteType: 'internal',
        title: 'Application Review',
        content:
          'All documents verified. Application ready for university review.',
        createdBy: 1,
        isPrivate: true,
        createdAt: new Date('2025-01-25T14:00:00Z')
      },
      {
        applicationId: appMap.get('APP-2025-003'),
        noteType: 'internal',
        title: 'Interview Scheduling',
        content:
          'Interview scheduled for February 10th at 2 PM. Student confirmed availability.',
        createdBy: 1,
        isPrivate: true,
        createdAt: new Date('2025-01-28T09:15:00Z')
      },
      {
        applicationId: appMap.get('APP-2025-004'),
        noteType: 'internal',
        title: 'Enrollment Confirmation',
        content:
          'Student successfully enrolled. All requirements met and fees paid.',
        createdBy: 1,
        isPrivate: true,
        createdAt: new Date('2025-03-05T16:00:00Z')
      }
    ];

    for (const noteData of notesData) {
      await this.applicationNoteModel.create(noteData);
    }

    this.logger.log('✅ Application notes seeded successfully');
  }

  async seedApplicationActivities(applications: any[]) {
    this.logger.log('Seeding application activities...');

    // Map application IDs by their applicationId field
    const appMap = new Map();
    applications.forEach((app) => {
      appMap.set(app.applicationId, app.id);
    });

    const activitiesData = [
      {
        applicationId: appMap.get('APP-2025-001'),
        activityType: 'application_created',
        activityDescription: 'Application created for undergraduate program',
        performedBy: 1,
        performedAt: new Date('2025-01-15T09:00:00Z'),
        metadata: JSON.stringify({
          program: 'Computer Science',
          university: 'University of Toronto'
        })
      },
      {
        applicationId: appMap.get('APP-2025-001'),
        activityType: 'document_uploaded',
        activityDescription: 'Passport document uploaded',
        performedBy: 1,
        performedAt: new Date('2025-01-16T10:30:00Z'),
        metadata: JSON.stringify({
          documentType: 'passport',
          fileSize: '2.5MB'
        })
      },
      {
        applicationId: appMap.get('APP-2025-002'),
        activityType: 'payment_completed',
        activityDescription: 'Application fee payment completed',
        performedBy: 2,
        performedAt: new Date('2025-01-20T11:30:00Z'),
        metadata: JSON.stringify({
          amount: 500.0,
          currency: 'USD',
          method: 'credit_card'
        })
      },
      {
        applicationId: appMap.get('APP-2025-002'),
        activityType: 'stage_completed',
        activityDescription: 'Document collection stage completed',
        performedBy: 1,
        performedAt: new Date('2025-01-25T09:00:00Z'),
        metadata: JSON.stringify({
          stage: 'collecting_documents',
          documentsCount: 5
        })
      },
      {
        applicationId: appMap.get('APP-2025-003'),
        activityType: 'interview_scheduled',
        activityDescription: 'Interview scheduled with university',
        performedBy: 1,
        performedAt: new Date('2025-01-28T09:15:00Z'),
        metadata: JSON.stringify({
          interviewDate: '2025-02-10T14:00:00Z',
          duration: '45 minutes'
        })
      },
      {
        applicationId: appMap.get('APP-2025-004'),
        activityType: 'enrollment_completed',
        activityDescription: 'Student successfully enrolled in program',
        performedBy: 1,
        performedAt: new Date('2025-03-05T16:00:00Z'),
        metadata: JSON.stringify({
          program: 'Master of Business Administration',
          startDate: '2025-09-01'
        })
      }
    ];

    for (const activityData of activitiesData) {
      await this.applicationActivityModel.create(activityData);
    }

    this.logger.log('✅ Application activities seeded successfully');
  }
}
