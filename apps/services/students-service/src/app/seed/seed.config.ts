export interface SeedConfig {
  autoSeed: boolean;
  clearExistingData: boolean;
  seedOnStartup: boolean;
  developmentOnly: boolean;
  logLevel: 'info' | 'debug' | 'warn' | 'error';
}

export const getSeedConfig = (): SeedConfig => {
  const isDevelopment = process.env.NODE_ENV === 'development';
  const autoSeed = process.env.AUTO_SEED === 'true' || isDevelopment;
  
  return {
    autoSeed,
    clearExistingData: process.env.SEED_CLEAR_EXISTING !== 'false', // Default to true
    seedOnStartup: process.env.SEED_ON_STARTUP !== 'false', // Default to true
    developmentOnly: process.env.SEED_DEVELOPMENT_ONLY !== 'false', // Default to true
    logLevel: (process.env.SEED_LOG_LEVEL as any) || 'info'
  };
};

export const SEED_CONFIG = getSeedConfig(); 