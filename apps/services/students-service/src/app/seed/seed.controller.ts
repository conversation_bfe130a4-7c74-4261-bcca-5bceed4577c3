import { <PERSON>, Post, Get, HttpStatus, HttpException, Logger } from '@nestjs/common';
import { SeedService } from './seed.service';

@Controller('seed')
export class SeedController {
  private readonly logger = new Logger(SeedController.name);

  constructor(private readonly seedService: SeedService) {}

  @Post('all')
  async seedAll() {
    try {
      this.logger.log('Starting comprehensive seeding process...');
      const result = await this.seedService.seedAll();
      return {
        success: true,
        message: 'All seed data created successfully',
        data: result
      };
    } catch (error) {
      this.logger.error('Failed to seed all data:', error);
      throw new HttpException(
        error.message || 'Failed to seed data',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('students')
  async seedStudents() {
    try {
      this.logger.log('Seeding students...');
      await this.seedService.seedStudents();
      return {
        success: true,
        message: 'Students seeded successfully'
      };
    } catch (error) {
      this.logger.error('Failed to seed students:', error);
      throw new HttpException(
        error.message || 'Failed to seed students',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('applications')
  async seedApplications() {
    try {
      this.logger.log('Seeding applications...');
      await this.seedService.seedApplications();
      return {
        success: true,
        message: 'Applications seeded successfully'
      };
    } catch (error) {
      this.logger.error('Failed to seed applications:', error);
      throw new HttpException(
        error.message || 'Failed to seed applications',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('application-stages')
  async seedApplicationStages() {
    try {
      this.logger.log('Seeding application stages...');
      // Get applications first, then seed stages
      const applications = await this.seedService.seedApplications();
      await this.seedService.seedApplicationStages(applications);
      return {
        success: true,
        message: 'Application stages seeded successfully'
      };
    } catch (error) {
      this.logger.error('Failed to seed application stages:', error);
      throw new HttpException(
        error.message || 'Failed to seed application stages',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('document-requirements')
  async seedDocumentRequirements() {
    try {
      this.logger.log('Seeding document requirements...');
      // Get applications first, then seed document requirements
      const applications = await this.seedService.seedApplications();
      await this.seedService.seedApplicationDocumentRequirements(applications);
      return {
        success: true,
        message: 'Document requirements seeded successfully'
      };
    } catch (error) {
      this.logger.error('Failed to seed document requirements:', error);
      throw new HttpException(
        error.message || 'Failed to seed document requirements',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('progress-records')
  async seedProgressRecords() {
    try {
      this.logger.log('Seeding progress records...');
      // Get applications first, then seed progress records
      const applications = await this.seedService.seedApplications();
      await this.seedService.seedApplicationProgressRecords(applications);
      return {
        success: true,
        message: 'Progress records seeded successfully'
      };
    } catch (error) {
      this.logger.error('Failed to seed progress records:', error);
      throw new HttpException(
        error.message || 'Failed to seed progress records',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('payments')
  async seedPayments() {
    try {
      this.logger.log('Seeding payments...');
      // Get applications first, then seed payments
      const applications = await this.seedService.seedApplications();
      await this.seedService.seedApplicationPayments(applications);
      return {
        success: true,
        message: 'Payments seeded successfully'
      };
    } catch (error) {
      this.logger.error('Failed to seed payments:', error);
      throw new HttpException(
        error.message || 'Failed to seed payments',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('notes')
  async seedNotes() {
    try {
      this.logger.log('Seeding notes...');
      // Get applications first, then seed notes
      const applications = await this.seedService.seedApplications();
      await this.seedService.seedApplicationNotes(applications);
      return {
        success: true,
        message: 'Notes seeded successfully'
      };
    } catch (error) {
      this.logger.error('Failed to seed notes:', error);
      throw new HttpException(
        error.message || 'Failed to seed notes',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('activities')
  async seedActivities() {
    try {
      this.logger.log('Seeding activities...');
      // Get applications first, then seed activities
      const applications = await this.seedService.seedApplications();
      await this.seedService.seedApplicationActivities(applications);
      return {
        success: true,
        message: 'Activities seeded successfully'
      };
    } catch (error) {
      this.logger.error('Failed to seed activities:', error);
      throw new HttpException(
        error.message || 'Failed to seed activities',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('status')
  async getSeedStatus() {
    try {
      // This could be enhanced to check actual data counts
      return {
        success: true,
        message: 'Seed endpoints available',
        endpoints: [
          'POST /seed/all - Seed all data',
          'POST /seed/students - Seed students only',
          'POST /seed/applications - Seed applications only',
          'POST /seed/application-stages - Seed application stages only',
          'POST /seed/document-requirements - Seed document requirements only',
          'POST /seed/progress-records - Seed progress records only',
          'POST /seed/payments - Seed payments only',
          'POST /seed/notes - Seed notes only',
          'POST /seed/activities - Seed activities only'
        ]
      };
    } catch (error) {
      this.logger.error('Failed to get seed status:', error);
      throw new HttpException(
        error.message || 'Failed to get seed status',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
} 