import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { SeedService } from './seed.service';
import { SeedController } from './seed.controller';

// Import all models needed for seeding
import { Student } from '../student/models/student.model';
import { StudentPersonalInfo } from '../student/models/student-personal-info.model';
import { StudentAcademicBackground } from '../student/models/student-academic.model';
import { Application } from '../application/application.model';
import { ApplicationStage } from '../application/application-stage.model';
import { ApplicationDocumentRequirement } from '../application/application-document-requirement.model';
import { ApplicationProgressRecord } from '../application/application-progress-record.model';
import { ApplicationPayment } from '../application/application-payment.model';
import { ApplicationNote } from '../application/application-note.model';
import { ApplicationActivity } from '../application/application-activity.model';
import { StudentDocument } from '../student/models/student-document.model';

@Module({
      imports: [
      SequelizeModule.forFeature([
        Student,
        StudentPersonalInfo,
        StudentAcademicBackground,
        StudentDocument,
        Application,
        ApplicationStage,
        ApplicationDocumentRequirement,
        ApplicationProgressRecord,
        ApplicationPayment,
        ApplicationNote,
        ApplicationActivity
      ]),
  ],
  controllers: [SeedController],
  providers: [SeedService],
  exports: [SeedService]
})
export class SeedModule {} 