import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { MulterFile } from '@apply-goal-backend/common';
import { StudentDocument } from '../models/student-document.model';
import { Student } from '../models/student.model';
import {
  EnhancedUploadService,
  EnhancedUploadResult
} from './enhanced-upload.service';
import {
  CreateStudentDocumentsDto,
  CreateStudentDocumentsResponseDto,
  GetStudentDocumentsQueryDto,
  UpdateDocumentVerificationDto,
  StudentDocumentResponseDto
} from '../dto/create-student-documents.dto';
import { Op } from 'sequelize';

@Injectable()
export class StudentDocumentsService {
  private readonly logger = new Logger(StudentDocumentsService.name);

  constructor(
    @InjectModel(StudentDocument)
    private readonly studentDocumentModel: typeof StudentDocument,
    @InjectModel(Student)
    private readonly studentModel: typeof Student,
    private readonly enhancedUploadService: EnhancedUploadService
  ) {}

  /**
   * Create or update student documents from multipart form upload
   */
  async createOrUpdate(
    studentId: string,
    metadataDto: CreateStudentDocumentsDto,
    uploadedFiles: { [fieldname: string]: MulterFile[] }
  ): Promise<CreateStudentDocumentsResponseDto> {
    this.logger.log(`Processing document upload for student ${studentId}`);

    // Verify student exists
    const student = await this.studentModel.findOne({ where: { studentId } });
    if (!student) {
      throw new NotFoundException(`Student with ID ${studentId} not found`);
    }

    // Validate all files before processing
    this.enhancedUploadService.validateFiles(uploadedFiles);

    const uploadResults: EnhancedUploadResult[] = [];
    const errors: string[] = [];

    try {
      // 1. Process regular files (profile, academic, proficiency, sponsor)
      const regularResults =
        await this.enhancedUploadService.uploadMultipleDocuments(
          uploadedFiles,
          studentId
        );
      uploadResults.push(...regularResults);

      // 2. Process dependent and children files with indexed metadata
      if (
        metadataDto.takeDependents &&
        (metadataDto.dependents || metadataDto.children)
      ) {
        const dependentResults =
          await this.enhancedUploadService.uploadDependentDocuments(
            uploadedFiles,
            studentId,
            metadataDto.dependents || [],
            metadataDto.children || []
          );
        uploadResults.push(...dependentResults);
      }

      // 3. Save all documents to database
      const savedDocuments = await this.saveDocumentsToDatabase(
        uploadResults,
        studentId
      );

      console.log(
        'Saved documents++++++++++++++++++++++++++++=',
        savedDocuments,
        'uploaded documents++++++++++++++=>',
        uploadResults
      );

      // 4. Process application requirements with URLs in single operation
      await this.processApplicationRequirementsWithUrls(
        studentId,
        savedDocuments
      );

      return {
        success: true,
        message: `Successfully uploaded ${savedDocuments.length} documents`,
        documents: savedDocuments.map((doc) => this.mapToResponseDto(doc)),
        totalUploaded: savedDocuments.length,
        failedUploads: errors.length,
        errors: errors.length > 0 ? errors : undefined
      };
    } catch (error) {
      this.logger.error(
        `Failed to process documents for student ${studentId}:`,
        error
      );
      throw new BadRequestException(`Document upload failed: ${error.message}`);
    }
  }

  /**
   * Get student documents with optional filtering
   */
  async getStudentDocuments(
    studentId: string,
    query: GetStudentDocumentsQueryDto
  ): Promise<StudentDocumentResponseDto[]> {
    const whereClause: any = { studentId };

    if (query.section) {
      whereClause.section = query.section;
    }

    if (query.field) {
      whereClause.field = query.field;
    }

    if (query.verificationStatus) {
      whereClause.verificationStatus = query.verificationStatus;
    }

    if (!query.includeInactive) {
      whereClause.isActive = true;
    }
    console.log('Qeury data', whereClause);

    const documents = await this.studentDocumentModel.findAll({
      where: whereClause,
      order: [['createdAt', 'DESC']]
    });

    const result = documents.map((doc) => this.mapToResponseDto(doc));
    return result;
  }

  /**
   * Update document verification status
   */
  async updateDocumentVerification(
    documentId: number,
    updateDto: UpdateDocumentVerificationDto
  ): Promise<StudentDocumentResponseDto> {
    const document = await this.studentDocumentModel.findByPk(documentId);
    if (!document) {
      throw new NotFoundException(`Document with ID ${documentId} not found`);
    }

    await document.update({
      verificationStatus: updateDto.verificationStatus,
      notes: updateDto.notes,
      expiryDate: updateDto.expiryDate
    });

    return this.mapToResponseDto(document);
  }

  /**
   * Delete a document (soft delete by setting isActive to false)
   */
  async deleteDocument(documentId: number): Promise<void> {
    const document = await this.studentDocumentModel.findByPk(documentId);
    if (!document) {
      throw new NotFoundException(`Document with ID ${documentId} not found`);
    }

    await document.update({ isActive: false });
  }

  // Private helper methods

  private async saveDocumentsToDatabase(
    uploadResults: EnhancedUploadResult[],
    studentId: string
  ): Promise<StudentDocument[]> {
    const documentsToCreate = uploadResults.map((result) => ({
      studentId: studentId,
      section: this.determineSectionFromResult(result),
      field: this.extractFieldFromObjectKey(result.objectKey),
      filename: result.filename,
      url: result.url,
      mimeType: result.mimeType,
      fileSize: result.fileSize,
      originalName: result.originalName,
      bucket: result.bucket,
      objectKey: result.objectKey,
      metadata: (result as any).metadata || null,
      isActive: true,
      verificationStatus: 'pending' as const
    }));

    return await this.studentDocumentModel.bulkCreate(documentsToCreate);
  }

  private determineSectionFromResult(result: EnhancedUploadResult): string {
    // Extract section from object key: students/{studentId}/{section}/{filename}
    const pathParts = result.objectKey.split('/');
    return pathParts[2] || 'other';
  }

  private extractFieldFromObjectKey(objectKey: string): string {
    // Extract field from filename in object key
    const pathParts = objectKey.split('/');
    const filename = pathParts[pathParts.length - 1];
    const fieldPart = filename.split('_')[0];
    return fieldPart;
  }

  private mapToResponseDto(
    document: StudentDocument
  ): StudentDocumentResponseDto {
    return {
      id: Number(document.id),
      studentId: Number(document.studentId),
      section: document.section,
      field: document.field,
      filename: document.filename,
      url: document.url,
      mimeType: document.mimeType || '',
      fileSize: Number(document.fileSize || 0),
      originalName: document.originalName || '',
      bucket: document.bucket || '',
      objectKey: document.objectKey || '',
      metadata: document.metadata,
      verificationStatus: document.verificationStatus,
      created_at: document.createdAt,
      updated_at: document.updatedAt
    };
  }

  /**
   * Process application requirements and set URLs in single operation
   */
  private async processApplicationRequirementsWithUrls(
    studentId: string,
    uploadResults: any[]
  ): Promise<void> {
    try {
      // Check if user has any applications
      // Temporarily commented out to resolve dependency injection issue
      // const userApplications = await this.applicationService.findByStudentId(
      //   studentId
      // );

      // if (!userApplications || userApplications.length === 0) {
      //   this.logger.log(`No applications found for student ${studentId}`);
      //   return;
      // }
      
      this.logger.log(`Skipping application requirements processing for student ${studentId} due to dependency injection issue`);
      return;

      // Temporarily commented out to resolve dependency injection issue
      // this.logger.log(
      //   `Found ${userApplications.length} application(s) for student ${studentId} applicationList=>+++++++++++++++++++++++++++ ${userApplications}`
      // );

      // // Create a mapping of field names to upload results
      // const fieldToUploadMap = new Map<string, EnhancedUploadResult>();
      // for (const result of uploadResults) {
      //   const field = this.extractFieldFromObjectKey(result.objectKey);
      //   fieldToUploadMap.set(field, result);
      // }

      // this.logger.log(
      //   `Field to upload map created for student ${studentId} fieldToUploadMap=>+++++++++++++++++++++++++++ ${fieldToUploadMap}`
      // );

      // // Process each application
      // for (const application of userApplications) {
      //   await this.processSingleApplicationRequirements(
      //     application.id,
      //     studentId,
      //     fieldToUploadMap
      //   );
      // }
    } catch (error) {
      this.logger.error(
        `Error processing application requirements for student ${studentId}:`,
        error
      );
      // Don't throw error here to avoid blocking the main document upload process
    }
  }

  /**
   * Process requirements for a single application
   * Temporarily commented out to resolve dependency injection issue
   */
  // private async processSingleApplicationRequirements(
  //   applicationId: number | bigint,
  //   studentId: string,
  //   fieldToUploadMap: Map<string, EnhancedUploadResult>
  // ): Promise<void> {
  //   try {
  //     this.logger.log(
  //       `Processing requirements for application ${applicationId}`
  //     );

  //     // Get existing application requirements
  //     // Temporarily commented out to resolve dependency injection issue
  //     // const existingRequirements =
  //     //   await this.applicationService.getApplicationRequirements(applicationId);

  //     this.logger.log(
  //       `Found ${existingRequirements.data.applicationRequirements} existing requirements for application ${applicationId}`
  //     );

  //     // Create document requirement items based on uploaded files
  //     const documentRequirements =
  //       this.createDocumentRequirementsFromFiles(fieldToUploadMap);

  //     this.logger.log(
  //       `Created ${documentRequirements.length} document requirements from uploaded files`
  //     );

  //     if (documentRequirements.length > 0) {
  //       // Process each document requirement
  //       for (const newReq of documentRequirements) {
  //         // Check if this requirement already exists (more flexible matching)
  //         const existingReq = existingRequirements?.data?.applicationRequirements?.find(
  //           (existingReq) => {
  //             // Try multiple matching strategies
  //             const titleMatch = existingReq.documentTitle === newReq.documentTitle;
  //             const nameMatch = existingReq.documentName === newReq.documentName;

  //             // Also try partial matching for common document types
  //             const partialNameMatch = this.isPartialDocumentMatch(
  //               existingReq.documentName,
  //               newReq.documentName
  //             );

  //             return (titleMatch && nameMatch) || (titleMatch && partialNameMatch);
  //           }
  //         );

  //         if (existingReq) {
  //           // Document requirement exists - update it with URL and status
  //           this.logger.log(
  //             `Updating existing requirement ID ${existingReq.id}: ${existingReq.documentTitle} - ${existingReq.documentName} with URL: ${newReq.url}`
  //           );

  //           await this.updateRequirementWithUrl(
  //             existingReq.id,
  //             newReq.url,
  //             'uploaded'
  //           );
  //         } else {
  //           // Document requirement doesn't exist - create new one with URL
  //           this.logger.log(
  //             `Adding new requirement: ${newReq.documentTitle} - ${newReq.documentName} with URL: ${newReq.url}`
  //           );

  //           const createResult = await this.applicationService.createApplicationRequirements(
  //             applicationId,
  //             [newReq],
  //             studentId
  //           );

  //           this.logger.log(`Create result status: ${createResult.status}`);
  //         }
  //       }

  //       this.logger.log(
  //         `Successfully processed ${documentRequirements.length} requirements for application ${applicationId}`
  //       );
  //     } else {
  //       this.logger.log(
  //         `No document requirements created from uploaded files for application ${applicationId}`
  //       );
  //     }
  //   } catch (error) {
  //     this.logger.error(
  //       `Error processing requirements for application ${applicationId}:`,
  //       error.message
  //     );
  //     this.logger.error('Error stack:', error.stack);
  //   }
  // }

  /**
   * Create document requirements from uploaded files with URLs
   */
  private createDocumentRequirementsFromFiles(
    fieldToUploadMap: Map<string, EnhancedUploadResult>
  ): Array<{
    documentTitle: string;
    documentName: string;
    documentDescription?: string;
    isRequired?: boolean;
    allowedFormats?: string[];
    url: string; // Now we have the URL directly
  }> {
    const requirements: Array<{
      documentTitle: string;
      documentName: string;
      documentDescription?: string;
      isRequired?: boolean;
      allowedFormats?: string[];
      url: string;
    }> = [];

    // Map field names to document titles and names
    // Format: documentTitle = root category (lowercase), documentName = sub-category (lowercase)
    const fieldMappings: {
      [key: string]: { title: string; name: string; description?: string };
    } = {
      photo: {
        title: 'profile',
        name: 'photo',
        description: 'Student profile photograph'
      },
      signature: {
        title: 'profile',
        name: 'signature',
        description: 'Student digital signature'
      },
      passport: {
        title: 'identity',
        name: 'passport',
        description: 'Valid passport copy'
      },
      '10th grade': {
        title: 'academic',
        name: '10th grade',
        description: 'Secondary School Certificate'
      },
      "12th grade": {
        title: 'academic',
        name: '12th grade',
        description: 'Higher Secondary Certificate'
      },
      "Bachelor`s Program": {
        title: 'academic',
        name: 'Bachelor`s Program',
        description: 'Bachelor degree certificate'
      },
      "Master`s Program": {
        title: 'academic',
        name: "Master`s Program",
        description: 'Master degree certificate'
      },
      "PhD Program": {
        title: 'academic',
        name: "PhD Program",
        description: 'PhD degree certificate'
      },
      diploma: {
        title: 'academic',
        name: 'diploma',
        description: 'Diploma certificate'
      },
      ielts: {
        title: 'proficiency',
        name: 'ielts',
        description: 'IELTS test score report'
      },
      toefl: {
        title: 'proficiency',
        name: 'toefl',
        description: 'TOEFL test score report'
      },
      duolingo: {
        title: 'proficiency',
        name: 'duolingo',
        description: 'Duolingo English test score'
      },
      pte: {
        title: 'proficiency',
        name: 'pte',
        description: 'Pearson Test of English score'
      },
      gre: {
        title: 'proficiency',
        name: 'gre',
        description: 'GRE test score report'
      },
      gmat: {
        title: 'proficiency',
        name: 'gmat',
        description: 'GMAT test score report'
      },
      sat: {
        title: 'proficiency',
        name: 'sat',
        description: 'SAT test score report'
      },
      sponsorPhoto: {
        title: 'sponsor',
        name: 'photo',
        description: 'Sponsor identification photo'
      },
      sponsorBankStatement: {
        title: 'sponsor',
        name: 'bankstatement',
        description: 'Sponsor bank statement'
      },
      sponsor_documents: {
        title: 'sponsor',
        name: 'documents',
        description: 'Additional sponsor documents'
      },
      bankStatement: {
        title: 'financial',
        name: 'bankstatement',
        description: 'Personal bank statement'
      },
      affidavit: {
        title: 'financial',
        name: 'affidavit',
        description: 'Financial affidavit of support'
      }
    };

    // Process each uploaded file field
    for (const [field, uploadResult] of fieldToUploadMap.entries()) {
      const mapping = fieldMappings[field];
      if (mapping) {
        requirements.push({
          documentTitle: mapping.title,
          documentName: mapping.name,
          documentDescription: mapping.description,
          isRequired: true,
          allowedFormats: ['pdf', 'jpg', 'png', 'jpeg'],
          url: uploadResult.url // Include the actual URL
        });
      } else {
        // For unknown fields, create a generic requirement following the same format
        const fieldName = field.toLowerCase();
        requirements.push({
          documentTitle: 'other',
          documentName: fieldName,
          documentDescription: `Uploaded document: ${field}`,
          isRequired: true,
          allowedFormats: ['pdf', 'jpg', 'png', 'jpeg'],
          url: uploadResult.url // Include the actual URL
        });
      }
    }

    // Remove duplicates based on documentTitle and documentName
    const uniqueRequirements = requirements.filter(
      (req, index, self) =>
        index ===
        self.findIndex(
          (r) =>
            r.documentTitle === req.documentTitle &&
            r.documentName === req.documentName
        )
    );

    return uniqueRequirements;
  }

  /**
   * Update requirement with URL and status in single operation
   */
  private async updateRequirementWithUrl(
    requirementId: number | bigint,
    url: string,
    status: string
  ): Promise<void> {
    try {
      // Import the model here to avoid circular dependencies
      const { ApplicationDocumentRequirement } = await import(
        '../../application/application-document-requirement.model'
      );

      await ApplicationDocumentRequirement.update(
        {
          url,
          uploadedAt: new Date(),
          documentStatus: status,
          // Reset rejection reason when document is uploaded
          rejectionReason: status === 'uploaded' ? null : undefined
        },
        { where: { id: requirementId } }
      );

      this.logger.log(`Updated requirement ${requirementId} with URL: ${url}`);
    } catch (error) {
      this.logger.error(`Error updating requirement URL: ${error.message}`);
    }
  }

  /**
   * Check if two document names are a partial match (for flexible matching)
   * Now works with the new lowercase format: documentTitle.documentName
   */
  private isPartialDocumentMatch(existingName: string, newName: string): boolean {
    const existing = existingName.toLowerCase().trim();
    const newDoc = newName.toLowerCase().trim();

    // Direct match for new lowercase format
    if (existing === newDoc) {
      return true;
    }

    // Common document type mappings for flexible matching with old and new formats
    const documentMappings = [
      ['10th grade', 'secondary school certificate', 'secondary certificate'],
      ['12th grade', 'higher secondary certificate', 'higher secondary'],
      ['Bachelor`s Program', 'bachelor degree', 'bachelor\'s degree', 'undergraduate degree'],
      ["Master`s Program", 'Master`s Program degree', 'Master`s Program degree', 'masters degree', 'masters'],
      ['ielts', 'ielts score report', 'ielts certificate'],
      ['toefl', 'toefl score report', 'toefl certificate'],
      ['passport', 'passport copy', 'passport document'],
      ['photo', 'profile photo', 'student photo'],
      ['signature', 'digital signature', 'student signature'],
      ['duolingo', 'duolingo score report', 'duolingo certificate'],
      ['pte', 'pte score report', 'pte certificate'],
      ['gre', 'gre score report', 'gre certificate'],
      ['gmat', 'gmat score report', 'gmat certificate'],
      ['sat', 'sat score report', 'sat certificate'],
      ['bankstatement', 'bank statement', 'financial statement'],
      ['affidavit', 'affidavit of support', 'financial affidavit']
    ];

    // Check if both names belong to the same document group
    for (const group of documentMappings) {
      const existingInGroup = group.some(term => existing.includes(term));
      const newInGroup = group.some(term => newDoc.includes(term));

      if (existingInGroup && newInGroup) {
        return true;
      }
    }

    // Check for direct substring match
    return existing.includes(newDoc) || newDoc.includes(existing);
  }
}
