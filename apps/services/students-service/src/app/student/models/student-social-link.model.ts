import { BaseModel } from '@apply-goal-backend/database';
import {
  Column,
  DataType,
  BelongsTo,
  ForeignKey,
  Table,
  Index,
  Default
} from 'sequelize-typescript';
import { Student } from './student.model';

@Table({
  tableName: 'student_social_links',
  indexes: [
    { fields: ['studentId'] },
    { fields: ['platform'] },
    { unique: true, fields: ['studentId', 'platform'] }
  ]
})
export class StudentSocialLink extends BaseModel {
  @ForeignKey(() => Student)
  @Column({
    type: DataType.STRING,
    allowNull: false,
    references: {
      model: 'students',
      key: 'studentId'
    }
  })
  studentId!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false
  })
  platform!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false
  })
  url!: string;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  username?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  displayName?: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: true
  })
  isPublic!: boolean;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: true
  })
  isActive!: boolean;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false
  })
  isPrimary!: boolean; // Primary profile for this platform

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false
  })
  isProfessional!: boolean; // Professional vs personal account

  @Column({
    type: DataType.TEXT,
    allowNull: true
  })
  description?: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true
  })
  followerCount?: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true
  })
  followingCount?: number;

  @Column({
    type: DataType.DATEONLY,
    allowNull: true
  })
  lastVerified?: Date;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false
  })
  isVerified!: boolean; // Platform verification status

  @Column({
    type: DataType.TEXT,
    allowNull: true
  })
  notes?: string;

  @Column({
    type: DataType.JSON,
    allowNull: true
  })
  metadata?: Record<string, any>;

  @BelongsTo(() => Student, { foreignKey: 'studentId', targetKey: 'studentId' })
  student!: Student;

  // Virtual properties
  get isProfessionalPlatform(): boolean {
    const professionalPlatforms = [
      'linkedin',
      'github',
      'portfolio',
      'researchgate',
      'orcid',
      'googlescholar',
      'academia',
      'behance',
      'dribbble',
      'medium'
    ];
    return professionalPlatforms.includes(this.platform);
  }

  get isSocialPlatform(): boolean {
    const socialPlatforms = [
      'facebook',
      'twitter',
      'instagram',
      'youtube',
      'tiktok'
    ];
    return socialPlatforms.includes(this.platform);
  }

  get isAcademicPlatform(): boolean {
    const academicPlatforms = [
      'researchgate',
      'orcid',
      'googlescholar',
      'academia'
    ];
    return academicPlatforms.includes(this.platform);
  }

  get isCreativePlatform(): boolean {
    const creativePlatforms = [
      'behance',
      'dribbble',
      'youtube',
      'instagram',
      'tiktok',
      'portfolio'
    ];
    return creativePlatforms.includes(this.platform);
  }

  get platformCategory(): string {
    if (this.isAcademicPlatform) return 'academic';
    if (this.isCreativePlatform) return 'creative';
    if (this.isProfessionalPlatform) return 'professional';
    if (this.isSocialPlatform) return 'social';
    return 'other';
  }

  get isValidUrl(): boolean {
    try {
      new URL(this.url);
      return true;
    } catch {
      return false;
    }
  }

  get domain(): string {
    try {
      return new URL(this.url).hostname;
    } catch {
      return '';
    }
  }

  get hasHighFollowing(): boolean {
    if (!this.followerCount) return false;

    // Different thresholds for different platforms
    const thresholds: Record<string, number> = {
      linkedin: 1000,
      twitter: 5000,
      instagram: 10000,
      facebook: 5000,
      youtube: 1000,
      tiktok: 10000,
      github: 100
    };

    const threshold = thresholds[this.platform] || 1000;
    return this.followerCount >= threshold;
  }

  get needsVerification(): boolean {
    if (!this.lastVerified) return true;

    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    return new Date(this.lastVerified) < sixMonthsAgo;
  }

  get platformIcon(): string {
    const icons: Record<string, string> = {
      linkedin: '💼',
      facebook: '📘',
      twitter: '🐦',
      instagram: '📷',
      github: '🐙',
      portfolio: '🌐',
      youtube: '📺',
      tiktok: '🎵',
      behance: '🎨',
      dribbble: '🏀',
      medium: '📝',
      researchgate: '🔬',
      orcid: '🆔',
      googlescholar: '🎓',
      academia: '📚'
    };

    return icons[this.platform] || '🔗';
  }
}
