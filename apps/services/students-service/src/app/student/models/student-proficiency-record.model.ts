import { BaseModel } from '@apply-goal-backend/database';
import {
  Column,
  DataType,
  BelongsTo,
  ForeignKey,
  Table,
  Index,
  Default
} from 'sequelize-typescript';
import { Student } from './student.model';

@Table({
  tableName: 'student_proficiency_records',
  indexes: [
    { fields: ['studentId'] },
    { fields: ['nameOfExam'] },
    { fields: ['examDate'] },
    { fields: ['expiryDate'] }
  ]
})
export class StudentProficiencyRecord extends BaseModel {
  @ForeignKey(() => Student)
  @Column({
    type: DataType.STRING,
    allowNull: false,
    references: {
      model: 'students',
      key: 'studentId'
    }
  })
  studentId!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false
  })
  nameOfExam!: string;

  // Score breakdown
  @Column({
    type: DataType.JSON,
    allowNull: false
  })
  score!: {
    overall: number;
    R?: number; // Reading
    L?: number; // Listening
    W?: number; // Writing
    S?: number; // Speaking
  };

  @Column({
    type: DataType.DATEONLY,
    allowNull: false
  })
  examDate!: Date;

  @Column({
    type: DataType.DATEONLY,
    allowNull: true
  })
  expiryDate?: Date;

  @Column({
    type: DataType.TEXT,
    allowNull: true
  })
  note?: string;

  // Additional fields for comprehensive proficiency tracking
  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  testCenter?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  candidateNumber?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  certificateNumber?: string;

  @Column({
    type: DataType.ENUM('academic', 'general', 'professional'),
    allowNull: true
  })
  testType?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  band?: string; // For IELTS bands like "Band 7"

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  level?: string; // For other tests like "C1", "B2", etc.

  // Document attachments (JSON array of file paths/URLs)
  @Column({
    type: DataType.JSON,
    allowNull: true,
    defaultValue: []
  })
  documents?: string[];

  @Column({
    type: DataType.JSON,
    allowNull: true
  })
  metadata?: Record<string, any>;

  @BelongsTo(() => Student, { foreignKey: 'studentId', targetKey: 'studentId' })
  student!: Student;

  // Virtual properties
  get isExpired(): boolean {
    if (!this.expiryDate) return false;
    return new Date() > new Date(this.expiryDate);
  }

  get isValid(): boolean {
    return !this.isExpired;
  }

  get daysUntilExpiry(): number {
    if (!this.expiryDate) return Infinity;
    const today = new Date();
    const expiry = new Date(this.expiryDate);
    const diffTime = expiry.getTime() - today.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  get isIELTS(): boolean {
    return this.nameOfExam.toLowerCase().includes('ielts');
  }

  get isTOEFL(): boolean {
    return this.nameOfExam.toLowerCase().includes('toefl');
  }

  get isPTE(): boolean {
    return this.nameOfExam.toLowerCase().includes('pte');
  }

  get isDuolingo(): boolean {
    return this.nameOfExam.toLowerCase().includes('duolingo');
  }

  get testCategory(): string {
    if (this.isIELTS) return 'IELTS';
    if (this.isTOEFL) return 'TOEFL';
    if (this.isPTE) return 'PTE';
    if (this.isDuolingo) return 'Duolingo';
    return 'Other';
  }

  get hasAllSkillScores(): boolean {
    return !!(this.score.R && this.score.L && this.score.W && this.score.S);
  }

  get averageSkillScore(): number {
    if (!this.hasAllSkillScores) return this.score.overall;
    return (this.score.R! + this.score.L! + this.score.W! + this.score.S!) / 4;
  }

  get hasDocuments(): boolean {
    return this.documents && this.documents.length > 0;
  }

  get isHighScore(): boolean {
    // Define high score thresholds for different tests
    if (this.isIELTS) return this.score.overall >= 7.0;
    if (this.isTOEFL) return this.score.overall >= 100;
    if (this.isPTE) return this.score.overall >= 65;
    if (this.isDuolingo) return this.score.overall >= 120;
    return false;
  }
}
