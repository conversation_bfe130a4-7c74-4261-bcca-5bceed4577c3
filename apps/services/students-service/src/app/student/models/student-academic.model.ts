import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { Student } from './student.model';
import { BaseModel } from '@apply-goal-backend/database';

@Table({
  tableName: 'student_academic_backgrounds',
  timestamps: true,
})
export class StudentAcademicBackground extends BaseModel {
  @ForeignKey(() => Student)
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    references: {
      model: 'students',
      key: 'id',
    },
  })
  studentId: bigint;

  @BelongsTo(() => Student, { foreignKey: 'studentId', targetKey: 'id' })
  student: Student;

  // Academic Records (JSON array)
  @Column({
    type: DataType.JSON,
    allowNull: true,
    defaultValue: [],
  })
  academicRecords: Array<{
    foreignDegree: boolean;
    nameOfExam: string;
    institute: string;
    subject: string;
    board: string;
    grade: string;
    passingYear: string;
  }>;

  // Proficiency Records (JSON array)
  @Column({
    type: DataType.JSON,
    allowNull: true,
    defaultValue: [],
  })
  proficiencyRecords: Array<{
    nameOfExam: string;
    score: {
      overall: number;
      reading: number;
      listening: number;
      writing: number;
      speaking: number;
    };
    examDate: string;
    expiryDate: string;
    note: string;
  }>;

  // Publication Records (JSON array)
  @Column({
    type: DataType.JSON,
    allowNull: true,
    defaultValue: [],
  })
  publicationRecords: Array<{
    subject: string;
    journal: string;
    publicationDate: string;
    link: string;
  }>;

  // Other Activities (JSON array)
  @Column({
    type: DataType.JSON,
    allowNull: true,
    defaultValue: [],
  })
  otherActivities: Array<{
    subject: string;
    certificationLink: string;
    startDate: string;
    endDate: string;
  }>;
}