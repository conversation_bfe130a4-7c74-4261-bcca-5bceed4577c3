import { BaseModel } from '@apply-goal-backend/database';
import {
  Column,
  DataType,
  BelongsTo,
  ForeignKey,
  Table,
  Index,
  Default
} from 'sequelize-typescript';
import { Student } from './student.model';

@Table({
  tableName: 'student_personal_info',
  indexes: [
    { unique: true, fields: ['studentId'] },
    { unique: true, fields: ['nid'] },
    { unique: true, fields: ['passport'] }
  ]
})
export class StudentPersonalInfo extends BaseModel {
  @ForeignKey(() => Student)
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    references: {
      model: 'students',
      key: 'id'
    }
  })
  studentId!: bigint;

  @Column({
    type: DataType.STRING,
    allowNull: false
  })
  firstName!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false
  })
  lastName!: string;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  nameInNative?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  middleName?: string;

  @Column({
    type: DataType.STRING,
    allowNull: false
  })
  email!: string;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  phone?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  guardianPhone?: string;

  @Column({
    type: DataType.DATEONLY,
    allowNull: true
  })
  dateOfBirth?: Date;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  countryOfBirth?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  cityOfBirth?: string;

  @Column({
    type: DataType.ENUM('male', 'female', 'other'),
    allowNull: true
  })
  gender?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  fatherName?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  motherName?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  nid?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  passport?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  ethnicSurvey?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  englishProficiency?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  sevisId?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  previousStudentId?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  referenceSource?: string;

  @Column({
    type: DataType.JSON,
    allowNull: true
  })
  identity?: Record<string, any>;

  @Column({
    type: DataType.JSON,
    allowNull: true
  })
  dependentsInfo?: Record<string, any>;

  // Present Address
  @Column({
    type: DataType.JSON,
    allowNull: true
  })
  presentAddress?: {
    address: string;
    country: string;
    state: string;
    city: string;
    postalCode: string;
  };

  // Permanent Address
  @Column({
    type: DataType.JSON,
    allowNull: true
  })
  permanentAddress?: {
    address: string;
    country: string;
    state: string;
    city: string;
    postalCode: string;
  };

  // Marital Status
  @Column({
    type: DataType.JSON,
    allowNull: true
  })
  maritalStatus?: {
    status: 'single' | 'married' | 'divorced' | 'widowed';
    spouseName?: string;
    spousePhone?: string;
    spousePassport?: string;
  };

  // Sponsor Information
  @Column({
    type: DataType.JSON,
    allowNull: true
  })
  sponsor?: {
    name: string;
    relation: string;
    phone: string;
    nid: string;
  };

  // Emergency Contact
  @Column({
    type: DataType.JSON,
    allowNull: true
  })
  emergencyContact?: {
    firstName: string;
    middleName?: string;
    lastName: string;
    phoneHome: string;
    phoneMobile: string;
    relation: string;
  };

  // Preferred Subjects (JSON array)
  @Column({
    type: DataType.JSON,
    allowNull: true,
    defaultValue: []
  })
  preferredSubject?: string[];

  // Preferred Countries (JSON array)
  @Column({
    type: DataType.JSON,
    allowNull: true,
    defaultValue: []
  })
  preferredCountry?: string[];

  // Social Links (JSON array)
  @Column({
    type: DataType.JSON,
    allowNull: true,
    defaultValue: []
  })
  socialLinks?: Array<{
    platform: string;
    url: string;
  }>;

  @Column({
    type: DataType.TEXT,
    allowNull: true
  })
  reference?: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true
  })
  note?: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: 'Flag to track if personal info has been edited after initial creation'
  })
  isPersonalInfoEdited!: boolean;

  @BelongsTo(() => Student, { foreignKey: 'studentId', targetKey: 'id' })
  student!: Student;

  // Virtual properties
  get fullName(): string {
    return `${this.firstName} ${this.lastName}`;
  }

  get age(): number {
    if (!this.dateOfBirth) return 0;
    const today = new Date();
    const birthDate = new Date(this.dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < birthDate.getDate())
    ) {
      age--;
    }

    return age;
  }

  get isMarried(): boolean {
    return this.maritalStatus?.status === 'married';
  }

  get hasPassport(): boolean {
    return !!this.passport;
  }

  get hasNid(): boolean {
    return !!this.nid;
  }
}
