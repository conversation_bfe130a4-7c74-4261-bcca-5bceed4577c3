import { Injectable, Logger } from '@nestjs/common';
import { UploadService } from '@apply-goal-backend/common';
import {
  HtmlTemplateService,
  PdfTemplateService
} from '@apply-goal-backend/utils';

export type TemplateType = 'pdf' | 'html';

export interface AdmissionPortfolioData {
  studentId: string;
  studentName: string;
  applicationType: string;
  email: string;
  phone: string;
  dateOfBirth: string;
  passport: string;
  nid: string;
  presentAddress: string;
  permanentAddress: string;
  preferredSubject: string[];
  preferredCountry: string[];
  issueDate: string;
  // Additional fields specific to Admission Portfolio documents
  fatherName: string;
  motherName: string;
  gender: string;
  maritalStatus: string;
  sponsor: string;
  emergencyContact: string;
  // Admission Portfolio specific fields
  portfolioNumber: string;
  portfolioDate: string;
  universityName: string;
  courseName: string;
  intakeName: string;
  campusName: string;
  tuitionFee: string;
  courseStartDate: string;
  courseEndDate: string;
  requirementsDocuments: any;
}

@Injectable()
export class AdmissionPortfolioService {
  private readonly logger = new Logger(AdmissionPortfolioService.name);

  constructor(
    private readonly uploadService: UploadService,
    private readonly htmlTemplateService: HtmlTemplateService,
    private readonly pdfTemplateService: PdfTemplateService
  ) {}

  async generateAdmissionPortfolioWithMinio(
    studentData: any,
    templateType: TemplateType = 'html'
  ): Promise<string> {
    try {
      this.logger.log(
        `Generating Admission Portfolio for student ${studentData.studentId} using ${templateType} templates`
      );

      // Prepare template data
      const templateData = this.prepareTemplateData(studentData);

      this.logger.debug(
        `${templateType} template data`,
        JSON.stringify(templateData, null, 2)
      );

      // Generate Admission Portfolio document based on template type
      let result;
      if (templateType === 'html') {
        result = await this.generateAdmissionPortfolioFromHtmlTemplate(
          templateData
        );
      } else {
        result = await this.generateAdmissionPortfolioFromPdfTemplate(
          templateData
        );
      }

      if (!result.success) {
        throw new Error(
          result.error || 'Failed to generate Admission Portfolio'
        );
      }

      // Upload to MinIO
      const filename = `Admission-Portfolio-${studentData.studentId}.pdf`;

      // Create a MulterFile-like object for the upload service
      const file = {
        fieldname: 'admission-portfolio',
        originalname: filename,
        encoding: '7bit',
        mimetype: 'application/pdf',
        size: result.documentContent!.length,
        buffer: result.documentContent!
      };

      const uploadResult = await this.uploadService.uploadFile(file, 'file');
      this.logger.log(
        `Admission Portfolio uploaded to MinIO: ${uploadResult.url}`
      );

      return uploadResult.url;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(
        `Error generating Admission Portfolio: ${errorMessage}`
      );
      throw error;
    }
  }

  private async generateAdmissionPortfolioFromHtmlTemplate(
    templateData: AdmissionPortfolioData
  ): Promise<any> {
    this.logger.log(
      `Generating Admission Portfolio from HTML template for student ${templateData.studentId}`
    );

    try {
      // Use the admission package directory structure for admission portfolio generation
      const result = await this.htmlTemplateService.generateAdmissionPortfolio(
        templateData as any
      );

      if (result.success) {
        this.logger.log(
          `HTML template generation successful, size: ${result.documentContent?.length} bytes`
        );
      } else {
        this.logger.error(`HTML template generation failed: ${result.error}`);
      }

      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(`Error in HTML template generation: ${errorMessage}`);
      throw error;
    }
  }

  private async generateAdmissionPortfolioFromPdfTemplate(
    templateData: AdmissionPortfolioData
  ): Promise<any> {
    this.logger.log(
      `Generating Admission Portfolio from PDF template for student ${templateData.studentId}`
    );

    try {
      // For now, use the admission package method as a fallback since admission portfolio templates are similar
      // TODO: Implement proper admission portfolio template generation
      const result = await this.pdfTemplateService.generateAdmissionPortfolio(
        templateData as any
      );

      if (result.success) {
        this.logger.log(
          `PDF template generation successful, size: ${result.documentContent?.length} bytes`
        );
      } else {
        this.logger.error(`PDF template generation failed: ${result.error}`);
      }

      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(`Error in PDF template generation: ${errorMessage}`);
      throw error;
    }
  }

  private prepareTemplateData(studentData: any): any {
    const now = new Date();

    // Extract student information
    const student = studentData.student;
    const personalInfo = studentData.studentPersonalInfo || {};
    const course = studentData.course || {};
    const program = studentData.program || {};
    const intake = studentData.intake || {};
    const campus = studentData.campus || {};
    const university = studentData.university || {};
    const fees = studentData.fees[0] || {};

    const processRequirements = (): {
      identityDocuments: any[];
      academicDocuments: any[];
      proficiencyDocuments: any[];
      otherDocuments: any[];
    } => {
      const requirements = studentData.applicationRequirement || {};

      // Helper function to filter documents with URLs (including all documents for AP generation)
      const filterDocumentsWithUrls = (documents: any[]): any[] => {
        // ✅ For AP generation, include all documents (even those with storage-disabled URLs)
        // The PDF generation will handle URL validation separately
        return documents.filter((doc) => doc.url && doc.url.trim() !== '');
      };

      // Helper function to format document data
      const formatDocument = (doc: any) => ({
        id: doc.id,
        documentName: doc.documentName,
        documentTitle: doc.documentTitle,
        url: doc.url,
        documentDescription: doc.documentDescription,
        documentStatus: doc.documentStatus,
        uploadedAt: doc.uploadedAt,
        verifiedAt: doc.verifiedAt,
        isRequired: doc.isRequired,
        allowedFormats: doc.allowedFormats || []
      });

      return {
        identityDocuments: filterDocumentsWithUrls(
          requirements.identity || []
        ).map(formatDocument),
        academicDocuments: filterDocumentsWithUrls(
          requirements.academic || []
        ).map(formatDocument),
        proficiencyDocuments: filterDocumentsWithUrls(
          requirements.proficiency || []
        ).map(formatDocument),
        otherDocuments: filterDocumentsWithUrls(requirements.others || []).map(
          formatDocument
        )
      };
    };

    // Process requirements
    const requirementsData = processRequirements();

    // Format dates properly
    const formatDate = (date: any): string => {
      if (!date) return 'N/A';
      const d = new Date(date);
      if (isNaN(d.getTime())) return 'N/A';
      return d.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    };

    // Get student name
    const getStudentName = (): string => {
      if (personalInfo.firstName && personalInfo.lastName) {
        return `${personalInfo.firstName} ${personalInfo.lastName}`;
      }
      if (personalInfo.firstName) return personalInfo.firstName;
      if (personalInfo.lastName) return personalInfo.lastName;
      return 'N/A';
    };

    // Get address information
    const getPresentAddress = (): string => {
      if (personalInfo.presentAddress) {
        const addr = personalInfo.presentAddress;
        return `${addr.address || ''}, ${addr.city || ''}, ${
          addr.state || ''
        }, ${addr.country || ''}, ${addr.postalCode || ''}`.trim();
      }
      return 'N/A';
    };

    const getPermanentAddress = (): string => {
      if (personalInfo.permanentAddress) {
        const addr = personalInfo.permanentAddress;
        return `${addr.address || ''}, ${addr.city || ''}, ${
          addr.state || ''
        }, ${addr.country || ''}, ${addr.postalCode || ''}`.trim();
      }
      return 'N/A';
    };

    // Get sponsor information
    const getSponsor = (): string => {
      if (personalInfo.sponsor) {
        const sponsor = personalInfo.sponsor;
        return `${sponsor.name || ''} (${sponsor.relation || ''})`;
      }
      return 'N/A';
    };

    // Get emergency contact
    const getEmergencyContact = (): string => {
      if (personalInfo.emergencyContact) {
        const contact = personalInfo.emergencyContact;
        return `${contact.firstName || ''} ${contact.lastName || ''} - ${
          contact.phoneHome || contact.phoneMobile || ''
        }`;
      }
      return 'N/A';
    };

    // Get course name
    const getCourseName = (): string => {
      if (course.courseTitle) return course.courseTitle;
      if (program.programLevelName) return program.programLevelName;
      if (course.id) return `Course ${course.id}`;
      return studentData.courseId ? `Course ${studentData.courseId}` : 'N/A';
    };

    // Get tuition fee
    const getTuitionFee = (): string => {
      if (fees.tuitionFee) return `${parseFloat(fees.tuitionFee).toFixed(2)}`;
      return 'N/A';
    };
    const estimateTotalCharge = (
      amount: string | number,
      extra = 100
    ): number => {
      const numeric =
        typeof amount === 'string'
          ? parseFloat(amount.replace(/[^0-9.]/g, ''))
          : amount;
      if (isNaN(numeric)) return 0;
      return numeric + extra;
    };
    // Generate Portfolio number
    const generatePortfolioNumber = (): string => {
      const year = new Date().getFullYear();
      const randomNum = Math.floor(Math.random() * 10000)
        .toString()
        .padStart(4, '0');
      return `PORT${year}${randomNum}`;
    };
    const getCityFromAddress = (address: string): string => {
      if (!address) return 'N/A';
      const parts = address.split(',').map((p) => p.trim());
      return parts.length >= 2 ? parts[1] : 'N/A';
    };

    // Extract state from address
    const getStateFromAddress = (address: string): string => {
      if (!address) return 'N/A';
      const parts = address.split(',').map((p) => p.trim());
      return parts.length >= 3 ? parts[2] : 'N/A';
    };

    // Extract country from address
    const getCountryFromAddress = (address: string): string => {
      if (!address) return 'N/A';
      const parts = address.split(',').map((p) => p.trim());
      return parts.length >= 4 ? parts[3] : 'N/A';
    };
    const shortName = (fullName: string): string => {
      if (!fullName) return '';

      return fullName
        .split(' ') // split into words
        .map((word) => word[0]) // take first letter of each word
        .join('') // join them together
        .toUpperCase(); // make uppercase
    };

    // Handle null application type - try to get it from application data or use default
    let applicationType = student.applicationType;
    if (!applicationType || applicationType === 'null') {
      // Use default based on student data or fallback
      applicationType = 'F-1 initial';
    }

    // ✅ Filter visa documents based on application type
    const getVisaDocumentsForApplicationType = (appType: string): {
      i20Url: string | null;
      i94Url: string | null;
      i797cUrl: string | null;
    } => {
      // Define which visa documents are needed for each application type
      const visaRequirements: { [key: string]: string[] } = {
        'F-1 initial': [], // F-1 initial students don't need I-20, I-94, I-797C
        'Transfer': ['i20Url', 'i94Url'], // Transfer students need I-20 and I-94
        'F-1 Transfer': ['i20Url', 'i94Url'], // F-1 Transfer students need I-20 and I-94
        'Reinstatement': ['i20Url', 'i94Url', 'i797cUrl'], // Reinstatement students need all three
        'F-1 Reinstatement': ['i20Url', 'i94Url', 'i797cUrl'], // F-1 Reinstatement students need all three
        'New Program': ['i20Url', 'i94Url'], // New Program students need I-20 and I-94
        'Change of Status': ['i20Url', 'i94Url', 'i797cUrl'], // Change of Status students need all three
        'Non F-1': [], // Non-F-1 students don't need these documents
        'Non F1': [] // Handle alternative naming
      };

      const requiredDocs = visaRequirements[appType] || [];

      // ✅ IMPORTANT: Use studentI20Url instead of i20Url for admission portfolio generation
      return {
        i20Url: requiredDocs.includes('i20Url') ? (studentData.studentI20Url || null) : null,
        i94Url: requiredDocs.includes('i94Url') ? (studentData.i94Url || null) : null,
        i797cUrl: requiredDocs.includes('i797cUrl') ? (studentData.i797cUrl || null) : null
      };
    };

    // Get filtered visa documents for this application type
    const visaDocuments = getVisaDocumentsForApplicationType(applicationType);

    this.logger.log(`📋 Filtered visa documents for ${applicationType}:`, {
      i20Url: visaDocuments.i20Url ? 'Present' : 'Not Required',
      i94Url: visaDocuments.i94Url ? 'Present' : 'Not Required',
      i797cUrl: visaDocuments.i797cUrl ? 'Present' : 'Not Required'
    });

    // ✅ IMPORTANT: Debug log to show studentI20Url is now being used
    this.logger.log(`🔍 DEBUG - Comprehensive data URLs:`, {
      i20Url: studentData.i20Url ? 'Present (NOT USED)' : 'Absent',
      studentI20Url: studentData.studentI20Url ? 'Present (NOW USED)' : 'Absent',
      i94Url: studentData.i94Url ? 'Present' : 'Absent',
      i797cUrl: studentData.i797cUrl ? 'Present' : 'Absent'
    });

    const templateData: any = {
      studentId: studentData.studentId || 'N/A',
      studentName: getStudentName(),
      studentFirstName: personalInfo.firstName || 'N/A',
      studentLastName: personalInfo.lastName || 'N/A',
      studentMiddleName: personalInfo.middleName || 'N/A',
      studentShortName: shortName(getStudentName()),
      applicationType: applicationType,
      email: personalInfo.email || 'N/A',
      phone: personalInfo.phone || 'N/A',
      dateOfBirth: formatDate(personalInfo.dateOfBirth),
      passport: personalInfo.passport || 'N/A',
      nid: personalInfo.nid || 'N/A',
      presentAddress: getPresentAddress(),
      permanentAddress: getPermanentAddress(),
      preferredSubject: personalInfo.preferredSubject || [],
      preferredCountry: personalInfo.preferredCountry || [],
      issueDate: now.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }),
      fatherName: personalInfo.fatherName || 'N/A',
      motherName: personalInfo.motherName || 'N/A',
      gender: personalInfo.gender || 'N/A',
      maritalStatus: personalInfo.maritalStatus?.status || 'N/A',
      sponsor: getSponsor(),
      sponsorRelation: personalInfo.sponsor?.relation || 'N/A',
      sponsorPhone: personalInfo.sponsor?.phone || 'N/A',
      sponsorEmail: personalInfo.sponsor?.email || 'N/A',
      // emergencyContact: getEmergencyContact(),
      emergenceyContactFirstName:
        personalInfo.emergencyContact?.firstName || 'N/A',
      emergenceyContactLastName:
        personalInfo.emergencyContact?.lastName || 'N/A',
      emergenceyContactRelation:
        personalInfo.emergencyContact?.relation || 'N/A',
      emergenceyContactHomePhone:
        personalInfo.emergencyContact?.phoneHome || 'N/A',
      emegerceyContactMobilePhone:
        personalInfo.emergencyContact?.phoneMobile || 'N/A',
      // Portfolio-specific
      portfolioNumber: generatePortfolioNumber(),
      apDate: now.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }),
      universityName: university.name || 'N/A',
      universityAddress: university.address || 'N/A',
      universityCountry: university.country || 'N/A',
      universityState: university.state || 'N/A',
      universityCity: university.city || 'N/A',
      universityPostalCode: university.postalCode || 'N/A',
      universityWebsite: university.website || 'N/A',
      universityEmail: university.email || 'N/A',
      universityContact: university.primaryContactNumber || 'N/A',
      campusName: campus.campusName || 'N/A',
      campusAddress: campus.address || 'N/A',
      campusPostalCode: campus.postalCode || 'N/A',
      campusCity: campus.city || 'N/A',
      campusState: campus.state || 'N/A',
      campusEmail: campus.email || 'N/A',
      campusContactNumber: campus.contactNumber || 'N/A',
      courseName: getCourseName(),
      intakeName: intake.name || 'N/A',
      classStartDate: formatDate(intake.classStartDate || intake.startDate),
      enrollmentDeadline: formatDate(
        intake.enrollmentDeadline || intake.endDate
      ),
      applicationProcessDuration: intake.applicationProcessDuration || 'N/A',
      courseStartDate: formatDate(intake.classStartDate || intake.startDate),
      courseEndDate: formatDate(intake.endDate),

      tuitionFee: getTuitionFee(),
      creditFee: fees.creditFee || 'N/A',
      semisterFee: fees.semisterFee || 'N/A',
      yearlyFee: fees.yearlyFee || 'N/A',
      estimateTotalCharge: estimateTotalCharge(fees.tuitionFee) || 'N/A',
      institutionCode: university.institutionCode || 'N/A',
      // ✅ Use filtered visa documents based on application type (NOW using studentI20Url)
      i20Url: visaDocuments.i20Url,
      i94Url: visaDocuments.i94Url,
      i797cUrl: visaDocuments.i797cUrl,
      // ✅ EXPLICIT: studentI20Url is now being used for admission portfolio generation
      studentI20Url: visaDocuments.i20Url, // Now using studentI20Url for admission portfolio
      savisId: studentData.savisId || 'N/A',
      calUrl: studentData.calUrl || null,
      admUrl: studentData.admUrl || null,
      apUrl: studentData.apUrl || null,
      programName: program.programLevelName || 'N/A',
      graduationFee: 50,
      cancelationFee: 50,
      totalChargesForCurrentPeriodAttendance: 3650,
      totalChargesForStudentObligation: 3650,
      // requirements
      requirementsDocuments: requirementsData,
      citizenshipCountry: personalInfo.presentAddress?.country || 'N/A',
      citizenshipCity: personalInfo.presentAddress?.city || 'N/A',
      birthCountry: personalInfo.permanentAddress?.country || 'N/A',
      birthCity: personalInfo.permanentAddress?.city || 'N/A',
      admissionYear: new Date().getFullYear(),
      // ✅ NEW: Add sponsor information for NID comparison
      sponsorInfo: {
        sponsor: personalInfo.sponsor,
        studentPersonalInfo: personalInfo
      }
    };

    this.logger.log("Admission Portfolio template data format",templateData)
    return templateData;
  }
}
