import { Injectable, Logger } from '@nestjs/common';
import { UploadService } from '@apply-goal-backend/common';
import {
  HtmlTemplateService,
  PdfTemplateService
} from '@apply-goal-backend/utils';

export type TemplateType = 'pdf' | 'html';

export interface AdmDocumentData {
  studentId: string;
  studentName: string;
  applicationType: string;
  email: string;
  phone: string;
  dateOfBirth: string;
  passport: string;
  nid: string;
  presentAddress: string;
  permanentAddress: string;
  preferredSubject: string[];
  preferredCountry: string[];
  issueDate: string;
  // Additional fields specific to ADM documents
  fatherName: string;
  motherName: string;
  gender: string;
  maritalStatus: string;
  sponsor: string;
  emergencyContact: string;
  // ADM specific fields
  admNumber: string;
  admDate: string;
  universityName: string;
  courseName: string;
  intakeName: string;
  campusName: string;
  tuitionFee: string;
  courseStartDate: string;
  courseEndDate: string;
  // I-20 document URL for merging as last pages
  i20Url?: string;
}

@Injectable()
export class AdmDocumentService {
  private readonly logger = new Logger(AdmDocumentService.name);

  constructor(
    private readonly uploadService: UploadService,
    private readonly htmlTemplateService: HtmlTemplateService,
    private readonly pdfTemplateService: PdfTemplateService
  ) {}

  async generateAdmDocumentWithMinio(
    studentData: any,
    templateType: TemplateType = 'html'
  ): Promise<string> {
    try {
      this.logger.log(
        `Generating ADM document for student ${studentData.studentId} using ${templateType} templates`
      );

      // Prepare template data
      const templateData = this.prepareTemplateData(studentData);

      this.logger.log(
        `${templateType} template data`,
        JSON.stringify(templateData, null, 2)
      );

      // ✅ REMOVED: I-20 validation for Change of Status
      // I-20 is now optional - if studentI20Url exists, it will be included in ADM generation
      // If it doesn't exist, ADM generation will proceed without it
      if (templateData.applicationType === 'Change of Status') {
        if (templateData.studentI20Url && templateData.studentI20Url.trim()) {
          this.logger.log(`Student I-20 found for Change of Status student ${studentData.studentId} - will be included in ADM`);
        } else {
          this.logger.log(`No Student I-20 found for Change of Status student ${studentData.studentId} - proceeding without I-20`);
        }
      }

      // Generate ADM document based on template type
      let result;
      if (templateType === 'html') {
        result = await this.generateAdmDocumentFromHtmlTemplate(templateData);
      } else {
        result = await this.generateAdmDocumentFromPdfTemplate(templateData);
      }

      if (!result.success) {
        throw new Error(result.error || 'Failed to generate ADM document');
      }

      // Upload to MinIO
      const filename = `ADM-Document-${studentData.studentId}.pdf`;

      // Create a MulterFile-like object for the upload service
      const file = {
        fieldname: 'adm-document',
        originalname: filename,
        encoding: '7bit',
        mimetype: 'application/pdf',
        size: result.documentContent!.length,
        buffer: result.documentContent!
      };

      const uploadResult = await this.uploadService.uploadFile(file, 'file');
      this.logger.log(`ADM document uploaded to MinIO: ${uploadResult.url}`);

      return uploadResult.url;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(`Error generating ADM document: ${errorMessage}`);
      throw error;
    }
  }

  private async generateAdmDocumentFromHtmlTemplate(
    templateData: AdmDocumentData
  ): Promise<any> {
    this.logger.log(
      `Generating ADM document from HTML template for student ${templateData.studentId}`
    );

    try {
      // Use the existing HTML template service but with ADM document specific data
      const result = await this.htmlTemplateService.generateAdmDocument(
        templateData
      );

      if (result.success) {
        this.logger.log(
          `HTML template generation successful, size: ${result.documentContent?.length} bytes`
        );
      } else {
        this.logger.error(`HTML template generation failed: ${result.error}`);
      }

      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(`Error in HTML template generation: ${errorMessage}`);
      throw error;
    }
  }

  private async generateAdmDocumentFromPdfTemplate(
    templateData: AdmDocumentData
  ): Promise<any> {
    this.logger.log(
      `Generating ADM document from PDF template for student ${templateData.studentId}`
    );

    try {
      // Use the existing PDF template service but with ADM document specific data
      const result = await this.pdfTemplateService.generateAdmDocument(
        templateData
      );

      if (result.success) {
        this.logger.log(
          `PDF template generation successful, size: ${result.documentContent?.length} bytes`
        );
      } else {
        this.logger.error(`PDF template generation failed: ${result.error}`);
      }

      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(`Error in PDF template generation: ${errorMessage}`);
      throw error;
    }
  }

  private prepareTemplateData(comprehensiveData: any): any {
    const now = new Date();

    // Extract comprehensive information based on actual data structure
    const student = comprehensiveData.student || {};
    const personalInfo = comprehensiveData.studentPersonalInfo || {};
    const application = comprehensiveData; // The root object contains application data
    const fees = comprehensiveData.fees[0] || {};

    // Extract university-related data from the comprehensive response
    const university = comprehensiveData.university || {};
    const campus = comprehensiveData.campus || {};
    const program = comprehensiveData.program || {};
    const course = comprehensiveData.course || {};
    const intake = comprehensiveData.intake || {};

    // Log the comprehensive data being extracted
    this.logger.debug('Comprehensive ADM data for template:', {
      studentId: student.studentId || application.studentId,
      hasPersonalInfo: Object.keys(personalInfo).length > 0,
      hasUniversityData: Object.keys(university).length > 0,
      hasApplicationData: !!application.id,
      feesCount: fees.length,
      universityName: university.name,
      courseName: course.courseTitle,
      programName: program.programLevelName,
      campusName: campus.campusName,
      intakeName: intake.name
    });

    // Handle application type with comprehensive data
    let applicationType = student.applicationType;
    if (!applicationType || applicationType === 'null') {
      // Try to get from application data
      if (application && application.applicationType) {
        applicationType = application.applicationType;
      } else {
        // Use default based on student data or fallback
        applicationType = 'F-1 initial';
      }
    }

    // Format dates properly
    // const formatDate = (date: any): string => {
    //   if (!date) return 'N/A';
    //   const d = new Date(date);
    //   if (isNaN(d.getTime())) return 'N/A';
    //   return d.toLocaleDateString('en-US', {
    //     year: 'numeric',
    //     month: 'long',
    //     day: 'numeric'
    //   });
    // };
    const formatDate = (dateStr: any): string => {
      if (!dateStr) return 'N/A';

      // Normalize string typo like "Septmeber"
      const normalized = dateStr.replace('Septmeber', 'September');

      const currentYear = new Date().getFullYear();
      const targetYear = currentYear + 1;

      // Append year to date string
      const withYear = `${normalized} ${targetYear}`;

      const d = new Date(withYear);
      if (isNaN(d.getTime())) return 'N/A';

      return d.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    };
    const formatDatePresentYear = (dateStr: any): string => {
      if (!dateStr) return 'N/A';

      // Normalize string typo like "Septmeber"
      const normalized = dateStr.replace('Septmeber', 'September');

      const currentYear = new Date().getFullYear();

      // Append year to date string
      const withYear = `${normalized} ${currentYear}`;

      const d = new Date(withYear);
      if (isNaN(d.getTime())) return 'N/A';

      return d.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    };
    const formatDateWithNextYear = (dateStr: any): string => {
      if (!dateStr) return 'N/A';

      // Normalize string typo like "Septmeber"
      const normalized = dateStr.replace('Septmeber', 'September');

      const currentYear = new Date().getFullYear();
      const targetYear = currentYear + 4;

      // Append year to date string
      const withYear = `${normalized} ${targetYear}`;

      const d = new Date(withYear);
      if (isNaN(d.getTime())) return 'N/A';

      return d.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    };
    const shortName = (fullName: string): string => {
      if (!fullName) return '';

      return fullName
        .split(' ') // split into words
        .map((word) => word[0]) // take first letter of each word
        .join('') // join them together
        .toUpperCase(); // make uppercase
    };
    const emailToWebsite = (email: string): string => {
      if (!email || !email.includes('@')) return '';

      const username = email.split('@')[0]; // "admin.iaula"
      const parts = username.split('.');

      // Take last part (iaula) and build .com domain
      const main = parts[parts.length - 1];
      return `www.${main}.com`;
    };

    // Get student name
    const getStudentName = (): string => {
      if (personalInfo.firstName && personalInfo.lastName) {
        return `${personalInfo.firstName} ${personalInfo.lastName}`;
      }
      if (personalInfo.firstName) return personalInfo.firstName;
      if (personalInfo.lastName) return personalInfo.lastName;
      return 'N/A';
    };

    // Get address information
    const getPresentAddress = (): string => {
      if (personalInfo.presentAddress) {
        const addr = personalInfo.presentAddress;
        return `${addr.address || ''}, ${addr.city || ''}, ${
          addr.state || ''
        }, ${addr.country || ''}, ${addr.postalCode || ''}`.trim();
      }
      return 'N/A';
    };

    const getPermanentAddress = (): string => {
      if (personalInfo.permanentAddress) {
        const addr = personalInfo.permanentAddress;
        return `${addr.address || ''}, ${addr.city || ''}, ${
          addr.state || ''
        }, ${addr.country || ''}, ${addr.postalCode || ''}`.trim();
      }
      return 'N/A';
    };

    // Get sponsor information
    const getSponsor = (): string => {
      if (personalInfo.sponsor) {
        const sponsor = personalInfo.sponsor;
        return `${sponsor.name || ''} (${sponsor.relation || ''})`;
      }
      return 'N/A';
    };

    // Get emergency contact
    const getEmergencyContact = (): string => {
      if (personalInfo.emergencyContact) {
        const contact = personalInfo.emergencyContact;
        return `${contact.firstName || ''} ${contact.lastName || ''} - ${
          contact.phoneHome || contact.phoneMobile || ''
        }`;
      }
      return 'N/A';
    };

    // Get course name with comprehensive data
    const getCourseName = (): string => {
      if (course.courseTitle) return course.courseTitle;
      if (program.programLevelName) return program.programLevelName;
      if (course.id) return `Course ${course.id}`;
      if (application.courseId) return `Course ${application.courseId}`;
      return 'N/A';
    };

    // Get tuition fee with comprehensive data
    const getTuitionFee = (): string => {
      // First check fees from comprehensive university data
      if (fees.tuitionFee) return `$${parseFloat(fees.tuitionFee).toFixed(2)}`;
      if (fees.length > 0) {
        const tuitionFee = fees.find((fee) =>
          fee.feeType?.toLowerCase().includes('tuition')
        );
        if (tuitionFee && tuitionFee.amount) {
          return `$${parseFloat(tuitionFee.amount).toFixed(2)}`;
        }
        // If no tuition fee specifically, use the first fee
        const firstFee = fees[0];
        if (firstFee && firstFee.amount) {
          return `$${parseFloat(firstFee.amount).toFixed(2)}`;
        }
      }
      // Fallback to course or application data
      if (course.tuitionFee)
        return `$${parseFloat(course.tuitionFee).toFixed(2)}`;
      if (application.totalAmount)
        return `$${parseFloat(application.totalAmount).toFixed(2)}`;
      return 'N/A';
    };

    // Generate ADM number
    const generateAdmNumber = (): string => {
      const year = new Date().getFullYear();
      const randomNum = Math.floor(Math.random() * 10000)
        .toString()
        .padStart(4, '0');
      return `ADM${year}${randomNum}`;
    };

    const estimateTotalCharge = (
      amount: string | number,
      extra = 100
    ): number => {
      // If amount is string like "$18000" → remove $
      const numeric =
        typeof amount === 'string'
          ? parseFloat(amount.replace(/[^0-9.]/g, ''))
          : amount;

      if (isNaN(numeric)) return 0;

      return numeric + extra;
    };

    this.logger.log(
      `ADM Document Service - Final applicationType: ${applicationType}`
    );
    const formatDOB = (dateStr: string): string => {
  if (!dateStr) return "N/A";

  const d = new Date(dateStr);
  if (isNaN(d.getTime())) return "N/A";

  return d.toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric"
  });
};

    const templateData: any = {
      studentId: student.studentId || 'N/A',
      studentName: getStudentName(),
      studentFirstName: personalInfo.firstName || 'N/A',
      studentLastName: personalInfo.lastName || 'N/A',
      studentMiddleName: personalInfo.middleName || 'N/A',
      shortName: shortName(getStudentName()),
      applicationType: applicationType,
      applicationId: application.id || 'N/A',
      email: personalInfo.email || 'N/A',
      phone: personalInfo.phone || 'N/A',
      dateOfBirth: formatDOB(personalInfo.dateOfBirth),
      passport: personalInfo.passport || 'N/A',
      nid: personalInfo.nid || 'N/A',
      presentAddress: getPresentAddress(),
      permanentAddress: getPermanentAddress(),
      preferredSubject: personalInfo.preferredSubject || [],
      preferredCountry: personalInfo.preferredCountry || [],
      issueDate: now.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }),
      fatherName: personalInfo.fatherName || 'N/A',
      motherName: personalInfo.motherName || 'N/A',
      gender: personalInfo.gender || 'N/A',
      maritalStatus: personalInfo.maritalStatus?.status || 'N/A',
      sponsor: getSponsor(),
      emergencyContact: getEmergencyContact(),
      // ADM specific fields
      admNumber: generateAdmNumber(),
      admDate: now.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }),
      universityName: university.name || 'N/A',
      universityAddress: university.address,
      courseName: getCourseName(),
      intakeName: intake.name || 'N/A',
      classStartDate: formatDate(intake.classStartDate || intake.startDate),
      enrollmentDeadline: formatDatePresentYear(
        intake.enrollmentDeadline || intake.endDate
      ),
      applicationDeadline: formatDatePresentYear(
        intake.applicationDeadline || intake.endDate
      ),
      applicationProcessDuration: intake.applicationProcessDuration || 'N/A',
      courseStartDate: formatDate(intake.classStartDate || intake.startDate),
      courseEndDate: formatDateWithNextYear(intake.endDate),

      campusName: campus.campusName || 'N/A',
      campusAddress: campus.address || 'N/A',
      campusPostalCode: campus.postalCode || 'N/A',
      campusCity: campus.city || 'N/A',
      campusState: campus.state || 'N/A',
      campusEmail: campus.email || 'N/A',
      campusWebsite: emailToWebsite(campus.email),
      campusContactNumber: campus.contactNumber || 'N/A',
      tuitionFee: getTuitionFee(),
      creditFee: fees.creditFee || 'N/A',
      semisterFee: fees.semisterFee || 'N/A',
      yearlyFee: fees.yearlyFee || 'N/A',
      estimateTotalCharge: estimateTotalCharge(fees.tuitionFee) || 'N/A',

      institutionCode: university.institutionCode,
      // Student I-20 document URL for merging as last pages
      i20Url: application.studentI20Url || null,
      // ✅ ADDED: Direct studentI20Url field for ADM generation
      studentI20Url: application.studentI20Url || null,
      programName: program.programLevelName || 'N/A',
      graduationFee:50,
      cancelationFee:50,
      totalChargesForCurrentPeriodAttendance:3650,
      totalChargesForStudentObligation:3650
    };

    return templateData;
  }
}
