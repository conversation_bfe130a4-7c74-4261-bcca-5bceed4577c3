import {
  IsString,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  IsO<PERSON>,
  IsBoolean,
  IsEnum,
  IsN<PERSON>ber,
  ValidateNested,
  IsNotEmpty,
  ValidateIf,
} from 'class-validator';
import { Type } from 'class-transformer';

/* --------------------------
   ENUM DEFINITIONS
-------------------------- */
export enum ApplicationTypeEnum {
  F1_INITIAL = 'F-1 initial',
  TRANSFER = 'Transfer',
  REINSTATEMENT = 'Reinstatement',
  NEW_PROGRAM = 'New Program',
  CHANGE_OF_STATUS = 'Change of Status',
  NON_F1 = 'Non F-1',
}

export enum GenderEnum {
  MALE = 'male',
  FEMALE = 'female',
  OTHER = 'other',
}

export enum MaritalStatusEnum {
  SINGLE = 'single',
  MARRIED = 'married',
  DIVORCED = 'divorced',
  WIDOWED = 'widowed',
}

/* --------------------------
   DEPENDENT INFO
-------------------------- */
export class DependentInfoDto {
  @IsBoolean()
  hasDependency: boolean;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ChildrenDto)
  children?: ChildrenDto[];
}

export class ChildrenDto {
  @IsString()
  firstName: string;

  @IsOptional()
  @IsString()
  middleName?: string;

  @IsString()
  lastName: string;

  @IsString()
  passportNumber: string;

  @IsString()
  dateOfBirth: string;

  @IsString()
  relationship: string;

  @IsString()
  gender: string;

  @IsString()
  nationality: string;

  @IsString()
  countryOfBirth: string;

  @IsString()
  @IsNotEmpty()
  cityOfBirth: string;
}

/* --------------------------
   ADDRESS DTO
-------------------------- */
export class AddressDto {
  @IsString()
  street: string;

  @IsOptional()
  @IsString()
  apartment?: string;

  @IsString()
  country: string;

  @IsString()
  state: string;

  @IsString()
  city: string;

  @IsString()
  postalCode: string;
}

/* --------------------------
   MARITAL STATUS DTO
-------------------------- */
export class MaritalStatusDto {
  @IsEnum(MaritalStatusEnum, {
    message: (args) =>
      `status must be one of the following values: ${Object.values(MaritalStatusEnum).join(', ')}`,
  })
  status: MaritalStatusEnum;

  // ✅ Only required if status = 'married'
  @ValidateIf((o) => o.status === MaritalStatusEnum.MARRIED)
  @IsString()
  @IsNotEmpty()
  spouseFirstName?: string;

  @ValidateIf((o) => o.status === MaritalStatusEnum.MARRIED)
  @IsString()
  @IsNotEmpty()
  spouseLastName?: string;

  @IsOptional()
  @IsString()
  spouseMiddleName?: string;

  @IsOptional()
  @IsString()
  spousePhone?: string;

  @IsOptional()
  @IsString()
  spousePassport?: string;
}

/* --------------------------
   SPONSOR DTO
-------------------------- */
export class SponsorIdentityDto {
  @IsString()
  type: string;

  @IsString()
  number: string;
}

export class SponsorDto {
  @IsString()
  @IsNotEmpty()
  lastName: string;

  @IsOptional()
  @IsString()
  middleName?: string;

  @IsString()
  @IsNotEmpty()
  firstName: string;

  @IsString()
  relation: string;

  @IsString()
  phone: string;

  @ValidateNested()
  @Type(() => AddressDto)
  mailingAddress: AddressDto;

  @IsString()
  dateOfBirth: string;

  @ValidateNested()
  @Type(() => SponsorIdentityDto)
  identity: SponsorIdentityDto;

  @IsOptional()
  @IsBoolean()
  isCompanyRepresentative?: boolean;

  @IsOptional()
  @IsString()
  companyName?: string;
}

/* --------------------------
   EMERGENCY CONTACT DTO
-------------------------- */
export class EmergencyContactDto {
  @IsString()
  @IsNotEmpty()
  lastName: string;

  @IsOptional()
  @IsString()
  middleName?: string;

  @IsString()
  @IsNotEmpty()
  firstName: string;

  @IsString()
  phoneHome: string;

  @IsString()
  phoneMobile: string;

  @IsString()
  relation: string;
}

/* --------------------------
   SOCIAL LINKS DTO
-------------------------- */
export class SocialLinkDto {
  @IsString()
  platform: string;

  @IsString()
  url: string;
}

/* --------------------------
   MAIN DTO
-------------------------- */
export class CreateApplicationOfficerStudentDto {
  @IsString()
  @IsNotEmpty()
  nationality: string;

  @IsNotEmpty()
  @IsEnum(ApplicationTypeEnum, {
    message: (args) =>
      `applicationType must be one of the following values: ${Object.values(ApplicationTypeEnum).join(', ')}`,
  })
  applicationType: ApplicationTypeEnum;

  @IsNumber()
  organizationId: number;

  @IsOptional()
  @IsNumber()
  agencyId?: number | null;

  @IsString()
  @IsNotEmpty()
  firstName: string;

  @IsString()
  @IsNotEmpty()
  lastName: string;

  @IsOptional()
  @IsString()
  middleName?: string;

  @IsEmail()
  @IsNotEmpty()
  email: string;

  @IsString()
  @IsNotEmpty()
  phone: string;

  @IsOptional()
  @IsString()
  guardianPhone?: string;

  @IsString()
  dateOfBirth?: string;

  @IsString()
  @IsNotEmpty()
  countryOfBirth?: string;

  @IsString()
  @IsNotEmpty()
  cityOfBirth?: string;

  @IsEnum(GenderEnum, {
    message: (args) =>
      `gender must be one of the following values: ${Object.values(GenderEnum).join(', ')}`,
  })
  gender: GenderEnum;

  @IsString()
  fatherName: string;

  @IsString()
  motherName: string;

  @IsOptional()
  @IsString()
  passport?: string;

  @IsString()
  @IsNotEmpty()
  ethnicSurvey?: string;

  @IsOptional()
  @IsString()
  englishProficiency?: string;

  @IsOptional()
  @IsString()
  sevisId?: string;

  @IsOptional()
  @IsString()
  previousStudentId?: string;

  @IsOptional()
  @IsString()
  referenceSource?: string;

  @IsOptional()
  @IsObject()
  identity?: {
    type: string;
    number: string;
  };

  @IsOptional()
  @IsObject()
  dependentsInfo?: {
    hasDependency: boolean;
    children?: Array<ChildrenDto>;
  };

  @ValidateNested()
  @Type(() => MaritalStatusDto)
  maritalStatus: MaritalStatusDto;

  @ValidateNested()
  @Type(() => AddressDto)
  presentAddress: AddressDto;

  @ValidateNested()
  @Type(() => AddressDto)
  permanentAddress: AddressDto;

  @ValidateNested()
  @Type(() => SponsorDto)
  sponsor: SponsorDto;

  @ValidateNested()
  @Type(() => EmergencyContactDto)
  emergencyContact: EmergencyContactDto;

  @IsOptional()
  @IsString()
  reference?: string;

  @IsBoolean()
  isLoginApplicable: boolean;

  @IsOptional()
  @IsBoolean()
  isPersonalInfoEdited?: boolean;

  @IsOptional()
  @IsNumber()
  createdBy?: number;

  @IsOptional()
  @IsArray()
  metaData?: Array<{
    isChecked: boolean;
    text: string;
  }>;
}
