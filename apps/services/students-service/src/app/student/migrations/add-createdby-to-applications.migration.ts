import { QueryInterface, DataTypes } from 'sequelize';

export const up = async (queryInterface: QueryInterface): Promise<void> => {
  // Add createdBy column to applications table
  try {
    await queryInterface.addColumn('applications', 'createdBy', {
      type: DataTypes.BIGINT,
      allowNull: true,
      defaultValue: null,
      comment: 'User ID of the application officer who created this application (null if created by student)'
    });

    // Add index for createdBy for better query performance
    await queryInterface.addIndex('applications', ['createdBy']);
    
    console.log('✅ Successfully added createdBy column to applications table');
  } catch (error) {
    // If column already exists, log and continue
    if (error.message?.includes('already exists') || error.message?.includes('duplicate column')) {
      console.log('ℹ️ createdBy column already exists in applications table');
    } else {
      throw error;
    }
  }
};

export const down = async (queryInterface: QueryInterface): Promise<void> => {
  try {
    // Remove index first
    await queryInterface.removeIndex('applications', ['createdBy']);
    
    // Remove column
    await queryInterface.removeColumn('applications', 'createdBy');
    
    console.log('✅ Successfully removed createdBy column from applications table');
  } catch (error) {
    console.log('ℹ️ createdBy column may not exist in applications table');
  }
};
