import { QueryInterface, Sequelize } from 'sequelize';

export async function up(queryInterface: QueryInterface, Sequelize: any) {
  // Add isCalGenerated column
  await queryInterface.addColumn('applications', 'isCalGenerated', {
    type: Sequelize.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: 'Flag indicating if CAL document has been generated for this application'
  });

  // Add isAdmGenerated column
  await queryInterface.addColumn('applications', 'isAdmGenerated', {
    type: Sequelize.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: 'Flag indicating if ADM document has been generated for this application'
  });

  // Add isApGenerated column
  await queryInterface.addColumn('applications', 'isApGenerated', {
    type: Sequelize.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: 'Flag indicating if Admission Portfolio has been generated for this application'
  });

  // Add indexes for better query performance
  await queryInterface.addIndex('applications', ['isCalGenerated']);
  await queryInterface.addIndex('applications', ['isAdmGenerated']);
  await queryInterface.addIndex('applications', ['isApGenerated']);
}

export async function down(queryInterface: QueryInterface, Sequelize: any) {
  // Remove indexes first
  await queryInterface.removeIndex('applications', ['isCalGenerated']);
  await queryInterface.removeIndex('applications', ['isAdmGenerated']);
  await queryInterface.removeIndex('applications', ['isApGenerated']);

  // Remove columns
  await queryInterface.removeColumn('applications', 'isCalGenerated');
  await queryInterface.removeColumn('applications', 'isAdmGenerated');
  await queryInterface.removeColumn('applications', 'isApGenerated');
} 