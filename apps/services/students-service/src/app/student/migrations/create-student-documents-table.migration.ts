import { QueryInterface, DataTypes } from 'sequelize';

export const up = async (queryInterface: QueryInterface): Promise<void> => {
  await queryInterface.createTable('student_documents', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true
    },
    studentId: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'students',
        key: 'studentId'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE'
    },
    section: {
      type: DataTypes.ENUM(
        'profile',
        'academic',
        'proficiency',
        'sponsor',
        'dependent',
        'child',
        'other'
      ),
      allowNull: false
    },
    field: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: 'Field name like photo, signature,"10th grade", "12th grade", etc.'
    },
    filename: {
      type: DataTypes.STRING,
      allowNull: false
    },
    url: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: 'Full URL to the uploaded file in MinIO'
    },
    mimeType: {
      type: DataTypes.STRING,
      allowNull: true
    },
    fileSize: {
      type: DataTypes.BIGINT,
      allowNull: true,
      comment: 'File size in bytes'
    },
    originalName: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Original filename from upload'
    },
    bucket: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'MinIO bucket name'
    },
    objectKey: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'MinIO object key/path'
    },
    metadata: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'Additional metadata like dependent/child info'
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true
    },
    verificationStatus: {
      type: DataTypes.ENUM('pending', 'verified', 'rejected', 'expired'),
      allowNull: true,
      defaultValue: 'pending',
      comment: 'Document verification status'
    },
    expiryDate: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      comment: 'Document expiry date if applicable'
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  });

  // Add indexes for better query performance
  await queryInterface.addIndex('student_documents', ['studentId']);
  await queryInterface.addIndex('student_documents', ['section']);
  await queryInterface.addIndex('student_documents', ['field']);
  await queryInterface.addIndex('student_documents', [
    'studentId',
    'section',
    'field'
  ]);
  await queryInterface.addIndex('student_documents', ['created_at']);
  await queryInterface.addIndex('student_documents', ['verificationStatus']);
  await queryInterface.addIndex('student_documents', ['isActive']);
};

export const down = async (queryInterface: QueryInterface): Promise<void> => {
  await queryInterface.dropTable('student_documents');
};
