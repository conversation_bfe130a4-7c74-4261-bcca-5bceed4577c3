import { QueryInterface, DataTypes } from 'sequelize';

export const up = async (queryInterface: QueryInterface): Promise<void> => {
  // Add createdBy column to students table
  try {
    await queryInterface.addColumn('students', 'createdBy', {
      type: DataTypes.BIGINT,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
      comment: 'User ID of the person who created this student (null if self-created)'
    });

    // Add index for createdBy for better query performance
    await queryInterface.addIndex('students', ['createdBy']);
    
    console.log('✅ Successfully added createdBy column to students table');
  } catch (error) {
    // If column already exists, log and continue
    if (error.message?.includes('already exists') || error.message?.includes('duplicate column')) {
      console.log('ℹ️ createdBy column already exists in students table');
    } else {
      throw error;
    }
  }
};

export const down = async (queryInterface: QueryInterface): Promise<void> => {
  try {
    // Remove index first
    await queryInterface.removeIndex('students', ['createdBy']);
    
    // Remove column
    await queryInterface.removeColumn('students', 'createdBy');
    
    console.log('✅ Successfully removed createdBy column from students table');
  } catch (error) {
    console.log('ℹ️ createdBy column may not exist in students table');
  }
};