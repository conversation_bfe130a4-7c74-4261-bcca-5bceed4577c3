import { QueryInterface, DataTypes } from 'sequelize';

export const up = async (queryInterface: QueryInterface): Promise<void> => {
  // Add userId column to students table if it doesn't exist
  try {
    await queryInterface.addColumn('students', 'userId', {
      type: DataTypes.BIGINT,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
      comment: 'User ID from auth service - links student profile to user account'
    });

    // Add index for userId for better query performance
    await queryInterface.addIndex('students', ['userId']);
    
    console.log('✅ Successfully added userId column to students table');
  } catch (error) {
    // If column already exists, log and continue
    if (error.message?.includes('already exists') || error.message?.includes('duplicate column')) {
      console.log('ℹ️ userId column already exists in students table');
    } else {
      throw error;
    }
  }
};

export const down = async (queryInterface: QueryInterface): Promise<void> => {
  try {
    // Remove userId column from students table
    await queryInterface.removeColumn('students', 'userId');
    console.log('✅ Successfully removed userId column from students table');
  } catch (error) {
    console.log('ℹ️ Could not remove userId column:', error.message);
  }
}; 