import { QueryInterface, DataTypes } from 'sequelize';

export const up = async (queryInterface: QueryInterface): Promise<void> => {
  // Add isApApproved column
  await queryInterface.addColumn('applications', 'isApApproved', {
    type: DataTypes.BOOLEAN,
    allowNull: true,
    defaultValue: null,
    comment: 'Flag to track if AP (Admission Portfolio) is approved'
  });

  // Add apRejectionNotes column
  await queryInterface.addColumn('applications', 'apRejectionNotes', {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Notes for AP rejection if applicable'
  });

  // Add index for isApApproved for better query performance
  await queryInterface.addIndex('applications', ['isApApproved']);
};

export const down = async (queryInterface: QueryInterface): Promise<void> => {
  // Remove index first
  await queryInterface.removeIndex('applications', ['isApApproved']);
  
  // Remove columns
  await queryInterface.removeColumn('applications', 'isApApproved');
  await queryInterface.removeColumn('applications', 'apRejectionNotes');
};