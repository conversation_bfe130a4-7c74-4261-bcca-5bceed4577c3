// student.proto → TypeScript interfaces

export interface CreateStudentRequest {
  studentId?: string | null;
  organizationId: number;
  agencyId?: number | null;
  firstName: string;
  lastName: string;
  nameInNative: string;
  middleName?: string;
  email: string;
  phone: string;
  guardianPhone: string;
  dateOfBirth: string; // YYYY-MM-DD
  countryOfBirth?: string;
  cityOfBirth?: string;
  gender: string;
  fatherName: string;
  motherName: string;
  nid: string;
  passport: string;
  ethnicSurvey?: string;
  englishProficiency?: string;
  sevisId?: string;
  previousStudentId?: string;
  referenceSource?: string;
  identity?: Record<string, any>;
  dependentsInfo?: Record<string, any>;

  presentAddress: Address;
  permanentAddress: Address;
  maritalStatus: MaritalStatus;
  sponsor: Sponsor;
  emergencyContact: EmergencyContact;

  preferredSubject: string[];
  preferredCountry: string[];
  socialLinks: SocialLink[];

  reference: string;
  note: string;
  
  userId?: number; // Add userId field to link with auth service user
  user_id?: string; // Add user_id field for CreateStudentAfterRegistrationRequest compatibility
  isPersonalInfoEdited?: boolean; // Flag to track if personal info has been edited
  isLoginApplicable?: boolean; // Flag to control whether user should receive OTP emails and be able to login
  applicationType?: string; // Field to specify the application type (e.g., "online", "offline")
  nationality?: string; // Field to specify the student's nationality
  createdBy?: number; // User ID of the person who created this student (null if self-created)
  metaData?: Array<{
    isChecked: boolean;
    text: string;
  }>; // Metadata array for application officer use
}

export interface StudentResponse {
  student: Student;
}

export interface GetStudentsByApplicationOfficerRequest {
  applicationOfficerId: number;
}

export interface GetStudentsByApplicationOfficerResponse {
  students: Student[];
  total: number;
  success: boolean;
}

export interface CreateStudentAfterRegistrationRequest {
  userId: number;
  name: string;
  email: string;
  phone: string;
  nationality: string;

  organization_name: string;
  role_name: string;
  department_name: string;
}

export interface Student {
  // original CreateStudentRequest fields
  organizationId: number;
  agencyId?: number | null;
  firstName: string;
  lastName: string;
  nameInNative: string;
  email: string;
  phone: string;
  guardianPhone: string;
  dateOfBirth: string;
  gender: string;
  fatherName: string;
  motherName: string;
  nid: string;
  passport: string;

  presentAddress: Address;
  permanentAddress: Address;
  maritalStatus: MaritalStatus;
  sponsor: Sponsor;
  emergencyContact: EmergencyContact;

  preferredSubject: string[];
  preferredCountry: string[];
  socialLinks: SocialLink[];

  reference: string;
  note: string;
  isPersonalInfoEdited?: boolean; // Flag to track if personal info has been edited

  profileImageUrl?: string;
  signatureUrl?: string;
  passportUrl?: string;

  // metadata
  id: string;
  createdAt: string; // ISO8601
  updatedAt: string; // ISO8601

  userId?: number; // Add userId field to link with auth service user
}

export interface Address {
  address: string;
  country: string;
  state: string;
  city: string;
  postalCode: string;
}

export interface MaritalStatus {
  status: string;
  spouseName: string;
  spousePhone: string;
  spousePassport: string;
}

export interface Sponsor {
  name: string;
  relation: string;
  phone: string;
  email: string;
  nationalId: string;
}

export interface EmergencyContact {
  lastName: string;
  middleName: string;
  firstName: string;
  phoneHome: string;
  phoneMobile: string;
  relation: string;
}

export interface SocialLink {
  platform: string;
  url: string;
}
