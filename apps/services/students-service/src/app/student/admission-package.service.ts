import { Injectable, Logger } from '@nestjs/common';
// import { UploadService } from '@apply-goal-backend/common';
import { HtmlTemplateService, PdfTemplateService } from '@apply-goal-backend/utils';

export type TemplateType = 'pdf' | 'html';

export interface AdmissionPackageData {
  studentId: string;
  studentName: string;
  applicationType: string;
  email: string;
  phone: string;
  dateOfBirth: string;
  passport: string;
  nid: string;
  presentAddress: string;
  permanentAddress: string;
  preferredSubject: string[];
  preferredCountry: string[];
  issueDate: string;
  // Additional fields that might be needed
  fatherName: string;
  motherName: string;
  gender: string;
  maritalStatus: string;
  sponsor: string;
  emergencyContact: string;
}

@Injectable()
export class AdmissionPackageService {
  private readonly logger = new Logger(AdmissionPackageService.name);

  constructor(
    // private readonly uploadService: UploadService,
    private readonly htmlTemplateService: HtmlTemplateService,
    private readonly pdfTemplateService: PdfTemplateService
  ) {}

  private prepareTemplateData(studentData: any): AdmissionPackageData {
    const now = new Date();
    
    // Extract student information
    const student = studentData;
    const personalInfo = studentData.personalInfo || {};
    
    // Log the raw data being extracted
    this.logger.debug('Raw student data:', {
      studentId: studentData.studentId,
      student: student,
      personalInfo: personalInfo
    });
    
    // Format dates properly
    const formatDate = (date: any): string => {
      if (!date) return 'N/A';
      const d = new Date(date);
      if (isNaN(d.getTime())) return 'N/A';
      return d.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    };
    
    // Get student name
    const getStudentName = (): string => {
      if (personalInfo.firstName && personalInfo.lastName) {
        return `${personalInfo.firstName} ${personalInfo.lastName}`;
      }
      if (personalInfo.firstName) return personalInfo.firstName;
      if (personalInfo.lastName) return personalInfo.lastName;
      return 'N/A';
    };

    // Get address information
    const getPresentAddress = (): string => {
      if (personalInfo.presentAddress) {
        const addr = personalInfo.presentAddress;
        return `${addr.address || ''}, ${addr.city || ''}, ${addr.state || ''}, ${addr.country || ''}, ${addr.postalCode || ''}`.trim();
      }
      return 'N/A';
    };

    const getPermanentAddress = (): string => {
      if (personalInfo.permanentAddress) {
        const addr = personalInfo.permanentAddress;
        return `${addr.address || ''}, ${addr.city || ''}, ${addr.state || ''}, ${addr.country || ''}, ${addr.postalCode || ''}`.trim();
      }
      return 'N/A';
    };

    // Get sponsor information
    const getSponsor = (): string => {
      if (personalInfo.sponsor) {
        const sponsor = personalInfo.sponsor;
        return `${sponsor.name || ''} (${sponsor.relation || ''})`;
      }
      return 'N/A';
    };

    // Get emergency contact
    const getEmergencyContact = (): string => {
      if (personalInfo.emergencyContact) {
        const contact = personalInfo.emergencyContact;
        return `${contact.firstName || ''} ${contact.lastName || ''} - ${contact.phoneHome || contact.phoneMobile || ''}`;
      }
      return 'N/A';
    };

    const templateData: AdmissionPackageData = {
      studentId: studentData.studentId || 'N/A',
      studentName: getStudentName(),
      applicationType: studentData.applicationType || 'F-1 initial',
      email: personalInfo.email || 'N/A',
      phone: personalInfo.phone || 'N/A',
      dateOfBirth: formatDate(personalInfo.dateOfBirth),
      passport: personalInfo.passport || 'N/A',
      nid: personalInfo.nid || 'N/A',
      presentAddress: getPresentAddress(),
      permanentAddress: getPermanentAddress(),
      preferredSubject: personalInfo.preferredSubject || [],
      preferredCountry: personalInfo.preferredCountry || [],
      issueDate: now.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }),
      fatherName: personalInfo.fatherName || 'N/A',
      motherName: personalInfo.motherName || 'N/A',
      gender: personalInfo.gender || 'N/A',
      maritalStatus: personalInfo.maritalStatus?.status || 'N/A',
      sponsor: getSponsor(),
      emergencyContact: getEmergencyContact()
    };

    // Log the processed template data
    this.logger.debug('Processed template data:', {
      studentId: templateData.studentId,
      studentName: templateData.studentName,
      applicationType: templateData.applicationType,
      email: templateData.email,
      phone: templateData.phone,
      dateOfBirth: templateData.dateOfBirth,
      passport: templateData.passport,
      issueDate: templateData.issueDate
    });

    return templateData;
  }
} 