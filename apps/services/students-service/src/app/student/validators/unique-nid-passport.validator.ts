import {
  registerDecorator,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  ValidationArguments,
} from 'class-validator';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { StudentPersonalInfo } from '../models/student-personal-info.model';

@ValidatorConstraint({ async: true })
@Injectable()
export class IsUniqueNidPassportConstraint implements ValidatorConstraintInterface {
  constructor(
    @InjectModel(StudentPersonalInfo)
    private readonly studentPersonalInfoModel: typeof StudentPersonalInfo,
  ) {}

  async validate(value: string, args: ValidationArguments) {
    const [field] = args.constraints;
    
    if (!value) {
      return true; // Let other validators handle empty values
    }

    try {
      const existingRecord = await this.studentPersonalInfoModel.findOne({
        where: { [field]: value }
      });
      
      return !existingRecord; // Return true if no existing record found
    } catch (error) {
      return false; // Return false on database error
    }
  }

  defaultMessage(args: ValidationArguments) {
    const [field] = args.constraints;
    const fieldName = field === 'nid' ? 'NID' : 'Passport';
    return `${fieldName} ${args.value} already exists. ${fieldName} must be unique.`;
  }
}

export function IsUniqueNidPassport(field: string, validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [field],
      validator: IsUniqueNidPassportConstraint,
    });
  };
}