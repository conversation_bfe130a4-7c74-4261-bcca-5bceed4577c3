import { Module } from '@nestjs/common';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { join } from 'path';
import { UniversityClientService } from './university-client.service';

@Module({
  imports: [
    ClientsModule.register([
      {
        name: 'UNIVERSITY_SERVICE',
        transport: Transport.GRPC,
        options: {
          package: 'university',
          protoPath: '/app/libs/shared/dto/src/lib/university/university.proto',
          url: `${process.env.GRPC_UNIVERSITY_SERVICE_HOST || 'university-service'}:${process.env.GRPC_UNIVERSITY_SERVICE_PORT || '50059'}`,
          loader: {
            longs: String,
            enums: String,
            defaults: true,
            oneofs: true,
          },
          // Added performance optimizations
          maxReceiveMessageLength: 10 * 1024 * 1024, // 10MB limit
          maxSendMessageLength: 10 * 1024 * 1024,   // 10MB limit
          keepalive: {
            keepaliveTimeMs: 30000,
            keepaliveTimeoutMs: 5000,
            keepalivePermitWithoutCalls: 1,
            http2MaxPingsWithoutData: 0,
            http2MinTimeBetweenPingsMs: 10000,
            http2MinPingIntervalWithoutDataMs: 300000
          }
        },
      },
    ]),
  ],
  providers: [UniversityClientService],
  exports: [UniversityClientService],
})
export class UniversityModule {} 