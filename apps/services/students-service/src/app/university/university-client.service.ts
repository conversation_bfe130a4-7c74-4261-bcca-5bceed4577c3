import { Injectable, OnModuleInit, Inject } from '@nestjs/common';
import { ClientGrpc } from '@nestjs/microservices';
import { firstValueFrom } from 'rxjs';
import { Logger } from '@nestjs/common';

@Injectable()
export class UniversityClientService implements OnModuleInit {
  private universityService: any;
  private readonly logger = new Logger(UniversityClientService.name);

  constructor(
    @Inject('UNIVERSITY_SERVICE')
    private readonly client: ClientGrpc
  ) {}

  onModuleInit() {
    this.universityService = this.client.getService<any>('UniversityService');
  }

  // University operations
  async getUniversity(universityId: number) {
    try {
      return await firstValueFrom(
        this.universityService.GetUniversity({ id: universityId })
      );
    } catch (error) {
      this.logger.error(`Failed to get university ${universityId}:`, error);
      throw error;
    }
  }

  async listUniversities(filters?: any) {
    try {
      return await firstValueFrom(
        this.universityService.ListUniversities(filters || {})
      );
    } catch (error) {
      this.logger.error('Failed to list universities:', error);
      throw error;
    }
  }

  // Course operations
  async getCourse(courseId: number) {
    try {
      return await firstValueFrom(
        this.universityService.GetCourse({ id: courseId })
      );
    } catch (error) {
      this.logger.error(`Failed to get course ${courseId}:`, error);
      throw error;
    }
  }

  async getCourseRequirements(data: {
    courseId: number;
    programLevelId: number;
  }): Promise<any> {
    try {
      return await firstValueFrom(
        this.universityService.GetCourseRequirement(data)
      );
    } catch (error) {
      this.logger.error(`Failed to get course requirements:`, error);
      throw error;
    }
  }

  async listCourses(filters?: any) {
    try {
      return await firstValueFrom(
        this.universityService.ListCourse(filters || {})
      );
    } catch (error) {
      this.logger.error('Failed to list courses:', error);
      throw error;
    }
  }

  // Intake operations
  async getIntakesByUniversity(universityId: number) {
    try {
      return await firstValueFrom(
        this.universityService.ListIntakesByUniversity({ universityId })
      );
    } catch (error) {
      this.logger.error(
        `Failed to get intakes for university ${universityId}:`,
        error
      );
      throw error;
    }
  }

  // Program Level operations
  async getProgramLevelsByUniversity(universityId: number) {
    try {
      return await firstValueFrom(
        this.universityService.ListProgramLevelByUniversity({ universityId })
      );
    } catch (error) {
      this.logger.error(
        `Failed to get program levels for university ${universityId}:`,
        error
      );
      throw error;
    }
  }

  // Field of Study operations
  async getFieldsOfStudyByUniversity(universityId: number) {
    try {
      return await firstValueFrom(
        this.universityService.listFieldOfStudyByUniversity({ universityId })
      );
    } catch (error) {
      this.logger.error(
        `Failed to get fields of study for university ${universityId}:`,
        error
      );
      throw error;
    }
  }

  // Campus operations
  async getCampusesByUniversity(universityId: number) {
    try {
      return await firstValueFrom(
        this.universityService.GetCampusList({ universityId })
      );
    } catch (error) {
      this.logger.error(
        `Failed to get campuses for university ${universityId}:`,
        error
      );
      throw error;
    }
  }

  // Fee operations
  async getFeesByUniversity(universityId: number) {
    try {
      return await firstValueFrom(
        this.universityService.ListProgramLevelIntakeFees({ universityId })
      );
    } catch (error) {
      this.logger.error(
        `Failed to get fees for university ${universityId}:`,
        error
      );
      throw error;
    }
  }

  // Comprehensive university information
  async getComprehensiveUniversityInformation(request: {
    universityId: number;
    campusId: number;
    programId: number;
    intakeId: number;
    courseId: number;
  }) {
    try {
      this.logger.log('Fetching comprehensive university information with request:', request);
      return await firstValueFrom(
        this.universityService.ComprehensiveUniversityInformationForStudent(request)
      );
    } catch (error) {
      this.logger.error(
        'Failed to get comprehensive university information:',
        error
      );
      throw error;
    }
  }
}
