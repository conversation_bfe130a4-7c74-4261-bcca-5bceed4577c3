import { Test, TestingModule } from '@nestjs/testing';
import { CalDocumentService } from './cal-document.service';
import { PdfConverterService } from '@apply-goal-backend/utils';
import { UploadService } from '@apply-goal-backend/common';

describe('CalDocumentService - MinIO Integration', () => {
  let service: CalDocumentService;
  let pdfConverter: PdfConverterService;
  let uploadService: UploadService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CalDocumentService,
        {
          provide: PdfConverterService,
          useValue: {
            convertHtmlToPdf: jest.fn().mockResolvedValue(Buffer.from('test pdf')),
            convertHtmlStringToPdf: jest.fn().mockResolvedValue(Buffer.from('test pdf')),
            processHtmlTemplate: jest.fn().mockResolvedValue('<html>test</html>'),
            getAvailableTemplates: jest.fn().mockResolvedValue(['CAL-Page-01', 'CAL-Page-02'])
          }
        },
        {
          provide: UploadService,
          useValue: {
            uploadFile: jest.fn().mockResolvedValue({ url: 'http://localhost:9000/applygoal-files/files/test-uuid.pdf' })
          }
        }
      ],
    }).compile();

    service = module.get<CalDocumentService>(CalDocumentService);
    pdfConverter = module.get<PdfConverterService>(PdfConverterService);
    uploadService = module.get<UploadService>(UploadService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('generateCalDocumentWithMinio', () => {
    it('should generate PDF document and upload to MinIO', async () => {
      const applicationData = {
        id: 1,
        student: {
          fullName: 'John Doe',
          passport: 'AB123456',
          dateOfBirth: '1990-01-01'
        },
        applicationId: 'APP001',
        course: {
          name: 'Computer Science',
          startDate: '2024-09-01',
          endDate: '2028-05-31'
        },
        intake: {
          name: 'Fall 2024'
        },
        campus: {
          name: 'Los Angeles Campus'
        },
        totalAmount: 25000
      };

      const result = await service.generateCalDocumentWithMinio(1, applicationData, {
        outputFormat: 'pdf',
        includeAllPages: true
      });

      expect(result.success).toBe(true);
      expect(result.documentContent).toBeDefined();
      expect(result.filename).toContain('CAL-Document-1.pdf');
      expect(result.contentType).toBe('application/pdf');
      expect(result.minioUrl).toBeDefined();
      expect(result.objectKey).toBeDefined();
      expect(uploadService.uploadFile).toHaveBeenCalled();
    });

    it('should handle MinIO upload errors gracefully', async () => {
      // Mock upload service to throw an error
      jest.spyOn(uploadService, 'uploadFile').mockRejectedValue(new Error('MinIO upload failed'));

      const applicationData = {
        id: 1,
        student: {
          fullName: 'John Doe',
          passport: 'AB123456',
          dateOfBirth: '1990-01-01'
        },
        applicationId: 'APP001',
        course: {
          name: 'Computer Science',
          startDate: '2024-09-01',
          endDate: '2028-05-31'
        },
        intake: {
          name: 'Fall 2024'
        },
        campus: {
          name: 'Los Angeles Campus'
        },
        totalAmount: 25000
      };

      const result = await service.generateCalDocumentWithMinio(1, applicationData, {
        outputFormat: 'pdf',
        includeAllPages: true
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('Failed to upload document to MinIO');
    });

    it('should generate HTML document and upload to MinIO', async () => {
      const applicationData = {
        id: 1,
        student: {
          fullName: 'Jane Smith',
          passport: 'CD789012',
          dateOfBirth: '1995-05-15'
        },
        applicationId: 'APP002',
        course: {
          name: 'Business Administration',
          startDate: '2024-09-01',
          endDate: '2028-05-31'
        },
        intake: {
          name: 'Fall 2024'
        },
        campus: {
          name: 'Los Angeles Campus'
        },
        totalAmount: 30000
      };

      const result = await service.generateCalDocumentWithMinio(1, applicationData, {
        outputFormat: 'html',
        includeAllPages: true
      });

      expect(result.success).toBe(true);
      expect(result.documentContent).toBeDefined();
      expect(result.filename).toContain('CAL-Document-1.html');
      expect(result.contentType).toBe('text/html');
      expect(result.minioUrl).toBeDefined();
      expect(result.objectKey).toBeDefined();
    });

    it('should handle document generation failures', async () => {
      // Mock PDF converter to throw an error
      jest.spyOn(pdfConverter, 'convertHtmlToPdf').mockRejectedValue(new Error('PDF generation failed'));

      const applicationData = {
        id: 1,
        student: {
          fullName: 'John Doe',
          passport: 'AB123456',
          dateOfBirth: '1990-01-01'
        },
        applicationId: 'APP001',
        course: {
          name: 'Computer Science',
          startDate: '2024-09-01',
          endDate: '2028-05-31'
        },
        intake: {
          name: 'Fall 2024'
        },
        campus: {
          name: 'Los Angeles Campus'
        },
        totalAmount: 25000
      };

      const result = await service.generateCalDocumentWithMinio(1, applicationData, {
        outputFormat: 'pdf',
        includeAllPages: true
      });

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });
  });

  describe('uploadDocumentToMinio', () => {
    it('should upload document to MinIO with correct parameters', async () => {
      const documentContent = Buffer.from('test pdf content');
      const filename = 'CAL-Document-123.pdf';
      const contentType = 'application/pdf';
      const applicationId = 123;

      // Use reflection to access private method for testing
      const uploadMethod = (service as any).uploadDocumentToMinio.bind(service);
      const result = await uploadMethod(documentContent, filename, contentType, applicationId);

      expect(result.minioUrl).toBeDefined();
      expect(result.objectKey).toBeDefined();
      expect(uploadService.uploadFile).toHaveBeenCalledWith(
        expect.objectContaining({
          fieldname: 'cal-document',
          originalname: filename,
          mimetype: contentType,
          size: documentContent.length,
          buffer: documentContent
        }),
        'file'
      );
    });

    it('should handle MinIO upload errors', async () => {
      jest.spyOn(uploadService, 'uploadFile').mockRejectedValue(new Error('MinIO connection failed'));

      const documentContent = Buffer.from('test pdf content');
      const filename = 'CAL-Document-123.pdf';
      const contentType = 'application/pdf';
      const applicationId = 123;

      const uploadMethod = (service as any).uploadDocumentToMinio.bind(service);
      
      await expect(uploadMethod(documentContent, filename, contentType, applicationId))
        .rejects.toThrow('Failed to upload document to MinIO: MinIO connection failed');
    });
  });

  describe('MinIO URL and Object Key Extraction', () => {
    it('should correctly extract object key from MinIO URL', async () => {
      // Mock upload service to return a specific URL format
      jest.spyOn(uploadService, 'uploadFile').mockResolvedValue({
        url: 'http://localhost:9000/applygoal-files/files/test-uuid.pdf'
      });

      const documentContent = Buffer.from('test pdf content');
      const filename = 'CAL-Document-123.pdf';
      const contentType = 'application/pdf';
      const applicationId = 123;

      const uploadMethod = (service as any).uploadDocumentToMinio.bind(service);
      const result = await uploadMethod(documentContent, filename, contentType, applicationId);

      expect(result.minioUrl).toBe('http://localhost:9000/applygoal-files/files/test-uuid.pdf');
      expect(result.objectKey).toBe('files/test-uuid.pdf');
    });
  });

  describe('Integration with existing generateCalDocument', () => {
    it('should use existing generateCalDocument method and add MinIO upload', async () => {
      const applicationData = {
        id: 1,
        student: {
          fullName: 'John Doe',
          passport: 'AB123456',
          dateOfBirth: '1990-01-01'
        },
        applicationId: 'APP001',
        course: {
          name: 'Computer Science',
          startDate: '2024-09-01',
          endDate: '2028-05-31'
        },
        intake: {
          name: 'Fall 2024'
        },
        campus: {
          name: 'Los Angeles Campus'
        },
        totalAmount: 25000
      };

      const result = await service.generateCalDocumentWithMinio(1, applicationData, {
        outputFormat: 'pdf',
        includeAllPages: true
      });

      // Verify that the base document generation worked
      expect(result.success).toBe(true);
      expect(result.documentContent).toBeDefined();
      expect(result.filename).toBeDefined();
      expect(result.contentType).toBeDefined();
      expect(result.fileSize).toBeDefined();

      // Verify that MinIO upload was added
      expect(result.minioUrl).toBeDefined();
      expect(result.objectKey).toBeDefined();
    });
  });
}); 