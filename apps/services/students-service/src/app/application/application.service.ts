import {
  Injectable,
  NotFoundException,
  ConflictException,
  Logger,
  Inject,
  forwardRef
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import {
  Application,
  ApplicationStatus,
  PaymentStatus
} from './application.model';
import { BaseRepository } from '@apply-goal-backend/database';
import { ApplicationStageService } from './application-stage.service';
import {
  ApplicationDocumentRequirement,
  DocumentStatus
} from './application-document-requirement.model';
import { UniversityClientService } from '../university/university-client.service';
import { Student } from '../student/models/student.model';
import { StudentService } from '../student/student.service';
import { StudentDocumentsService } from '../student/services/student-documents.service';
import { StudentAcademicBackground } from '../student/models/student-academic.model';
import {
  ApplicationStage,
  ApplicationStageStatus
} from './application-stage.model';
import { ApplicationNote } from './application-note.model';
import {
  ApplicationProgressRecord,
  ProgressRecordStatus
} from './application-progress-record.model';
import { RpcException } from '@nestjs/microservices/exceptions';

// Define DTOs locally for now since they're not exported from the shared dto library
export interface CreateApplicationDto {
  studentId: string;
  universityId: number;
  universityCountryId: number;
  universityCountryCampus: number;
  programId: number;
  intakeId: number;
  courseId: number;
  note?: string;
  status?: ApplicationStatus;
  paymentStatus?: PaymentStatus;
  applicationType?: string;
  // Enhanced fields
  applicationId?: string;
  currentStage?: string;
  overallProgress?: number;
  totalAmount?: number;
  paidAmount?: number;
  refundAmount?: number;
  deliveryMethod?: string;
  submissionDate?: Date;
  deadlineDate?: Date;
  // Payment-related fields
  paymentInvoiceUrl?: string;
  paymentMethod?: string;
  // ApplyGoal acceptance fields
  applicationAcceptedByApplyGoal?: boolean;
  applicationAcceptedByApplyGoalAt?: Date;
  applicationAcceptedByApplyGoalUserId?: number;
  // AP approval fields
  isApApproved?: boolean | null;
  apRejectionNotes?: string;
  // Immigration document fields
  studentI20Url?: string;
  // Document status fields
  studentI20UrlStatus?: string;
  i94UrlStatus?: string;
  i797cUrlStatus?: string;
  // Created by field
  createdBy?: number | null;
}

export interface UpdateApplicationDto {
  id?: number | bigint; // Add ID field to match proto message
  studentId?: string;
  universityId?: number;
  universityCountryId?: number;
  universityCountryCampus?: number;
  programId?: number;
  intakeId?: number;
  courseId?: number;
  note?: string;
  status?: ApplicationStatus;
  paymentStatus?: PaymentStatus;
  applicationType?: string;
  // Enhanced fields
  applicationId?: string;
  currentStage?: string;
  overallProgress?: number;
  totalAmount?: number;
  paidAmount?: number;
  refundAmount?: number;
  deliveryMethod?: string;
  submissionDate?: Date | string;
  deadlineDate?: Date | string;
  // Payment-related fields
  paymentInvoiceUrl?: string;
  paymentMethod?: string;
  // ApplyGoal acceptance fields
  applicationAcceptedByApplyGoal?: boolean;
  applicationAcceptedByApplyGoalAt?: Date | string;
  applicationAcceptedByApplyGoalUserId?: number;
  // Document generation flags
  isCalGenerated?: boolean;
  calUrl?: string;
  isAdmGenerated?: boolean;
  admUrl?: string;
  isApGenerated?: boolean;
  apUrl?: string;
  // Immigration document fields
  savisId?: number;
  i20Url?: string;
  studentI20Url?: string;
  i94Url?: string;
  i797cUrl?: string;
  // AP approval fields
  isApApproved?: boolean | null;
  apRejectionNotes?: string;
  // Document status fields
  studentI20UrlStatus?: string;
  i94UrlStatus?: string;
  i797cUrlStatus?: string;
  // Created by field
  createdBy?: number | null;
}

@Injectable()
export class ApplicationService {
  private applicationRepository: BaseRepository<Application>;
  private readonly logger = new Logger(ApplicationService.name);

  constructor(
    @InjectModel(Application)
    private applicationModel: typeof Application,
    @InjectModel(ApplicationDocumentRequirement)
    private applicationDocumentRequirementModel: typeof ApplicationDocumentRequirement,

    @InjectModel(ApplicationStage)
    private applicationStageModel: typeof ApplicationStage,
    @InjectModel(ApplicationNote)
    private applicationNoteModel: typeof ApplicationNote,
    @InjectModel(ApplicationProgressRecord)
    private applicationProgressRecordModel: typeof ApplicationProgressRecord,
    @InjectModel(Student)
    private studentModel: typeof Student,
    private readonly applicationStageService: ApplicationStageService,
    private readonly universityClientService: UniversityClientService,
    // Add this line to inject StudentAcademicBackground model
    @InjectModel(StudentAcademicBackground)
    private studentAcademicModel: typeof StudentAcademicBackground,
    // @Inject(forwardRef(() => StudentService))
    // private readonly studentService: StudentService,
    @Inject(forwardRef(() => StudentDocumentsService))
    private readonly studentDocumentsService: StudentDocumentsService
  ) {
    this.applicationRepository = new BaseRepository<Application>(
      applicationModel
    );

    // Validate requirements description on service initialization
    if (!this.validateRequirementsDescription(this.requirementsDescription)) {
      this.logger.error(
        'Invalid requirements description configuration detected'
      );
      throw new Error('Invalid requirements description configuration');
    }

    this.logger.log('Requirements description validation passed');
  }
  async createApplication(
    createApplicationDto: CreateApplicationDto
  ): Promise<Application> {
    try {
      this.logger.log(
        'Incoming create data:',
        this.safeStringify(createApplicationDto)
      );

      // Helper function to safely convert dates
      const safeDate = (dateInput: any): Date | null => {
        if (
          !dateInput ||
          dateInput === '' ||
          dateInput === null ||
          dateInput === undefined
        ) {
          return null;
        }

        if (typeof dateInput === 'string') {
          const date = new Date(dateInput);
          return isNaN(date.getTime()) ? null : date;
        }

        if (dateInput instanceof Date) {
          return isNaN(dateInput.getTime()) ? null : dateInput;
        }

        return null;
      };

      const application = await this.applicationRepository.create({
        ...createApplicationDto,
        // Date fields - properly validated
        appliedAt: new Date(),
        updatedAt: new Date(),
        deadlineDate: safeDate(createApplicationDto.deadlineDate),
        submissionDate: safeDate(createApplicationDto.submissionDate),
        applicationAcceptedByApplyGoalAt: safeDate(
          createApplicationDto.applicationAcceptedByApplyGoalAt
        ),

        // Default values with fallbacks
        status: ApplicationStatus.COLLECTING_DOCUMENTS,
        currentStage:
          createApplicationDto.currentStage || 'collecting_documents',
        overallProgress: createApplicationDto.overallProgress || 0,
        paidAmount: createApplicationDto.paidAmount || 0,
        refundAmount: createApplicationDto.refundAmount || 0,
        deliveryMethod: createApplicationDto.deliveryMethod || 'online',

        // Handle other potential empty strings
        applicationId: createApplicationDto.applicationId || null,
        paymentInvoiceUrl: createApplicationDto.paymentInvoiceUrl || null,
        paymentMethod: createApplicationDto.paymentMethod || null,
        note: createApplicationDto.note || '',

        // Ensure boolean fields
        applicationAcceptedByApplyGoal:
          createApplicationDto.applicationAcceptedByApplyGoal || false,

        // Handle numeric fields that might be 0
        applicationAcceptedByApplyGoalUserId:
          createApplicationDto.applicationAcceptedByApplyGoalUserId || null,

        // Handle createdBy field - store application officer ID if provided, otherwise null
        createdBy: createApplicationDto.createdBy || null
      });

      this.logger.log(
        `Application created successfully with ID: ${application.id}`
      );

      // Initialize application stages
      // await this.applicationStageService.initializeApplicationStages(Number(application.id));

      // Initialize document requirements
      await this.initializeDocumentRequirements(
        application,
        createApplicationDto
      );

      this.logger.log(
        `Application stages and document requirements initialized for application: ${application.id}`
      );

      return application;
    } catch (error) {
      this.logger.error(`Failed to create application: ${error.message}`);
      this.logger.error('Error stack:', error.stack);

      // Log the specific data that caused the issue for debugging
      this.logger.error('Problematic data:', {
        submissionDate: createApplicationDto.submissionDate,
        deadlineDate: createApplicationDto.deadlineDate,
        applicationAcceptedByApplyGoalAt:
          createApplicationDto.applicationAcceptedByApplyGoalAt
      });

      throw new ConflictException(
        `Failed to create application: ${error.message}`
      );
    }
  }

  async findOne(id: number | bigint): Promise<Application> {
    this.logger.log(`Finding application with ID: ${id}, Type: ${typeof id}`);

    const application = await this.applicationRepository.findById(id);

    this.logger.log(`findById result: ${application ? 'Found' : 'Not found'}`);
    if (application) {
      this.logger.log(
        `Application ID from database: ${
          application.id
        }, Type: ${typeof application.id}`
      );
    }

    if (!application) {
      throw new NotFoundException(`Application with ID ${id} not found`);
    }

    return application;
  }

  /**
   * Find application with all related data needed for CAL document generation
   */
  async findOneWithRelatedData(id: number | bigint): Promise<any> {
    this.logger.log(`Finding application with related data for ID: ${id}`);

    // First, get the application
    const application = await this.applicationRepository.findById(id);
    this.logger.log(`Application found: ${this.safeStringify(application)}`);

    if (!application) {
      throw new NotFoundException(`Application with ID ${id} not found`);
    }

    const applicaitonRequirementsWithGrouping =
      await this.getApplicationRequirementsWithGrouping(id);

    // Fetch related data separately
    const relatedData: any = {
      ...application.toJSON(),
      student: null,
      studentPersonalInfo: null,
      applicationRequirement: applicaitonRequirementsWithGrouping,
      course: null,
      program: null,
      intake: null,
      campus: null,
      university: null,
      fees: []
    };

    try {
      // Fetch student data
      const studentModel = this.applicationModel.sequelize.models.Student;
      if (studentModel) {
        const student = await studentModel.findOne({
          where: { studentId: application.studentId }
        });
        if (student) {
          relatedData.student = student.toJSON();

          // Fetch student personal information
          const studentPersonalInfoModel =
            this.applicationModel.sequelize.models.StudentPersonalInfo;
          if (studentPersonalInfoModel) {
            const personalInfo = await studentPersonalInfoModel.findOne({
              where: { studentId: (student as any).id }
            });
            if (personalInfo) {
              relatedData.studentPersonalInfo = personalInfo.toJSON();
            }
          }
        }
      }

      // Fetch comprehensive university data using the new comprehensive API
      try {
        if (
          application.universityId &&
          application.universityCountryCampus &&
          application.programId &&
          application.intakeId &&
          application.courseId
        ) {
          this.logger.log(
            'Fetching comprehensive university data for application'
          );

          const comprehensiveRequest = {
            universityId: application.universityId,
            campusId: application.universityCountryCampus,
            programId: application.programId,
            intakeId: application.intakeId,
            courseId: application.courseId
          };

          const comprehensiveResponse =
            await this.universityClientService.getComprehensiveUniversityInformation(
              comprehensiveRequest
            );
          this.logger.log(
            'Comprehensive university data response:',
            comprehensiveResponse
          );

          if (comprehensiveResponse && (comprehensiveResponse as any).data) {
            const data = (comprehensiveResponse as any).data;

            relatedData.university = data.university || null;
            relatedData.campus = data.campus || null;
            relatedData.program = data.programLevel || null;
            relatedData.intake = data.intake || null;
            relatedData.course = data.course || null;

            // Filter fees to match the intake name
            let filteredFee = null;
            if (
              data.fees &&
              data.fees.length > 0 &&
              data.intake &&
              data.intake.name
            ) {
              const intakeName = data.intake.name; // e.g., "Fall 1"

              // Find fee that contains the intake name in its title
              const matchingFee = data.fees.find(
                (fee: any) =>
                  fee.feeTitle &&
                  fee.feeTitle.toLowerCase().includes(intakeName.toLowerCase())
              );

              if (matchingFee) {
                filteredFee = matchingFee;
                this.logger.log(
                  `Found matching fee for intake "${intakeName}": ${matchingFee.feeTitle}`
                );
              } else {
                this.logger.warn(
                  `No matching fee found for intake "${intakeName}"`
                );
              }
            }

            relatedData.fees = filteredFee ? [filteredFee] : [];

            this.logger.log(
              'Successfully fetched comprehensive university data'
            );
          }
        }
      } catch (error) {
        this.logger.warn(
          `Error fetching comprehensive university data: ${error.message}`
        );
      }
    } catch (error) {
      this.logger.warn(`Error fetching related data: ${error.message}`);
      // Continue with application data even if related data fails
    }

    this.logger.log(`Found application with related data for ID: ${id}`);
    return relatedData;
  }

  async findByStudentId(studentId: string): Promise<Application[]> {
    const applications = await this.applicationRepository.findAll({
      where: { studentId }
    });

    return applications;
  }

  async findByCreatedBy(createdBy: number): Promise<Application[]> {
    this.logger.log(`Finding applications created by officer ID: ${createdBy}`);
    
    const applications = await this.applicationRepository.findAll({
      where: { createdBy }
    });

    this.logger.log(`Found ${applications.length} applications created by officer ${createdBy}`);
    return applications;
  }

  async update(
    id: number | bigint,
    updateApplicationDto: UpdateApplicationDto,
    createdBy?: number
  ): Promise<Application> {
    this.logger.log(`Updating application with ID: ${id}`);

    // Validate input
    if (!updateApplicationDto || typeof updateApplicationDto !== 'object') {
      throw new Error('Invalid update data provided');
    }

    // Find existing application to ensure it exists
    const existingApplication = await this.findOne(Number(id));
    if (!existingApplication) {
      throw new Error(`Application with ID ${id} not found`);
    }

    // Prepare update data - only include fields that are actually provided
    const updateData: any = {};

    // Add updatedAt timestamp
    updateData.updatedAt = new Date();

    // Process each field from the DTO
    Object.keys(updateApplicationDto).forEach((key) => {
      const value = (updateApplicationDto as any)[key];

      // ✅ Allow null values for specific AP approval fields
      const fieldsThatAllowNull = [
        'submissionDate',
        'isApApproved',
        'apRejectionNotes'
      ];

      // Include fields that have actual values OR are explicitly null for allowed fields
      if (
        value !== undefined &&
        (value !== null || fieldsThatAllowNull.includes(key))
      ) {
        // Handle date fields - convert strings to Date objects
        if (
          [
            'submissionDate',
            'deadlineDate',
            'applicationAcceptedByApplyGoalAt'
          ].includes(key)
        ) {
          if (value === null) {
            updateData[key] = null;
          } else if (typeof value === 'string') {
            updateData[key] = new Date(value);
          } else if (value instanceof Date) {
            updateData[key] = value;
          }
        } else {
          // For all other fields, include the value as-is (including null for allowed fields)
          updateData[key] = value;
        }
      }
    });

    this.logger.log(`Update data prepared:`, updateData);

    try {
      this.logger.log(`Starting update process for application ${id}`);
      this.logger.log(`Update data:`, this.safeStringify(updateData));

      // Use the BaseRepository updateById method with proper type handling
      const [affectedCount] = await this.applicationRepository.updateById(
        Number(id),
        updateData
      );

      this.logger.log(`Update completed. Affected rows: ${affectedCount}`);

      if (affectedCount === 0) {
        throw new Error('No rows were updated');
      }

      // Return the updated application
      const updatedApplication = await this.findOne(Number(id));
      if (!updatedApplication) {
        throw new Error('Failed to retrieve updated application');
      }

      // Track i20Url updates
      if (
        updateData.i20Url &&
        updateData.i20Url !== existingApplication.i20Url
      ) {
        try {
          await this.trackApplicationProgress(
            Number(id),
            'i20_document_upload',
            'I-20 Document Uploaded',
            `I-20 document has been uploaded and updated for application ${
              updatedApplication.applicationId || id
            }`,
            {
              status: 'completed',
              createdBy: createdBy || 1,
              proofLinks: [updateData.i20Url],
              applicationStage: updatedApplication.currentStage,
              applicationStep: 'document_upload'
            }
          );
        } catch (trackingError) {
          this.logger.warn(
            `Failed to track i20Url update: ${trackingError.message}`
          );
          // Don't fail the main update if tracking fails
        }
      }

      // Track calUrl updates
      if (
        updateData.calUrl &&
        updateData.calUrl !== existingApplication.calUrl
      ) {
        try {
          await this.trackApplicationProgress(
            Number(id),
            'cal_document_generated',
            'CAL Document Generated',
            `CAL document has been successfully generated for application ${
              updatedApplication.applicationId || id
            }`,
            {
              status: 'completed',
              createdBy: createdBy || 1,
              proofLinks: [updateData.calUrl],
              applicationStage: updatedApplication.currentStage,
              applicationStep: 'document_generation'
            }
          );
        } catch (trackingError) {
          this.logger.warn(
            `Failed to track calUrl update: ${trackingError.message}`
          );
        }
      }

      // Track admUrl updates
      if (
        updateData.admUrl &&
        updateData.admUrl !== existingApplication.admUrl
      ) {
        try {
          await this.trackApplicationProgress(
            Number(id),
            'adm_document_generated',
            'ADM Document Generated',
            `ADM document has been successfully generated for application ${
              updatedApplication.applicationId || id
            }`,
            {
              status: 'completed',
              createdBy: createdBy || 1,
              proofLinks: [updateData.admUrl],
              applicationStage: updatedApplication.currentStage,
              applicationStep: 'document_generation'
            }
          );
        } catch (trackingError) {
          this.logger.warn(
            `Failed to track admUrl update: ${trackingError.message}`
          );
        }
      }

      // Track apUrl updates
      if (updateData.apUrl && updateData.apUrl !== existingApplication.apUrl) {
        try {
          await this.trackApplicationProgress(
            Number(id),
            'ap_document_generated',
            'AP Document Generated',
            `AP document has been successfully generated for application ${
              updatedApplication.applicationId || id
            }`,
            {
              status: 'completed',
              createdBy: createdBy || 1,
              proofLinks: [updateData.apUrl],
              applicationStage: updatedApplication.currentStage,
              applicationStep: 'document_generation'
            }
          );
        } catch (trackingError) {
          this.logger.warn(
            `Failed to track apUrl update: ${trackingError.message}`
          );
        }
      }

      this.logger.log(
        `Application updated successfully. ID: ${id}, Affected rows: ${affectedCount}`
      );
      return updatedApplication;
    } catch (error) {
      this.logger.error(`Error updating application ${id}:`, error);
      this.logger.error(`Error stack:`, error.stack);
      throw new Error(`Failed to update application: ${error.message}`);
    }
  }

  async remove(id: number | bigint): Promise<void> {
    const application = await this.findOne(Number(id));
    await this.applicationRepository.delete(Number(id));
  }

  async findAll({
    page = 1,
    limit = 10,
    studentId,
    status,
    paymentStatus,
    applicationType,
    currentStage,
    applicationId,
    universityId,
    programId,
    intakeId,
    courseId,
    campusId,
    sortBy = 'createdAt',
    sortOrder = 'desc'
  }: {
    page?: number;
    limit?: number;
    studentId?: string;
    status?: ApplicationStatus;
    paymentStatus?: PaymentStatus;
    applicationType?: string;
    currentStage?: string;
    applicationId?: string;
    universityId?: number;
    programId?: number;
    intakeId?: number;
    courseId?: number;
    campusId?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }): Promise<{ rows: Application[]; count: number }> {
    const where: any = {};

    if (studentId) {
      where.studentId = studentId;
    }

    if (status) {
      where.status = status;
    }

    if (paymentStatus) {
      where.paymentStatus = paymentStatus;
    }

    if (applicationType) {
      where.applicationType = applicationType;
    }

    if (currentStage) {
      where.currentStage = currentStage;
    }

    if (applicationId) {
      where.applicationId = applicationId;
    }

    if (universityId) where.universityId = universityId;
    if (programId) where.programId = programId;
    if (intakeId) where.intakeId = intakeId;
    if (courseId) where.courseId = courseId;
    if (campusId) where.universityCountryCampus = campusId;

    const result = await this.applicationRepository.findAndCountAll({
      where,
      limit,
      offset: (page - 1) * limit,
      order: [[sortBy, sortOrder.toUpperCase()]]
    });

    return result;
  }

  /**
   * Find all applications with mapped data (actual names instead of IDs)
   */
  async findAllWithMappedData({
    page = 1,
    limit = 10,
    studentId,
    status,
    paymentStatus,
    applicationType,
    currentStage,
    applicationId,
    universityId,
    programId,
    intakeId,
    courseId,
    campusId,
    sortBy = 'createdAt',
    sortOrder = 'desc'
  }: {
    page?: number;
    limit?: number;
    studentId?: string;
    status?: ApplicationStatus;
    paymentStatus?: PaymentStatus;
    applicationType?: string;
    currentStage?: string;
    applicationId?: string;
    universityId?: number;
    programId?: number;
    intakeId?: number;
    courseId?: number;
    campusId?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }): Promise<{ rows: any[]; count: number }> {
    try {
      // First get the basic application data
      const result = await this.findAll({
        page,
        limit,
        studentId,
        status,
        paymentStatus,
        applicationType,
        currentStage,
        applicationId,
        universityId,
        programId,
        intakeId,
        courseId,
        campusId,
        sortBy,
        sortOrder
      });

      // Map the applications with actual names
      const mappedRows = await Promise.all(
        result.rows.map(async (app) => {
          const mappedApp = {
            id: app.id,
            studentId: app.studentId,
            universityId: app.universityId,
            universityCountryId: app.universityCountryId,
            universityCountryCampus: app.universityCountryCampus,
            programId: app.programId,
            intakeId: app.intakeId,
            courseId: app.courseId,
            note: app.note,
            status: app.status,
            paymentStatus: app.paymentStatus,
            applicationType: app.applicationType,
            // Enhanced fields
            applicationId: app.applicationId,
            currentStage: app.currentStage,
            overallProgress: app.overallProgress,
            totalAmount: app.totalAmount,
            paidAmount: app.paidAmount,
            refundAmount: app.refundAmount,
            deliveryMethod: app.deliveryMethod,
            submissionDate: this.safeDateToISOString(app.submissionDate),
            deadlineDate: this.safeDateToISOString(app.deadlineDate),
            appliedAt: this.safeDateToISOString(app.appliedAt),
            updatedAt: this.safeDateToISOString(app.updatedAt),
            createdAt: this.safeDateToISOString(app.createdAt),
            // ApplyGoal acceptance fields
            applicationAcceptedByApplyGoal: app.applicationAcceptedByApplyGoal,
            applicationAcceptedByApplyGoalAt: this.safeDateToISOString(
              app.applicationAcceptedByApplyGoalAt
            ),
            applicationAcceptedByApplyGoalUserId:
              app.applicationAcceptedByApplyGoalUserId,
            // Immigration document fields
            savisId: app.savisId,
            i20Url: app.i20Url,
            studentI20Url: app.studentI20Url,
            i94Url: app.i94Url,
            i797cUrl: app.i797cUrl,
            // AP approval fields
            isApApproved: app.isApApproved,
            apRejectionNotes: app.apRejectionNotes,
            // Mapped names (will be populated below)
            universityName: '',
            countryName: '',
            campusName: '',
            programLevelName: '',
            intakeName: '',
            courseName: ''
          };

          try {
            // Get university details
            if (app.universityId) {
              const universityResponse =
                await this.universityClientService.getUniversity(
                  app.universityId
                );
              if (
                universityResponse &&
                (universityResponse as any).status === 200
              ) {
                mappedApp.universityName =
                  (universityResponse as any).data?.title || '';
              }
            }

            // Get country details - using the university service structure
            if (app.universityCountryId) {
              // For now, we'll use a placeholder since getCountry method doesn't exist
              mappedApp.countryName = `Country ID: ${app.universityCountryId}`;
            }

            // Get campus details - using the university service structure
            if (app.universityCountryCampus) {
              // For now, we'll use a placeholder since getCampus method doesn't exist
              mappedApp.campusName = `Campus ID: ${app.universityCountryCampus}`;
            }

            // Get program level details - using the university service structure
            if (app.programId) {
              // For now, we'll use a placeholder since getProgramLevel method doesn't exist
              mappedApp.programLevelName = `Program Level ID: ${app.programId}`;
            }

            // Get intake details - using the university service structure
            if (app.intakeId) {
              // For now, we'll use a placeholder since getIntake method doesn't exist
              mappedApp.intakeName = `Intake ID: ${app.intakeId}`;
            }

            // Get course details
            if (app.courseId) {
              const courseResponse =
                await this.universityClientService.getCourse(app.courseId);
              if (courseResponse && (courseResponse as any).status === 200) {
                mappedApp.courseName =
                  (courseResponse as any).data?.title || '';
              }
            }
          } catch (error) {
            this.logger.warn(
              `Error fetching mapped data for application ${app.id}: ${error.message}`
            );
            // Continue with empty names if mapping fails
          }

          return mappedApp;
        })
      );

      return {
        rows: mappedRows,
        count: result.count
      };
    } catch (error) {
      this.logger.error(`Error in findAllWithMappedData: ${error.message}`);
      throw error;
    }
  }

  /**
   * Safe date to ISO string conversion
   */
  private safeDateToISOString(date: any): string | null {
    if (!date) {
      return null;
    }

    // If it's already a string, return as is
    if (typeof date === 'string') {
      return date;
    }

    // If it's a Date object, convert to ISO string
    if (date instanceof Date) {
      return date.toISOString();
    }

    // If it's a number (timestamp), convert to Date then ISO string
    if (typeof date === 'number') {
      return new Date(date).toISOString();
    }

    // For any other type, try to convert to Date
    try {
      const dateObj = new Date(date);
      if (!isNaN(dateObj.getTime())) {
        return dateObj.toISOString();
      }
    } catch (error) {
      this.logger.warn(`Failed to convert date: ${date}`);
    }

    return null;
  }

  async createApplicationRequirements(
    applicationId: number | bigint,
    items: Array<{
      documentTitle: string;
      documentName: string;
      url?: string;
      documentDescription?: string;
      isRequired?: boolean;
      allowedFormats?: string[];
      requiredByDate?: string;
      isNew?: boolean;
    }>,
    studentId?: string
  ) {
    // Check application existence
    const appStringId = applicationId.toString();
    const application = await this.applicationRepository.findById(appStringId);
    if (!application) {
      return {
        status: 404,
        message: `Application with ID ${applicationId} not found`,
        data: []
      };
    }

    // Validate optional studentId
    if (
      studentId &&
      application.studentId &&
      studentId !== application.studentId
    ) {
      return {
        status: 400,
        message: `Provided studentId (${studentId}) does not match application's studentId (${application.studentId})`,
        data: []
      };
    }

    const effectiveStudentId = studentId || application.studentId || null;

    const requirementsToCreate = items.map((item) => ({
      applicationId,
      studentId: effectiveStudentId,
      documentTitle: item.documentTitle,
      documentName: item.documentName,
      url: item.url || null,
      documentDescription: item.documentDescription || '',
      isRequired: item.isRequired ?? true,
      allowedFormats:
        item.allowedFormats && item.allowedFormats.length > 0
          ? item.allowedFormats
          : ['pdf', 'jpg', 'png'],
      documentStatus: 'requested',
      requiredByDate: item.requiredByDate
        ? new Date(item.requiredByDate)
        : null,
      isNew: item.isNew ?? true // Set to true when admin uploads new requirements, or use provided value
    }));

    const created = await this.applicationDocumentRequirementModel.bulkCreate(
      requirementsToCreate
    );

    return {
      status: 200,
      message: 'Application requirements created successfully',
      data: created.map((req) => ({
        id: req.id,
        applicationId: req.applicationId,
        studentId: req.studentId,
        documentTitle: req.documentTitle,
        documentName: req.documentName,
        url: req.url,
        documentDescription: req.documentDescription,
        isRequired: req.isRequired,
        allowedFormats: req.allowedFormats || [],
        documentStatus: req.documentStatus,
        requiredByDate: req.requiredByDate
          ? new Date(req.requiredByDate).toISOString()
          : null,
        uploadedAt: req.uploadedAt ? req.uploadedAt.toISOString() : null,
        verifiedAt: req.verifiedAt ? req.verifiedAt.toISOString() : null,
        verifiedBy: req.verifiedBy || null,
        acceptedByApplyGoal: this.convertToGrpcBoolValue(
          req.acceptedByApplyGoal
        ),
        acceptedByApplyGoalUserId: req.acceptedByApplyGoalUserId || null,
        acceptedByUniversity: req.acceptedByUniversity || false,
        acceptedByUniversityUserId: req.acceptedByUniversityUserId || null,
        rejectionReason: req.rejectionReason || null,
        isNew: req.isNew || false,
        createdAt: req.createdAt.toISOString(),
        updatedAt: req.updatedAt.toISOString()
      }))
    };
  }

  async getApplicationProgress(id: number | bigint): Promise<{
    application: Application;
    progress: {
      totalStages: number;
      completedStages: number;
      overallProgress: number;
      currentStage: string;
      stages: any[];
    };
  }> {
    const application = await this.findOne(id);
    const progress = await this.applicationStageService.getApplicationProgress(
      Number(application.id)
    );

    return {
      application,
      progress
    };
  }
  async getApplicationRequirements(applicationId: number | bigint) {
    try {
      this.logger.log(
        `Getting application requirements and stages for application: ${applicationId}`
      );

      // Get application requirements
      const requirements =
        await this.applicationDocumentRequirementModel.findAll({
          where: { applicationId },
          order: [['createdAt', 'ASC']]
        });

      // Get application stages sorted by stageOrder
      const stages = await this.applicationStageModel.findAll({
        where: { applicationId },
        order: [['stageOrder', 'ASC']]
      });

      console.log('+++++++++++++++++++++++=', requirements);
      console.log('Application stages:', stages);

      const applicationRequirements = requirements.map((req) => ({
        id: req.id,
        applicationId: req.applicationId,
        documentTitle: req.documentTitle,
        documentName: req.documentName,
        url: req.url,
        documentDescription: req.documentDescription,
        isRequired: req.isRequired,
        allowedFormats: req.allowedFormats || [],
        documentStatus: req.documentStatus,
        requiredByDate: req.requiredByDate
          ? new Date(req.requiredByDate).toISOString()
          : null,
        uploadedAt: req.uploadedAt ? req.uploadedAt.toISOString() : null,
        verifiedAt: req.verifiedAt ? req.verifiedAt.toISOString() : null,
        verifiedBy: req.verifiedBy || null,
        acceptedByApplyGoal: this.convertToGrpcBoolValue(
          req.acceptedByApplyGoal
        ),
        acceptedByApplyGoalUserId: req.acceptedByApplyGoalUserId || null,
        acceptedByUniversity: req.acceptedByUniversity || false,
        acceptedByUniversityUserId: req.acceptedByUniversityUserId || null,
        rejectionReason: req.rejectionReason || null,
        isNew: req.isNew || false,
        createdAt: req.createdAt.toISOString(),
        updatedAt: req.updatedAt.toISOString()
      }));

      const applicationStages = stages.map((stage) => ({
        id: stage.id,
        applicationId: stage.applicationId,
        stageName: stage.stageName,
        stageOrder: stage.stageOrder,
        status: stage.status,
        progressPercentage: stage.progressPercentage,
        startDate: stage.startDate ? stage.startDate.toISOString() : null,
        completedDate: stage.completedDate
          ? stage.completedDate.toISOString()
          : null,
        requirementsMet: stage.requirementsMet,
        totalRequirements: stage.totalRequirements,
        createdAt: stage.createdAt.toISOString(),
        updatedAt: stage.updatedAt.toISOString()
      }));

      return {
        status: 200,
        message: 'Application requirements and stages retrieved successfully',
        data: {
          applicationRequirements,
          applicationStages
        },
        error: null
      };
    } catch (error) {
      this.logger.error(
        `Failed to get application requirements and stages: ${error.message}`
      );
      throw error;
    }
  }
  private async getApplicationRequirementsWithGrouping(
    applicationId: number | bigint
  ): Promise<Record<string, any[]>> {
    try {
      this.logger.log(
        `Fetching application requirements for application: ${applicationId}`
      );

      // Get application requirements
      const requirements =
        await this.applicationDocumentRequirementModel.findAll({
          where: { applicationId },
          order: [['createdAt', 'ASC']]
        });

      this.logger.log(`Found ${requirements.length} application requirements`);

      // ✅ NEW: Group requirements by documentTitle
      const groupedRequirements = requirements.reduce((groups, req) => {
        const documentTitle = req.documentTitle || 'other';

        if (!groups[documentTitle]) {
          groups[documentTitle] = [];
        }

        // Transform requirement data
        const requirementData = {
          id: req.id,
          applicationId: req.applicationId,
          studentId: req.studentId,
          documentTitle: req.documentTitle,
          documentName: req.documentName,
          url: req.url,
          documentDescription: req.documentDescription,
          isRequired: req.isRequired,
          allowedFormats: req.allowedFormats || [],
          documentStatus: req.documentStatus,
          requiredByDate: req.requiredByDate
            ? req.requiredByDate.toISOString()
            : null,
          uploadedAt: req.uploadedAt ? req.uploadedAt.toISOString() : null,
          verifiedAt: req.verifiedAt ? req.verifiedAt.toISOString() : null,
          verifiedBy: req.verifiedBy || null,
          acceptedByApplyGoal: req.acceptedByApplyGoal,
          acceptedByApplyGoalUserId: req.acceptedByApplyGoalUserId || null,
          acceptedByUniversity: req.acceptedByUniversity || false,
          acceptedByUniversityUserId: req.acceptedByUniversityUserId || null,
          rejectionReason: req.rejectionReason || null,
          createdAt: req.createdAt.toISOString(),
          updatedAt: req.updatedAt.toISOString()
        };

        groups[documentTitle].push(requirementData);
        return groups;
      }, {} as Record<string, any[]>);

      this.logger.log(
        `Grouped requirements by documentTitle:`,
        Object.keys(groupedRequirements)
      );
      this.logger.debug(
        'Docuemnt Requirmeent  final  list ============================>',
        groupedRequirements
      );

      return groupedRequirements;
    } catch (error) {
      this.logger.error(
        `Error fetching application requirements: ${error.message}`
      );
      return {};
    }
  }
  async updateApplicationStage(
    applicationId: number | bigint,
    stageId: number | bigint,
    status: string
  ) {
    try {
      this.logger.log(
        `Updating application stage ${stageId} for application ${applicationId} to status: ${status}`
      );

      // Validate status
      const validStatuses = ['pending', 'in_progress', 'completed', 'failed'];
      if (!validStatuses.includes(status)) {
        return {
          status: 400,
          message: `Invalid status. Must be one of: ${validStatuses.join(
            ', '
          )}`,
          data: null,
          error: { details: 'Invalid status value' }
        };
      }

      // Find the stage
      const stage = await this.applicationStageModel.findOne({
        where: {
          id: stageId,
          applicationId: applicationId
        }
      });

      if (!stage) {
        return {
          status: 404,
          message: 'Application stage not found',
          data: null,
          error: { details: 'Stage not found' }
        };
      }

      // Update the stage
      const updateData: any = { status };

      // If status is completed, set completedDate
      if (status === 'completed') {
        updateData.completedDate = new Date();
        updateData.progressPercentage = 100;
      }

      // If status is in_progress, set startDate if not already set
      if (status === 'in_progress' && !stage.startDate) {
        updateData.startDate = new Date();
      }

      await stage.update(updateData);

      // Return the updated stage
      return {
        status: 200,
        message: 'Application stage updated successfully',
        data: {
          id: stage.id,
          applicationId: stage.applicationId,
          stageName: stage.stageName,
          stageOrder: stage.stageOrder,
          status: stage.status,
          progressPercentage: stage.progressPercentage,
          startDate: stage.startDate ? stage.startDate.toISOString() : null,
          completedDate: stage.completedDate
            ? stage.completedDate.toISOString()
            : null,
          requirementsMet: stage.requirementsMet,
          totalRequirements: stage.totalRequirements,
          createdAt: stage.createdAt.toISOString(),
          updatedAt: stage.updatedAt.toISOString()
        },
        error: null
      };
    } catch (error) {
      this.logger.error(`Failed to update application stage: ${error.message}`);
      return {
        status: 500,
        message: 'Failed to update application stage',
        data: null,
        error: { details: error.message }
      };
    }
  }

  async createApplicationNote(
    applicationId: number | bigint,
    noteData: {
      noteType: string;
      title?: string;
      content: string;
      createdBy: number;
      isPrivate?: boolean;
      userInfo?: any;
      replyId?: number | null;
    }
  ) {
    try {
      this.logger.log(
        `Creating application note for application: ${applicationId}`
      );

      // Validate noteType
      const validNoteTypes = ['internal', 'student', 'university', 'applygoal'];
      if (!validNoteTypes.includes(noteData.noteType)) {
        return {
          status: 400,
          message: `Invalid noteType. Must be one of: ${validNoteTypes.join(
            ', '
          )}`,
          data: null,
          error: { details: 'Invalid noteType value' }
        };
      }

      // Check if application exists
      const application = await this.applicationModel.findByPk(applicationId);
      if (!application) {
        return {
          status: 404,
          message: 'Application not found',
          data: null,
          error: { details: 'Application not found' }
        };
      }

      // If replyId is provided, validate that the parent note exists
      if (noteData.replyId) {
        const parentNote = await this.applicationNoteModel.findByPk(
          noteData.replyId
        );
        if (parentNote) {
          this.logger.log(
            `Found parent note for reply: ${JSON.stringify(parentNote)}`
          );
        }
        if (!parentNote) {
          return {
            status: 404,
            message: 'Parent note not found',
            data: null,
            error: {
              details: 'Parent note with the specified replyId does not exist'
            }
          };
        }

        // Ensure the parent note belongs to the same application
        if (Number(parentNote.applicationId) !== Number(applicationId)) {
          return {
            status: 400,
            message: 'Parent note belongs to a different application',
            data: null,
            error: {
              details:
                'Reply can only be created for notes in the same application'
            }
          };
        }
      }

      // Create the note
      const note = await this.applicationNoteModel.create({
        applicationId: applicationId,
        noteType: noteData.noteType,
        title: noteData.title || '',
        content: noteData.content,
        createdBy: noteData.createdBy,
        isPrivate: noteData.isPrivate || false,
        userInfo: noteData.userInfo || null,
        replyId: noteData.replyId || null
      });

      // Note: Progress record tracking removed for note creation
      // Only tracking ADM/AP/CAL generation and I-20 upload

      return {
        status: 201,
        message: 'Application note created successfully',
        data: {
          id: note.id,
          applicationId: note.applicationId,
          noteType: note.noteType,
          title: note.title,
          content: note.content,
          createdBy: note.createdBy,
          isPrivate: note.isPrivate,
          userInfo: note.userInfo,
          replyId: note.replyId,
          createdAt: note.createdAt.toISOString(),
          updatedAt: note.updatedAt.toISOString()
        },
        error: null
      };
    } catch (error) {
      this.logger.error(`Failed to create application note: ${error.message}`);
      return {
        status: 500,
        message: 'Failed to create application note',
        data: null,
        error: { details: error.message }
      };
    }
  }

  async getApplicationNotes(applicationId: number | bigint, noteType?: string) {
    try {
      this.logger.log(
        `Getting application notes for application: ${applicationId}${
          noteType ? `, noteType: ${noteType}` : ''
        }`
      );

      // Build where clause
      const whereClause: any = { applicationId };
      if (noteType) {
        // Validate noteType if provided
        const validNoteTypes = ['internal', 'student', 'university'];
        if (!validNoteTypes.includes(noteType)) {
          return {
            status: 400,
            message: `Invalid noteType. Must be one of: ${validNoteTypes.join(
              ', '
            )}`,
            data: null,
            error: { details: 'Invalid noteType value' }
          };
        }
        whereClause.noteType = noteType;
      }

      // Get all notes with their parent note information
      const notes = await this.applicationNoteModel.findAll({
        where: whereClause,
        include: [
          {
            model: this.applicationNoteModel,
            as: 'parentNote',
            required: false,
            attributes: ['id', 'title', 'content', 'createdBy', 'createdAt']
          }
        ],
        order: [['createdAt', 'DESC']]
      });
      this.logger.log('notes from model', JSON.stringify(notes));
      // Format notes and organize them hierarchically
      const formattedNotes = notes.map((note) => ({
        id: note.id,
        applicationId: note.applicationId,
        noteType: note.noteType,
        title: note.title,
        content: note.content,
        createdBy: note.createdBy,
        isPrivate: note.isPrivate,
        userInfo: note.userInfo,
        replyId: note.replyId,
        parentNote: note.parentNote
          ? {
              id: note.parentNote.id,
              title: note.parentNote.title,
              content:
                note.parentNote.content.substring(0, 100) +
                (note.parentNote.content.length > 100 ? '...' : ''),
              createdBy: note.parentNote.createdBy,
              createdAt: note.parentNote.createdAt.toISOString()
            }
          : null,
        createdAt: note.createdAt.toISOString(),
        updatedAt: note.updatedAt.toISOString()
      }));

      return {
        status: 200,
        message: 'Application notes retrieved successfully',
        data: {
          notes: formattedNotes,
          total: formattedNotes.length
        },
        error: null
      };
    } catch (error) {
      this.logger.error(`Failed to get application notes: ${error.message}`);
      return {
        status: 500,
        message: 'Failed to get application notes',
        data: null,
        error: { details: error.message }
      };
    }
  }

  // ==================== APPLICATION PROGRESS RECORDS ====================

  async getApplicationProgressRecords(
    applicationId: number | bigint,
    recordType?: string
  ) {
    try {
      this.logger.log(
        `Getting application progress records for application: ${applicationId}`
      );

      // Build where clause with filters
      const whereClause: any = { applicationId };
      if (recordType) whereClause.recordType = recordType;

      // Get progress records
      const records = await this.applicationProgressRecordModel.findAll({
        where: whereClause,
        order: [
          ['recordDate', 'DESC'],
          ['createdAt', 'DESC']
        ]
      });

      const formattedRecords = records.map((record) => ({
        id: record.id,
        applicationId: record.applicationId,
        recordType: record.recordType,
        title: record.title,
        description: record.description,
        status: record.status,
        recordDate: record.recordDate ? record.recordDate.toISOString() : null,
        amount: record.amount || 0,
        currency: record.currency || 'USD',
        proofLinks: this.extractUrlsFromProofLinks(record.proofLinks),
        attachments: this.extractUrlsFromProofLinks(record.attachments),
        createdBy: record.createdBy,
        createdAt: record.createdAt.toISOString(),
        updatedAt: record.updatedAt.toISOString()
      }));

      return {
        status: 200,
        message: 'Application progress records retrieved successfully',
        data: {
          records: formattedRecords,
          total: formattedRecords.length
        },
        error: null
      };
    } catch (error) {
      this.logger.error(
        `Failed to get application progress records: ${error.message}`
      );
      return {
        status: 500,
        message: 'Failed to get application progress records',
        data: null,
        error: { details: error.message }
      };
    }
  }

  async createApplicationProgressRecord(
    applicationId: number | bigint,
    recordData: {
      recordType: string;
      title: string;
      description: string;
      status?: string;
      recordDate?: Date | string;
      amount?: number;
      currency?: string;
      proofLinks?: any[];
      attachments?: any[];
      createdBy: number;
      applicationStage?: string;
      applicationStep?: string;
    }
  ) {
    try {
      this.logger.log(
        `Creating application progress record for application: ${applicationId}, type: ${recordData.recordType}`
      );

      // Validate status
      const validStatuses = ['completed', 'in_progress', 'failed'];
      const status = recordData.status || 'completed';
      if (!validStatuses.includes(status)) {
        return {
          status: 400,
          message: `Invalid status. Must be one of: ${validStatuses.join(
            ', '
          )}`,
          data: null,
          error: { details: 'Invalid status value' }
        };
      }

      // Check if application exists
      const application = await this.applicationModel.findByPk(applicationId);
      if (!application) {
        return {
          status: 404,
          message: 'Application not found',
          data: null,
          error: { details: 'Application not found' }
        };
      }

      // Parse recordDate
      let recordDate = new Date();
      if (recordData.recordDate) {
        recordDate =
          typeof recordData.recordDate === 'string'
            ? new Date(recordData.recordDate)
            : recordData.recordDate;
      }

      // Create enhanced description with stage/step info
      // let enhancedDescription = recordData.description;
      // if (recordData.applicationStage || recordData.applicationStep) {
      //   const stageInfo = [];
      //   if (recordData.applicationStage)
      //     stageInfo.push(`Stage: ${recordData.applicationStage}`);
      //   if (recordData.applicationStep)
      //     stageInfo.push(`Step: ${recordData.applicationStep}`);
      //   enhancedDescription = `${recordData.description} [${stageInfo.join(
      //     ', '
      //   )}]`;
      // }

      // Format arrays as JSON with metadata
      const proofLinks = this.formatJsonArrayWithMetadata(
        recordData.proofLinks
      );
      const attachments = this.formatJsonArrayWithMetadata(
        recordData.attachments
      );

      // Create the progress record
      const record = await this.applicationProgressRecordModel.create({
        applicationId: applicationId,
        recordType: recordData.recordType,
        title: recordData.title,
        description: recordData.description,
        status: status,
        recordDate: recordDate,
        amount: recordData.amount || null,
        currency: recordData.currency || 'USD',
        proofLinks: proofLinks,
        attachments: attachments,
        createdBy: recordData.createdBy
      });

      return {
        status: 201,
        message: 'Application progress record created successfully',
        data: {
          id: record.id,
          applicationId: record.applicationId,
          recordType: record.recordType,
          title: record.title,
          description: record.description,
          status: record.status,
          recordDate: record.recordDate
            ? record.recordDate.toISOString()
            : null,
          amount: record.amount || 0,
          currency: record.currency,
          proofLinks: this.formatJsonArray(record.proofLinks),
          attachments: this.formatJsonArray(record.attachments),
          createdBy: record.createdBy,
          createdAt: record.createdAt.toISOString(),
          updatedAt: record.updatedAt.toISOString()
        },
        error: null
      };
    } catch (error) {
      this.logger.error(
        `Failed to create application progress record: ${error.message}`
      );
      return {
        status: 500,
        message: 'Failed to create application progress record',
        data: null,
        error: { details: error.message }
      };
    }
  }

  // Universal tracking method - can be called from anywhere
  async trackApplicationProgress(
    applicationId: number | bigint,
    recordType: string,
    title: string,
    description: string,
    options: {
      status?: 'completed' | 'in_progress' | 'failed';
      amount?: number;
      currency?: string;
      proofLinks?: any[];
      attachments?: any[];
      createdBy: number;
      applicationStage?: string;
      applicationStep?: string;
    }
  ) {
    try {
      this.logger.log(
        `Tracking application progress: ${applicationId} - ${recordType} - ${title}`
      );

      return await this.createApplicationProgressRecord(applicationId, {
        recordType,
        title,
        description,
        status: options.status || 'completed',
        recordDate: new Date(),
        amount: options.amount,
        currency: options.currency,
        proofLinks: options.proofLinks,
        attachments: options.attachments,
        createdBy: options.createdBy,
        applicationStage: options.applicationStage,
        applicationStep: options.applicationStep
      });
    } catch (error) {
      this.logger.error(
        `Failed to track application progress: ${error.message}`
      );
      // Don't throw error to avoid breaking the main process
      return {
        status: 500,
        message: 'Failed to track application progress',
        data: null,
        error: { details: error.message }
      };
    }
  }

  // Helper methods
  private formatJsonArray(data: any): any[] {
    if (!data) return [];

    // If it's already an array, return it (this is the normal case for JSONB fields)
    if (Array.isArray(data)) return data;

    // If it's an object, wrap it in an array
    if (typeof data === 'object' && data !== null) {
      return [data];
    }

    // If it's a string, try to parse it as JSON
    if (typeof data === 'string') {
      try {
        const parsed = JSON.parse(data);
        return Array.isArray(parsed) ? parsed : [parsed];
      } catch {
        return [];
      }
    }

    // For any other type, return empty array
    return [];
  }

  private extractUrlsFromProofLinks(data: any): string[] {
    if (!data) return [];

    // If it's already an array of URLs (strings), return it
    if (Array.isArray(data)) {
      return data
        .map((item) => {
          if (typeof item === 'string') {
            return item;
          } else if (typeof item === 'object' && item.url) {
            return item.url;
          }
          return '';
        })
        .filter((url) => url && url.trim() !== '');
    }

    // If it's a single object with url property
    if (typeof data === 'object' && data !== null && data.url) {
      return [data.url];
    }

    // If it's a string, try to parse it as JSON
    if (typeof data === 'string') {
      // Handle empty string
      if (data === '' || data === '[object Object]') {
        return [];
      }

      try {
        const parsed = JSON.parse(data);
        if (Array.isArray(parsed)) {
          return parsed
            .map((item) => {
              if (typeof item === 'string') {
                return item;
              } else if (typeof item === 'object' && item.url) {
                return item.url;
              }
              return '';
            })
            .filter((url) => url && url.trim() !== '');
        } else if (typeof parsed === 'object' && parsed.url) {
          return [parsed.url];
        }
        return [];
      } catch {
        // If it's not JSON, treat it as a single URL string
        return data.trim() !== '' ? [data] : [];
      }
    }

    return [];
  }

  private formatJsonArrayWithMetadata(data: any[]): any[] {
    if (!data || !Array.isArray(data)) return [];
    return data.map((item) => {
      if (typeof item === 'string') {
        return {
          url: item,
          uploadedAt: new Date().toISOString(),
          type: this.getFileType(item)
        };
      }
      return item;
    });
  }

  private getFileType(url: string): string {
    const extension = url.split('.').pop()?.toLowerCase();
    const typeMap: { [key: string]: string } = {
      pdf: 'document',
      doc: 'document',
      docx: 'document',
      jpg: 'image',
      jpeg: 'image',
      png: 'image',
      gif: 'image'
    };
    return typeMap[extension || ''] || 'file';
  }

  // Helper method to convert boolean | null to gRPC BoolValue format
  private convertToGrpcBoolValue(
    value: boolean | null
  ): { value: boolean } | null {
    if (value === null || value === undefined) {
      return null;
    }
    return { value: value };
  }

  async updateDocumentRequirementStatus(
    requirementId: number | bigint,
    updateData: {
      applicationId?: number | bigint;
      acceptedByApplyGoal?: boolean | null;
      acceptedByUniversity?: boolean;
      acceptedByApplyGoalUserId?: number;
      acceptedByUniversityUserId?: number;
      rejectionReason?: string;
      status?: string;
      url?: string;
      isNew?: boolean;
    }
  ) {
    try {
      this.logger.log(
        `Updating document requirement status: ${updateData.isNew}`
      );

      // Convert requirementId to number for Sequelize
      const numericRequirementId =
        typeof requirementId === 'bigint'
          ? Number(requirementId)
          : requirementId;

      const requirement =
        await this.applicationDocumentRequirementModel.findByPk(
          numericRequirementId
        );
      if (!requirement) {
        throw new NotFoundException('Document requirement not found');
      }

      if (
        updateData.applicationId &&
        requirement.applicationId !== updateData.applicationId
      ) {
        throw new NotFoundException(
          'Requirement does not belong to the specified application'
        );
      }

      const updateFields: any = { ...updateData };

      if (updateData.acceptedByApplyGoal !== undefined) {
        updateFields.acceptedByApplyGoalAt = updateData.acceptedByApplyGoal
          ? new Date()
          : null;
      }

      if (updateData.acceptedByUniversity !== undefined) {
        updateFields.acceptedByUniversityAt = updateData.acceptedByUniversity
          ? new Date()
          : null;
      }
      if (updateData.status) {
        updateFields.documentStatus = updateData.status;
      }

      if (updateData.isNew) {
        updateFields.isNew = updateData.isNew;
      }

      await requirement.update(updateFields);

      // ✅ NEW: Update Application model fields for visa documents
      if (updateData.url && requirement.documentName) {
        const documentName = requirement.documentName.toLowerCase();
        const applicationId = requirement.applicationId;

        // Find the application
        const application = await this.applicationRepository.findById(
          applicationId
        );
        if (application) {
          const applicationUpdateFields: any = {};

          // Check for I-20 document
          if (documentName.includes('i-20') || documentName.includes('i20')) {
            applicationUpdateFields.studentI20Url = updateData.url;
            applicationUpdateFields.studentI20UrlStatus =
              updateData.status || 'uploaded';
            this.logger.log(
              `Updated studentI20Url for application ${applicationId}: ${updateData.url}`
            );
          }

          // Check for I-94 document
          if (documentName.includes('i-94') || documentName.includes('i94')) {
            applicationUpdateFields.i94Url = updateData.url;
            applicationUpdateFields.i94UrlStatus =
              updateData.status || 'uploaded';
            this.logger.log(
              `Updated i94Url for application ${applicationId}: ${updateData.url}`
            );
          }

          // Check for I-797C document
          if (
            documentName.includes('i-797c') ||
            documentName.includes('i797c')
          ) {
            applicationUpdateFields.i797cUrl = updateData.url;
            applicationUpdateFields.i797cUrlStatus =
              updateData.status || 'uploaded';
            this.logger.log(
              `Updated i797cUrl for application ${applicationId}: ${updateData.url}`
            );
          }

          // Update application if any visa document fields need updating
          if (Object.keys(applicationUpdateFields).length > 0) {
            await application.update(applicationUpdateFields);
            this.logger.log(
              `Updated application ${applicationId} visa document fields: ${Object.keys(
                applicationUpdateFields
              ).join(', ')}`
            );
          }
        }
      }

      // Note: Progress record tracking removed for requirement updates
      // Only tracking ADM/AP/CAL generation and I-20 upload

      return {
        status: 200,
        message: 'Document requirement status updated successfully',
        data: {
          id: requirement.id,
          applicationId: requirement.applicationId,
          documentTitle: requirement.documentTitle,
          documentName: requirement.documentName,
          url: requirement.url,
          documentDescription: requirement.documentDescription,
          isRequired: requirement.isRequired,
          allowedFormats: requirement.allowedFormats || [],
          documentStatus: requirement.documentStatus,
          requiredByDate: requirement.requiredByDate
            ? new Date(requirement.requiredByDate).toISOString()
            : null,
          uploadedAt: requirement.uploadedAt
            ? requirement.uploadedAt.toISOString()
            : null,
          verifiedAt: requirement.verifiedAt
            ? requirement.verifiedAt.toISOString()
            : null,
          verifiedBy: requirement.verifiedBy || null,
          acceptedByApplyGoal: this.convertToGrpcBoolValue(
            requirement.acceptedByApplyGoal
          ),
          acceptedByApplyGoalUserId:
            requirement.acceptedByApplyGoalUserId || null,
          acceptedByUniversity: requirement.acceptedByUniversity || false,
          acceptedByUniversityUserId:
            requirement.acceptedByUniversityUserId || null,
          rejectionReason: requirement.rejectionReason || null,
          createdAt: requirement.createdAt.toISOString(),
          updatedAt: requirement.updatedAt.toISOString(),
          isNew: requirement.isNew
        },
        error: null
      };
    } catch (error) {
      this.logger.error(
        `Failed to update document requirement status: ${error.message}`
      );
      throw error;
    }
  }

  private async initializeDocumentRequirements(
    application: Application,
    createApplicationDto: CreateApplicationDto
  ) {
    try {
      this.logger.log(
        `Initializing document requirements for application: ${application.id}`
      );

      // ==================== STEP 1: GET COURSE REQUIREMENTS ====================
      let applicationSteps: any[] = [];
      let lastAcademiceLevel: any;

      try {
        const resp = await this.universityClientService.getCourseRequirements({
          courseId: createApplicationDto.courseId,
          programLevelId: createApplicationDto.programId
        });

        console.log(
          '+++++++++++++++++++++++++++++++++++++>CourseInformationRecords+++++++++++++>',
          resp
        );

        if (resp && resp.status === 200 && resp.data) {
          // Extract application steps from course requirements
          // Filter out 'Collecting Documents' step as it's handled separately
          applicationSteps = Array.isArray(resp.data.applicationSteps)
            ? resp.data.applicationSteps.filter(
                (step: any) => step.title !== 'Collecting Documents'
              )
            : [];

          // Get the last academic level required for the course
          lastAcademiceLevel = resp.data.course?.lastAcademic;
        } else {
          this.logger.warn(
            `Failed to get course requirements for courseId: ${createApplicationDto.courseId}`
          );
        }
      } catch (e) {
        this.logger.warn(
          `Error getting course requirements for courseId: ${createApplicationDto.courseId} — proceeding with defaults`
        );
        applicationSteps = [];
      }

      // ==================== STEP 2: INITIALIZE APPLICATION STAGES ====================
      if (applicationSteps.length > 0) {
        try {
          // Initialize stages based on course requirements
          const stages =
            await this.initializeApplicationStagesFromCourseRequirements(
              Number(application.id),
              applicationSteps
            );
          console.log(
            `Initialized ${stages.length} application stages from course requirements`
          );
        } catch (stageError) {
          this.logger.error(
            'Failed to initialize application stages:',
            stageError
          );
          // Continue with document requirements even if stage initialization fails
        }
      } else {
        try {
          await this.applicationStageService.initializeApplicationStages(
            Number(application.id)
          );
          console.log('Initialized default application stages');
        } catch (stageError) {
          this.logger.error(
            'Failed to initialize default application stages:',
            stageError
          );
        }
      }

      // ==================== STEP 3: GET IDENTITY DOCUMENTS ====================
      const identityDocuments = this.getRequiredDocumentsByApplicationType(
        createApplicationDto.applicationType || 'F-1 initial'
      );

      // ==================== STEP 4: PREPARE BASE DEFAULTS ====================
      const requirements: any[] = [];
      const baseDefaults = {
        isRequired: true,
        allowedFormats: ['pdf'] as string[],
        requiredByDate: null as Date | null,
        verifiedAt: null as Date | null,
        verifiedBy: null as number | null,
        acceptedByApplyGoal: null as boolean | null,
        acceptedByApplyGoalUserId: null as number | null,
        acceptedByUniversity: false,
        acceptedByUniversityUserId: null as number | null,
        rejectionReason: null as string | null
      };
      // ==================== STEP 5: ADD PROFILE REQUIREMENTS ====================
      // Add single photo requirement
      requirements.push({
        applicationId: Number(application.id),
        studentId: createApplicationDto.studentId,
        documentTitle: 'identity', // ✅ Set to 'identity'
        documentName: 'Photo', // ✅ Set to 'Photo'
        url: null,
        documentDescription: this.getRequirementsDescription('Photo'),
        documentStatus: DocumentStatus.REQUESTED,
        uploadedAt: null,
        ...baseDefaults,
        allowedFormats: ['jpg', 'jpeg', 'png'] // ✅ Override to only allow image formats
      });

      // ==================== STEP 5: ADD ACADEMIC REQUIREMENTS ====================
      if (lastAcademiceLevel) {
        const academicDocuments =
          this.getRequiredAcademicDocuments(lastAcademiceLevel);

        for (const academicDoc of academicDocuments) {
          // Make 10th grade certificate and transcript optional (not required)
          const isOptionalDocument =
            academicDoc.toLowerCase().includes('10th grade') ||
            academicDoc.toLowerCase().includes('certification of 10th') ||
            academicDoc.toLowerCase().includes('transcript of 10th');

          requirements.push({
            applicationId: Number(application.id),
            studentId: createApplicationDto.studentId,
            documentTitle: 'academic',
            documentName: academicDoc,
            url: null,
            documentDescription: this.getRequirementsDescription(academicDoc),
            documentStatus: DocumentStatus.REQUESTED,
            uploadedAt: null,
            ...baseDefaults,
            isRequired: !isOptionalDocument // Set to false for 10th grade documents, true for others
          });
        }

        this.logger.debug(
          `Added ${academicDocuments.length} academic requirements for lastAcademicLevel: ${lastAcademiceLevel}`
        );
      }

      // ==================== STEP 6: ADD IDENTITY REQUIREMENTS ====================
      for (const identityDoc of identityDocuments) {
        requirements.push({
          applicationId: Number(application.id),
          studentId: createApplicationDto.studentId,
          documentTitle: 'identity',
          documentName: identityDoc,
          url: null,
          documentDescription: this.getRequirementsDescription(identityDoc),
          documentStatus: DocumentStatus.REQUESTED,
          uploadedAt: null,
          ...baseDefaults
        });
      }

      this.logger.debug(
        `Added ${identityDocuments.length} identity requirements for application type: ${createApplicationDto.applicationType}`
      );
      // ==================== STEP 7.5: ADD I-20 REQUIREMENT FOR CHANGE OF STATUS ====================
      // Add optional I-20 requirement specifically for "Change of Status" students
      if (createApplicationDto.applicationType === 'Change of Status') {
        requirements.push({
          applicationId: Number(application.id),
          studentId: createApplicationDto.studentId,
          documentTitle: 'identity',
          documentName: 'I-20',
          url: null,
          documentDescription: this.getRequirementsDescription('I-20'),
          documentStatus: DocumentStatus.REQUESTED,
          uploadedAt: null,
          ...baseDefaults,
          isRequired: false // ✅ Set as optional (not required)
        });

        this.logger.debug(
          'Added optional I-20 requirement for Change of Status student'
        );
      }

      // ==================== STEP 7: ADD PROFICIENCY REQUIREMENT ====================
      requirements.push({
        applicationId: Number(application.id),
        studentId: createApplicationDto.studentId,
        documentTitle: 'proficiency',
        documentName: 'Language Proficiency Certificate',
        url: null,
        documentDescription: this.getRequirementsDescription(
          'Language Proficiency Certificate'
        ),
        documentStatus: DocumentStatus.REQUESTED,
        uploadedAt: null,
        ...baseDefaults
      });

      this.logger.debug('Added single proficiency requirement');

      // ==================== STEP 8: SAVE REQUIREMENTS TO DATABASE ====================
      if (requirements.length > 0) {
        // Check for existing requirements to avoid duplicates
        const existing = await this.applicationDocumentRequirementModel.findAll(
          {
            where: { applicationId: Number(application.id) }
          }
        );

        // Process each requirement (update existing or create new)
        for (const requirement of requirements) {
          const found = existing.find(
            (e: any) =>
              e.documentTitle === requirement.documentTitle &&
              e.documentName === requirement.documentName
          );

          if (found) {
            // Update existing requirement
            await found.update({
              documentStatus: requirement.documentStatus,
              url: requirement.url,
              uploadedAt: requirement.uploadedAt,
              documentDescription: requirement.documentDescription,
              isRequired: requirement.isRequired,
              updatedAt: new Date()
            });
          } else {
            // Create new requirement
            await this.applicationDocumentRequirementModel.create(requirement);
          }
        }

        this.logger.log(
          `Processed ${requirements.length} document requirements for application: ${application.id}`
        );
      } else {
        this.logger.log('No document requirements to create');
      }

      this.logger.log(
        `Document requirements initialized successfully for application: ${application.id}`
      );
    } catch (error: any) {
      this.logger.error(
        `Failed to initialize document requirements: ${error?.message}`,
        error?.stack
      );
    }
  }

  async getStudentAcademic(
    studentId: string
  ): Promise<StudentAcademicBackground> {
    try {
      const student = await this.studentModel.findOne({
        where: { studentId: studentId }
      });
      if (!student) {
        throw new NotFoundException('Student not found');
      }

      const academicBackground = await this.studentAcademicModel.findOne({
        where: { studentId: student.id }
      });

      // if (!academicBackground) {
      //   throw new NotFoundException('Academic background not found');
      // }

      return academicBackground;
    } catch (error) {
      this.logger.error('Error getting student academic background:', error);
      throw error;
    }
  }

  // Add this new method to ApplicationStageService

  // Add this method after the existing initializeApplicationStages method

  async initializeApplicationStagesFromCourseRequirements(
    applicationId: number,
    applicationSteps: any[]
  ): Promise<ApplicationStage[]> {
    try {
      const stages = [];

      // Map application step titles to stage names
      const stageNameMapping: { [key: string]: string } = {
        'Collecting Documents': 'collecting_documents',
        Applied: 'applied',
        'Accepted / Rejected': 'accepted_rejected',
        'CAL Issued': 'cal_issued',
        'I-20 Issued': 'i20_issued',
        'Appointment Confirmation': 'appointment_confirmation',
        'VISA Processing': 'visa_processing',
        'Visa Decision': 'visa_decision',
        Enrollment: 'enrollment'
      };

      // Define which stages should be set to "in_progress" initially
      const inProgressStages = ['collecting_documents', 'applied'];

      for (const step of applicationSteps) {
        // Map the step title to a stage name
        const stepTitle = step.title || step.name;
        if (!stepTitle) {
          this.logger.warn(
            `Step missing title/name property: ${JSON.stringify(step)}`
          );
          continue;
        }
        const stageName =
          stageNameMapping[stepTitle] ||
          stepTitle.toLowerCase().replace(/\s+/g, '_');
        this.logger.log(`Mapped stage name: ${stageName}`);

        // Determine initial status
        let initialStatus = ApplicationStageStatus.PENDING;
        let progressPercentage = 0;
        let startDate: Date | null = null;

        if (inProgressStages.includes(stageName)) {
          initialStatus = ApplicationStageStatus.COMPLETED;
          progressPercentage = 10; // Start with 10% progress for in-progress stages
          startDate = new Date();
        }
        this.logger.log(
          `Initial status: ${initialStatus}, progressPercentage: ${progressPercentage}, startDate: ${startDate}`
        );

        // Create the stage directly using the ApplicationStage model
        const stage = await this.applicationStageModel.create({
          applicationId: applicationId,
          stageName: stageName,
          stageOrder: step.weight || parseInt(step.id) || 1,
          status: initialStatus,
          progressPercentage: progressPercentage,
          startDate: startDate,
          completedDate: null, // Will be set when stage is completed
          requirementsMet: 0,
          totalRequirements: 0
        });

        stages.push(stage);

        this.logger.log(
          `Created stage: ${stageName} with status: ${initialStatus} for application: ${applicationId}`
        );
      }

      this.logger.log(
        `Successfully initialized ${stages.length} application stages from course requirements`
      );
      return stages;
    } catch (error) {
      this.logger.error(
        'Failed to initialize application stages from course requirements:',
        error
      );
      throw error;
    }
  }

  /**
   * Get dashboard data by campus ID
   * Returns counts for total applications, pending applications, CAL generated, and Admission Portfolio generated
   */
  async getDashboardDataByCampus(campusId: number): Promise<{
    totalApplications: number;
    pendingApplications: number;
    calGenerated: number;
    admissionPortfolioGenerated: number;
    campusId: number;
  }> {
    try {
      this.logger.log(`Getting dashboard data for campus ID: ${campusId}`);

      // Count total applications for the campus
      const totalApplicationsResult =
        await this.applicationRepository.findAndCountAll({
          where: {
            universityCountryCampus: campusId
          },
          limit: 0
        });

      // Count pending applications for the campus
      const pendingApplicationsResult =
        await this.applicationRepository.findAndCountAll({
          where: {
            universityCountryCampus: campusId,
            status: ApplicationStatus.PENDING
          },
          limit: 0
        });

      // Count CAL generated applications for the campus
      const calGeneratedResult =
        await this.applicationRepository.findAndCountAll({
          where: {
            universityCountryCampus: campusId,
            isCalGenerated: true
          },
          limit: 0
        });

      // Count Admission Portfolio generated applications for the campus
      const admissionPortfolioGeneratedResult =
        await this.applicationRepository.findAndCountAll({
          where: {
            universityCountryCampus: campusId,
            isApGenerated: true
          },
          limit: 0
        });

      const dashboardData = {
        totalApplications: totalApplicationsResult.count,
        pendingApplications: pendingApplicationsResult.count,
        calGenerated: calGeneratedResult.count,
        admissionPortfolioGenerated: admissionPortfolioGeneratedResult.count,
        campusId
      };

      this.logger.log(
        `Dashboard data retrieved for campus ${campusId}:`,
        dashboardData
      );

      return dashboardData;
    } catch (error) {
      this.logger.error(
        `Failed to get dashboard data for campus ${campusId}:`,
        error
      );
      throw error;
    }
  }
  // private getRequiredDocumentsByApplicationType(
  //   applicationType: string
  // ): string[] {
  //   const documentRequirements = {
  //     'F-1 initial': [
  //       'Resume',
  //       'Passport',
  //       'Statement Of Bank Documents',
  //       'Letter Of Recommendation',
  //       'Statement Of Purpose',
  //       'Sponsor Nid'
  //     ],
  //     Transfer: [
  //       'Resume',
  //       'Passport',
  //       'Visa',
  //       'Statement Of Bank Documents',
  //       // 'Statement Of Purpose',
  //       'Sponsor Nid'
  //     ],
  //     Reinstatement: [
  //       'Resume',
  //       'Passport',
  //       'Visa',
  //       'Statement Of Bank Documents'
  //       // 'Letter Of Recommendation',
  //       // 'Statement Of Purpose',
  //       // 'Sponsor Nid'
  //     ],
  //     'Non F-1': ['Passport'],
  //     'New Program': [
  //       'Passport',
  //       'Resume',
  //       'Visa',
  //       'Statement Of Bank Documents',
  //       // 'Letter Of Recommendation',
  //       // 'Statement Of Purpose',
  //       'Sponsor Nid'
  //     ],
  //     'Change of Status': [
  //       'Resume',
  //       'Passport',
  //       'Visa',
  //       'Statement Of Bank Documents',
  //       // 'Letter Of Recommendation',
  //       'Statement Of Purpose',
  //       'I-797A Documents Upload'
  //       // 'Sponsor Nid'
  //     ]
  //   };

  //   return documentRequirements[applicationType] || [];
  // }

  private getRequiredDocumentsByApplicationType(
    applicationType: string
  ): string[] {
    const documentRequirements = {
      'F-1 initial': [
        'Resume',
        'Passport',
        'Statement Of Bank Documents',
        'Letter Of Recommendation',
        'Statement Of Purpose',
        'Sponsor Nid'
        // No visa documents for F-1 initial
      ],
      Transfer: [
        'Resume',
        'Passport',
        'Copy of Visa',
        'Statement Of Bank Documents',
        'Sponsor Nid',
        'I-20', // ✅ Added Student I-20
        'I-94' // ✅ Added I-94
        // 'Statement Of Purpose',
      ],
      Reinstatement: [
        'Resume',
        'Passport',
        'Copy of Visa',
        'Statement Of Bank Documents',
        'I-20', // ✅ Added Student I-20
        'I-94' // ✅ Added I-94
        // 'I-797c' // ✅ Added I-797C
        // 'Letter Of Recommendation',
        // 'Statement Of Purpose',
        // 'Sponsor Nid'
      ],
      'Non F-1': [
        'Passport',
        'I-797C'
        // No visa documents for Non F-1
      ],
      'New Program': [
        'Passport',
        'Resume',
        'Copy of Visa',
        'Statement Of Bank Documents',
        'Sponsor Nid',
        'I-20', // ✅ Added Student I-20
        'I-94' // ✅ Added I-94
        // 'Letter Of Recommendation',
        // 'Statement Of Purpose',
      ],
      'Change of Status': [
        'Resume',
        'Passport',
        'Copy of Visa',
        'Statement Of Bank Documents',
        'Statement Of Purpose',
        // 'I-20', // ✅ Added Student I-20
        'I-94', // ✅ Added I-94
        // 'I-797C', // ✅ Added I-797C
        'I-797A'
        // 'Letter Of Recommendation',
      ]
    };

    return documentRequirements[applicationType] || [];
  }
  // ✅ NEW: Helper method to get required academic documents based on lastAcademic level
  private getRequiredAcademicDocuments(lastAcademic: string): string[] {
    const academicMapping = {
      '12th grade': [
        'Transcript of 12th Grade',
        'Certification of 12th Grade',
        'Transcript of 10th Grade',
        'Certification of 10th Grade'
      ],
      'Bachelor`s Program': [
        'Transcript of Bachelor`s Program',
        'Certification of Bachelor`s Program',
        'Transcript of 12th Grade',
        'Certification of 12th Grade',
        'Transcript of 10th Grade',
        'Certification of 10th Grade'
      ],
      'Master`s Program': [
        'Transcript of Master`s Program',
        'Certification of Master`s Program',
        'Transcript of Bachelor`s Program',
        'Certification of Bachelor`s Program',
        'Transcript of 12th Grade',
        'Certification of 12th Grade',
        'Transcript of 10th Grade',
        'Certification of 10th Grade'
      ],
      'PhD Program': [
        'Transcript of PhD Program',
        'Certification of PhD Program',
        'Transcript of Master`s Program',
        'Certification of Master`s Program',
        'Transcript of Bachelor`s Program',
        'Certification of Bachelor`s Program',
        'Transcript of 12th Grade',
        'Certification of 12th Grade',
        'Transcript of 10th Grade',
        'Certification of 10th Grade'
      ]
    };

    return (
      academicMapping[lastAcademic] || [
        'Certification of 10th Grade',
        'Transcript of 10th Grade',
        'Certification of 12th Grade',
        'Transcript of 12th Grade'
      ]
    );
  }
  async validateCourseRequirements(
    courseId: number,
    programLevelId: number
  ): Promise<any> {
    const courseRequirements =
      await this.universityClientService.getCourseRequirements({
        courseId,
        programLevelId
      });

    if (
      !courseRequirements ||
      courseRequirements.status !== 200 ||
      !courseRequirements.data
    ) {
      return false;
    }

    const lastAcademiceLevel = courseRequirements.data.course?.lastAcademic;

    if (!lastAcademiceLevel) {
      return false;
    }

    return lastAcademiceLevel;
  }

  // ✅ Helper method to safely stringify objects with BigInt values
  private safeStringify(obj: any): string {
    return JSON.stringify(obj, (key, value) =>
      typeof value === 'bigint' ? value.toString() : value
    );
  }

  // JSON validation interface for requirements description
  private validateRequirementsDescription(description: any): boolean {
    if (!description || typeof description !== 'object') {
      return false;
    }

    // Check if all values are strings and contain valid HTML
    for (const [key, value] of Object.entries(description)) {
      if (
        typeof value !== 'string' ||
        !value.includes('<ul class="requirements-list">')
      ) {
        this.logger.warn(`Invalid requirements description for key: ${key}`);
        return false;
      }
    }

    return true;
  }

  // Get requirements description with validation
  private getRequirementsDescription(documentName: string): string {
    const description = this.requirementsDescription[documentName];
    if (!description) {
      this.logger.warn(`No description found for document: ${documentName}`);
      return `Document requirement: ${documentName}`;
    }
    return description;
  }

  private requirementsDescription = {
    Passport:
      '<ul class="requirements-list"><li>Upload a copy of the applicant\'s passport showing personal details (Name, Date of Birth, Passport Expiry Date).</li><li>File must be clear and legible (no cut-offs).</li></ul>',

    'Language Proficiency Certificate':
      '<ul class="requirements-list"><li>Provide a copy of the applicant\'s English test scores.</li><li>Must include a verification link</li><li>Document must be clear and complete (no edges cut off).</li><li>Special note for Duolingo Test:</li><li>Upload the Duolingo certificate with the verification link</li><li>Must be in English format</li></ul>',

    'Transcript of 12th Grade':
      '<ul class="requirements-list"><li>Original + English Translation</li><li>Clear, complete, no cut-offs</li></ul>',

    'Certification of 12th Grade':
      '<ul class="requirements-list"><li>Original + English Translation</li><li>Clear, complete, no cut-offs</li></ul>',

    'Transcript of 10th Grade':
      '<ul class="requirements-list"><li>Original + English Translation</li><li>Clear, complete, no cut-offs</li></ul>',

    'Certification of 10th Grade':
      '<ul class="requirements-list"><li>Original + English Translation</li><li>Clear, complete, no cut-offs</li></ul>',

    'Bank Statement':
      '<ul class="requirements-list"><li>Must show liquid funds (readily accessible).</li><li>Can be a single statement covering the total required funds OR combined statements from listed sponsors.</li><li>Balance must meet or exceed the amount stated on the Financial Guarantee form for one year of study.</li><li>Must be issued within the last 6 months.</li><li>An Affidavit of Support is not accepted as a substitute.</li></ul>',

    'Statement Of Bank Documents':
      '<ul class="requirements-list"><li>Must show liquid funds (readily accessible).</li><li>Can be a single statement covering the total required funds OR combined statements from listed sponsors.</li><li>Balance must meet or exceed the amount stated on the Financial Guarantee form for one year of study.</li><li>Must be issued within the last 6 months.</li><li>An Affidavit of Support is not accepted as a substitute.</li></ul>',

    'Financial Guarantee':
      '<ul class="requirements-list"><li>Proof of sufficient funds per academic year, covering tuition, fees, housing, books, and insurance.</li><li>Personal/family savings must be verified with a bank statement not older than 6 months.</li><li>If sponsored by:</li><li>Family/Friend/Private sponsor → Provide a signed sponsor letter with relationship details.</li><li>Government/Agency → Submit an official letter confirming scholarship validity, start, and end dates.</li></ul>',

    'Statement of Purpose':
      '<ul class="requirements-list"><li>Please write and upload your Statement of Purpose (SOP). Your SOP is an important part of the application process, designed to be used as an integrity assessment tool that the Australian Government uses to assess an applicant\'s eligibility and genuine intent to study in Australia.</li><li>Please check the link below to see what to include in your SOP. Be sure to upload any additional supporting documents with your SOP. Important: Your SOP must be written on your own (not by your agent or a family member), and it should match your level of English proficiency.</li></ul>',

    'Statement Of Purpose':
      '<ul class="requirements-list"><li>Please write and upload your Statement of Purpose (SOP). Your SOP is an important part of the application process, designed to be used as an integrity assessment tool that the Australian Government uses to assess an applicant\'s eligibility and genuine intent to study in Australia.</li><li>Please check the link below to see what to include in your SOP. Be sure to upload any additional supporting documents with your SOP. Important: Your SOP must be written on your own (not by your agent or a family member), and it should match your level of English proficiency.</li></ul>',

    Resume:
      '<ul class="requirements-list"><li>Most recent version, showing education, work, and volunteer experience in chronological order (with month/year).</li><li>For graduate applicants, must align with the chosen field of study.</li><li>Must include periods leading up to program start date (future months can be predicted).</li><li>Will be rejected if incomplete.</li><li>IMPORTANT: Applicants must include the period of time leading up to the month of the program start date; this will have to be predicted, as it is in the future. The resume will be rejected if it does not include this information.</li></ul>',
    'Personal Statement':
      '<ul class="requirements-list"><li>Explains academic/professional background, motivation, aptitude, and career goals.</li><li>Justifies why the applicant is a strong candidate for the program.</li></ul>',

    'Letter of Recommendation':
      '<ul class="requirements-list"><li>At least one academic reference (if graduated within the last 2 years).</li><li>Others may be from employers or supervisors relevant to the applicant\'s field.</li></ul>',

    "Transcript of Bachelor's Program":
      '<ul class="requirements-list"><li>Please provide a copy of the applicant\'s original transcripts and English-translated transcripts:</li><li>The photocopy/scan has no cut-off at the edges and is clear to read</li></ul>',

    'Transcript of Bachelor`s Program':
      '<ul class="requirements-list"><li>Please provide a copy of the applicant\'s original transcripts and English-translated transcripts:</li><li>The photocopy/scan has no cut-off at the edges and is clear to read</li></ul>',

    "Certification of Bachelor's Program":
      '<ul class="requirements-list"><li>Please provide a copy of the applicant\'s original transcripts and English-translated transcripts:</li><li>The photocopy/scan has no cut-off at the edges and is clear to read</li></ul>',

    'Certification of Bachelor`s Program':
      '<ul class="requirements-list"><li>Please provide a copy of the applicant\'s original transcripts and English-translated transcripts:</li><li>The photocopy/scan has no cut-off at the edges and is clear to read</li></ul>',
    "Transcript of Master's Program":
      '<ul class="requirements-list"><li>Please provide a copy of the applicant\'s original transcripts and English-translated transcripts:</li><li>The photocopy/scan has no cut-off at the edges and is clear to read</li></ul>',

    "Certification of Master's Program":
      '<ul class="requirements-list"><li>Please provide a copy of the applicant\'s original transcripts and English-translated transcripts:</li><li>The photocopy/scan has no cut-off at the edges and is clear to read</li></ul>',

    'Personal Statement UK':
      '<ul class="requirements-list"><li>Length: 1,000–4,000 characters (max 47 lines, including spaces).</li><li>75% academic/subject-specific focus</li><li>25% extracurriculars (passion for subject area)</li><li>Must explain: course choice, reason for studying in the UK, international study preference, English skills, study gaps, and any prior visa refusals.</li><li>Should follow the ABC rule: Action, Benefit, Course relevance.</li></ul>',
    'Study Gap Explanation':
      '<ul class="requirements-list"><li>Study Gap Explanation</li><li>If applicable, provide a statement explaining gaps with dates and activities (work, volunteering, etc.).</li><li>Resume can be attached if it covers the same information.</li></ul>',

    Visa: '<ul class="requirements-list"><li>I-20 Confirmation: Provide the date the I-20 was received.</li><li>Visa Submission Date (Step 1): Enter the date of visa application submission (YYYY/MM/DD). Do not enter the interview date.</li><li>Visa Progress (Step 2): Provide the date the visa result was received and indicate the outcome.</li><li>Visa Outcome (Step 3): Upload proof of visa result (approval letter, visa stamp, or refusal letter).</li><li>Confirmation of Enrollment (Step 5): Upload verification of enrollment from the institution, including:</li><li>Issue date (must be current/past withdrawal date)</li><li>Student name & ID number</li><li>Statement of full-time enrollment with program/term</li><li>No Outstanding Fees Verification: Provide a recent tuition receipt or statement showing no balance due.</li></ul>',

    'Copy of Visa':
      '<ul class="requirements-list"><li>Upload a clear copy of the applicant’s Visa.</li><li>Information must be clearly visible.</li></ul>',

    Interview:
      '<ul class="requirements-list"><li>Provide the visa result date (YYYY/MM/DD) with outcome status.</li></ul>',

    'Affidavit of Financial Support':
      '<ul class="requirements-list"><li>Completed and signed by sponsor (must match bank statement sponsor).</li></ul>',

    'Offer Letter':
      '<ul class="requirements-list"><li>Upload the official offer letter issued by the institution.</li></ul>',

    'I-94':
      '<ul class="requirements-list"><li>Upload the most recent I-94 record (retrieved from the CBP website or the paper copy if issued).</li><li>Must show admission date, class of admission, and admit-until date.</li><li>Ensure the document is up to date and clearly readable.</li></ul>',

    'I-20':
      '<ul class="requirements-list"><li>Upload a clear copy of all pages of the I-20.</li><li>Must include the student\'s details, SEVIS ID, program start and end date, and signatures.</li><li>Ensure no cut-offs and all text is clearly legible.</li></ul>',

    'I-797C':
      '<ul class="requirements-list"><li>Upload the entire notice received from USCIS.</li><li>Must show applicant\'s name, case type, receipt number, and notice date.</li><li>Ensure the document is scanned/photographed fully without cut-offs.</li></ul>',

    'I-797A':
      '<ul class="requirements-list"><li>Upload the complete I-797A approval notice, including the bottom section containing the I-94.</li><li>Must clearly show applicant\'s name, petition/approval details, validity dates, and I-94 info.</li><li>The document must be clear, sharp, and without missing sections.</li></ul>',

    Photo:
      '<ul class="requirements-list"><li>Each dependent must provide a 2x2 size photo</li><li>Image Quality: Must be clear, sharp, and recent</li><li>Background: White or blue solid background</li><li>Face Visibility: No glasses or obstructions</li><li>Ears: Both ears must be clearly visible</li></ul>',

    'Student National Identity':
      '<ul class="requirements-list"><li>Upload a clear copy of the applicant\'s National Identification Card (both front and back).</li><li>Information must be clearly legible (Name, Date of Birth, NID Number).</li><li>Ensure no glare, blur, or cut-off edges.</li></ul>',

    'Sponsor Nid':
      '<ul class="requirements-list"><li>Upload a clear copy of the sponsor\'s National Identification Card (both front and back).</li><li>Information must be clearly visible (Name, NID Number, Date of Birth).</li><li>The NID details must match the sponsor name provided in the Financial Guarantee / Bank Statement.</li></ul>',

    'Dependents Documents':
      '<ul class="requirements-list"><li>If the applicant is applying with dependents (spouse/children), please upload the required documents as follows:</li><li>Declaration of Dependents</li><li>Indicate the number of dependents accompanying the applicant.</li><li>Specify the relationship type (Spouse / Child).</li><li>Passport Copies</li><li>Upload a clear copy of each dependent\'s passport (bio-data page).</li><li>Must show Name, Date of Birth, and Passport Expiry Date.</li><li>National Identification (if applicable)</li><li>Upload NID (both front and back) for each dependent (where available).</li><li>Birth / Marriage Certificates</li><li>For children → Upload the Birth Certificate.</li><li>For spouse → Upload the Marriage Certificate.</li><li>Documents must be clear, complete, and translated into English (if issued in another language).</li><li>Photo</li><li>Each dependent must provide a 2x2 size photo with the following requirements:</li><li>Clear and recent</li><li>White or blue solid background</li><li>No glasses or obstructions</li><li>Both ears visible</li></ul>'
  };

  // private async OldinitializeDocumentRequirements(
  //   application: Application,
  //   createApplicationDto: CreateApplicationDto
  // ) {
  //   try {
  //     this.logger.log(
  //       `Initializing document requirements for application: ${application.id}`
  //     );
  //     const studentType =
  //       createApplicationDto?.applicationType || 'F-1 initial';

  //     // 1) Get course requirements safely
  //     // let proficiencyRequirements: any[] = [];
  //     let applicationSteps: any[] = []; // Add this line
  //     let lastAcademiceLevel: any;
  //     try {
  //       const resp = await this.universityClientService.getCourseRequirements({
  //         courseId: createApplicationDto.courseId,
  //         programLevelId: createApplicationDto.programId
  //       });

  //       console.log(
  //         '+++++++++++++++++++++++++++++++++++++>CourseInformationRecords+++++++++++++>',
  //         resp
  //       );

  //       if (resp && resp.status === 200 && resp.data) {
  //         // proficiencyRequirements = Array.isArray(
  //         //   resp.data.proficiencyRequirements
  //         // )
  //         //   ? resp.data.proficiencyRequirements
  //         //   : [];

  //         // Extract application steps from course requirements
  //         // if (studentType === 'F-1 initial') {
  //         applicationSteps = Array.isArray(resp.data.applicationSteps)
  //           ? resp.data.applicationSteps.filter(
  //               (step: any) => step.title !== 'Collecting Documents'
  //             )
  //           : [];
  //         // } else {
  //         //   applicationSteps = Array.isArray(resp.data.applicationSteps)
  //         //     ? resp.data.applicationSteps
  //         //     : [];
  //         // }

  //         // update applicationSteps
  //         // if (studentType === 'F-1 initial') {
  //         //   applicationSteps = Array.isArray(resp.data.applicationSteps)
  //         //     ? resp.data.applicationSteps
  //         //     : [];
  //         // } else {
  //         //   applicationSteps = [
  //         //     { name: 'collecting_documents', order: 1 },
  //         //     { name: 'accepted_rejected', order: 2 }
  //         //   ];
  //         // }

  //         lastAcademiceLevel = resp.data.course?.lastAcademic;
  //       } else {
  //         this.logger.warn(
  //           `Failed to get course requirements for courseId: ${createApplicationDto.courseId}`
  //         );
  //       }
  //     } catch (e) {
  //       this.logger.warn(
  //         `Error getting course requirements for courseId: ${createApplicationDto.courseId} — proceeding with defaults`
  //       );
  //       // proficiencyRequirements = [];
  //       applicationSteps = [];
  //     }

  //     // Initialize application stages from course requirements
  //     if (applicationSteps.length > 0) {
  //       try {
  //         // Call the method from ApplicationStageService (not ApplicationService)
  //         const stages =
  //           await this.initializeApplicationStagesFromCourseRequirements(
  //             Number(application.id),
  //             applicationSteps
  //           );
  //         console.log(
  //           `Initialized ${stages.length} application stages from course requirements`
  //         );
  //       } catch (stageError) {
  //         this.logger.error(
  //           'Failed to initialize application stages:',
  //           stageError
  //         );
  //         // Continue with document requirements even if stage initialization fails
  //       }
  //     } else {
  //       // Fallback to default stages if no application steps found
  //       try {
  //         await this.applicationStageService.initializeApplicationStages(
  //           Number(application.id)
  //         );
  //         console.log('Initialized default application stages');
  //       } catch (stageError) {
  //         this.logger.error(
  //           'Failed to initialize default application stages:',
  //           stageError
  //         );
  //       }
  //     }

  //     // 2) Get student profile slices safely
  //     const studentAcademic = await this.getStudentAcademic(
  //       createApplicationDto.studentId
  //     );
  //     // const studentAcademic = null;
  //     const academicRecords = Array.isArray(studentAcademic?.academicRecords)
  //       ? studentAcademic.academicRecords
  //       : [];
  //     const publicationRecords = Array.isArray(
  //       studentAcademic?.publicationRecords
  //     )
  //       ? studentAcademic.publicationRecords
  //       : [];
  //     const otherActivities = Array.isArray(studentAcademic?.otherActivities)
  //       ? studentAcademic.otherActivities
  //       : [];
  //     // const proficiencyRecords = Array.isArray(
  //     //   studentAcademic?.proficiencyRecords
  //     // )
  //     //   ? studentAcademic.proficiencyRecords
  //     //   : [];

  //     console.log(
  //       '+++++++++++++++++++++++++++++++++++++>Academice Record',
  //       academicRecords,
  //       // 'proficiencyRecords=========>',
  //       // proficiencyRecords,
  //       'publicationRecords===================>',
  //       publicationRecords,
  //       'otherActivities======================>',
  //       otherActivities
  //     );

  //     // 3) Merge missing proficiency requirements from course into local proficiencyRecords
  //     // const existingProficiencyNames = proficiencyRecords
  //     //   .map((r: any) => (r?.nameOfExam || '').toString().toLowerCase())
  //     //   .filter(Boolean);

  //     // for (const rec of proficiencyRequirements) {
  //     //   const testName = (rec?.test || '').toString().toLowerCase();
  //     //   if (!testName) continue;

  //     //   if (!existingProficiencyNames.includes(testName)) {
  //     //     let parsedScore: any = {};
  //     //     try {
  //     //       parsedScore = rec?.testScore ? JSON.parse(rec.testScore) : {};
  //     //     } catch {
  //     //       parsedScore = {};
  //     //     }
  //     //     proficiencyRecords.push({
  //     //       nameOfExam: rec?.test || '',
  //     //       score: parsedScore,
  //     //       examDate: '',
  //     //       expiryDate: '',
  //     //       note: ''
  //     //     });
  //     //   }
  //     // }

  //     // 4) Student uploaded documents map (section::field => url)
  //     const studentDocuments =
  //       await this.studentDocumentsService.getStudentDocuments(
  //         createApplicationDto.studentId,
  //         { includeInactive: false }
  //       );
  //     // 4) Get required documents based on application type
  //     const IdentityDocuments = this.getRequiredDocumentsByApplicationType(
  //       createApplicationDto.applicationType || 'F-1 initial'
  //     );

  //     this.logger.debug(
  //       'Idenity DocumentList following studentType',
  //       JSON.stringify(IdentityDocuments)
  //     );
  //     const docMap = new Map<string, string>();
  //     for (const d of studentDocuments || []) {
  //       const section = (d?.section ?? '').toString().trim().toLowerCase();
  //       const field = (d?.field ?? '').toString().trim().toLowerCase();
  //       if (!section || !field) continue;
  //       if (d?.url) docMap.set(`${section}::${field}`, d.url);
  //     }

  //     // 5) Build requirements array exactly per your model
  //     const requirements: any[] = [];
  //     const baseDefaults = {
  //       isRequired: true,
  //       allowedFormats: ['pdf'] as string[],
  //       requiredByDate: null as Date | null,
  //       verifiedAt: null as Date | null,
  //       verifiedBy: null as number | null,
  //       acceptedByApplyGoal: null as boolean | null,
  //       acceptedByApplyGoalUserId: null as number | null,
  //       acceptedByUniversity: false,
  //       acceptedByUniversityUserId: null as number | null,
  //       rejectionReason: null as string | null
  //     };
  //     // ✅ NEW: Add Academic Documents Based on lastAcademic Level
  //     // if (lastAcademiceLevel) {
  //     //   const academicDocuments =
  //     //     this.getRequiredAcademicDocuments(lastAcademiceLevel);

  //     //   for (const academicDoc of academicDocuments) {
  //     //     const doc = docMap.get(`academic::${academicDoc.toLowerCase()}`);
  //     //     const hasFile = !!doc;

  //     //     requirements.push({
  //     //       applicationId: Number(application.id),
  //     //       studentId: createApplicationDto.studentId,
  //     //       documentTitle: 'academic',
  //     //       documentName: academicDoc,
  //     //       url: doc || null,
  //     //       documentDescription: `Academic transcript or certificate for ${academicDoc.toUpperCase()}`,
  //     //       documentStatus: hasFile
  //     //         ? DocumentStatus.UPLOADED
  //     //         : DocumentStatus.REQUESTED,
  //     //       uploadedAt: hasFile ? new Date() : null,
  //     //       ...baseDefaults
  //     //     });
  //     //   }
  //     // }

  //     // //  ✅ Academic
  //     // for (const academic of academicRecords) {
  //     //   const nameRaw = (academic as any)?.nameOfExam || '';
  //     //   const tag = nameRaw.toString().trim().toLowerCase();
  //     //   if (!tag) continue;

  //     //   const doc = docMap.get(`academic::${tag}`);
  //     //   const hasFile = !!doc;

  //     //   requirements.push({
  //     //     applicationId: Number(application.id),
  //     //     documentTitle: 'academic',
  //     //     documentName: nameRaw,
  //     //     url: doc || null,
  //     //     documentDescription: `Transcript or certificate for ${nameRaw
  //     //       .toString()
  //     //       .toUpperCase()} (${
  //     //       (academic as any).institute || 'Unknown Institute'
  //     //     }, ${(academic as any).board || 'Unknown Board'}, ${
  //     //       (academic as any).passingYear || 'Unknown Year'
  //     //     })`,
  //     //     documentStatus: hasFile
  //     //       ? DocumentStatus.UPLOADED
  //     //       : DocumentStatus.REQUESTED,
  //     //     uploadedAt: hasFile ? new Date() : null,
  //     //     ...baseDefaults
  //     //   });
  //     // }

  //     // ✅ Academic Requirements - Merged approach to avoid duplicates
  //     const academicRequirements = new Map<string, any>();

  //     // First, add requirements based on lastAcademicLevel (what's required)
  //     if (lastAcademiceLevel) {
  //       const academicDocuments =
  //         this.getRequiredAcademicDocuments(lastAcademiceLevel);

  //       for (const academicDoc of academicDocuments) {
  //         const doc = docMap.get(`academic::${academicDoc.toLowerCase()}`);
  //         const hasFile = !!doc;

  //         // Make 10th grade certificate and transcript optional (not required)
  //         const isOptionalDocument =
  //           academicDoc.toLowerCase().includes('10th grade') ||
  //           academicDoc.toLowerCase().includes('certification of 10th') ||
  //           academicDoc.toLowerCase().includes('transcript of 10th');

  //         academicRequirements.set(academicDoc.toLowerCase(), {
  //           applicationId: Number(application.id),
  //           studentId: createApplicationDto.studentId,
  //           documentTitle: 'academic',
  //           documentName: academicDoc,
  //           url: doc || null,
  //           documentDescription: `Academic transcript or certificate for ${academicDoc.toUpperCase()}`,
  //           documentStatus: hasFile
  //             ? DocumentStatus.UPLOADED
  //             : DocumentStatus.REQUESTED,
  //           uploadedAt: hasFile ? new Date() : null,
  //           ...baseDefaults,
  //           isRequired: !isOptionalDocument // Set to false for 10th grade documents, true for others
  //         });
  //       }
  //     }

  //     this.logger.debug(
  //       `Academic Requirements For lastAcademice ${lastAcademiceLevel}: ${JSON.stringify(
  //         Array.from(academicRequirements.entries())
  //       )}`
  //     );

  //     // Then, update with actual student academic records (more specific info)
  //     for (const academic of academicRecords) {
  //       const nameRaw = (academic as any)?.nameOfExam || '';
  //       const tag = nameRaw.toString().trim().toLowerCase();
  //       if (!tag) continue;

  //       const doc = docMap.get(`academic::${tag}`);
  //       const hasFile = !!doc;

  //       // Check if this academic level is already in requirements
  //       if (academicRequirements.has(tag)) {
  //         // Update existing requirement with more specific information
  //         const existing = academicRequirements.get(tag);
  //         academicRequirements.set(tag, {
  //           ...existing,
  //           url: doc || existing.url, // Use actual document if available
  //           documentStatus: hasFile
  //             ? DocumentStatus.UPLOADED
  //             : existing.documentStatus,
  //           uploadedAt: hasFile ? new Date() : existing.uploadedAt,
  //           documentDescription: `Transcript or certificate for ${nameRaw
  //             .toString()
  //             .toUpperCase()} (${
  //             (academic as any).institute || 'Unknown Institute'
  //           }, ${(academic as any).board || 'Unknown Board'}, ${
  //             (academic as any).passingYear || 'Unknown Year'
  //           })`
  //         });
  //       } else {
  //         // Add new requirement if not already required by lastAcademicLevel
  //         academicRequirements.set(tag, {
  //           applicationId: Number(application.id),
  //           studentId: createApplicationDto.studentId,
  //           documentTitle: 'academic',
  //           documentName: nameRaw,
  //           url: doc || null,
  //           documentDescription: `Transcript or certificate for ${nameRaw
  //             .toString()
  //             .toUpperCase()} (${
  //             (academic as any).institute || 'Unknown Institute'
  //           }, ${(academic as any).board || 'Unknown Board'}, ${
  //             (academic as any).passingYear || 'Unknown Year'
  //           })`,
  //           documentStatus: hasFile
  //             ? DocumentStatus.UPLOADED
  //             : DocumentStatus.REQUESTED,
  //           uploadedAt: hasFile ? new Date() : null,
  //           ...baseDefaults
  //         });
  //       }
  //     }

  //     // Add all academic requirements to the main requirements array
  //     for (const [key, requirement] of academicRequirements) {
  //       requirements.push(requirement);
  //     }
  //     // ✅ NEW: Identity Documents
  //     for (const identityDoc of IdentityDocuments) {
  //       const doc = docMap.get(`identity::${identityDoc.toLowerCase()}`);
  //       const hasFile = !!doc;

  //       requirements.push({
  //         applicationId: Number(application.id),
  //         studentId: createApplicationDto.studentId,
  //         documentTitle: 'identity',
  //         documentName: identityDoc,
  //         url: doc || null,
  //         documentDescription: `Identity document: ${identityDoc}`,
  //         documentStatus: hasFile
  //           ? DocumentStatus.UPLOADED
  //           : DocumentStatus.REQUESTED,
  //         uploadedAt: hasFile ? new Date() : null,
  //         ...baseDefaults
  //       });
  //     }
  //     // Proficiency
  //     // for (const prof of proficiencyRecords) {
  //     //   const nameRaw = (prof as any)?.nameOfExam || '';
  //     //   const tag = nameRaw.toString().trim().toLowerCase();
  //     //   if (!tag) continue;

  //     //   const doc = docMap.get(`proficiency::${tag}`);
  //     //   const hasFile = !!doc;

  //     //   requirements.push({
  //     //     applicationId: Number(application.id),
  //     //     studentId: createApplicationDto.studentId,
  //     //     documentTitle: 'proficiency',
  //     //     documentName: nameRaw,
  //     //     url: doc || null,
  //     //     documentDescription: `Upload a valid ${nameRaw
  //     //       .toString()
  //     //       .toUpperCase()} score report.`,
  //     //     documentStatus: hasFile
  //     //       ? DocumentStatus.UPLOADED
  //     //       : DocumentStatus.REQUESTED,
  //     //     uploadedAt: hasFile ? new Date() : null,
  //     //     ...baseDefaults
  //     //   });
  //     // }

  //     // ✅ NEW: Single Proficiency Requirement
  //     // Check if student has any proficiency documents
  //     let proficiencyUrl: string | null = null;
  //     let proficiencyFound = false;

  //     // Look for any proficiency document in student documents
  //     for (const d of studentDocuments || []) {
  //       const section = (d?.section ?? '').toString().trim().toLowerCase();
  //       if (section === 'proficiency' && d?.url) {
  //         proficiencyUrl = d.url;
  //         proficiencyFound = true;
  //         break;
  //       }
  //     }

  //     // Add single proficiency requirement
  //     requirements.push({
  //       applicationId: Number(application.id),
  //       studentId: createApplicationDto.studentId,
  //       documentTitle: 'proficiency',
  //       documentName: 'Language Proficiency Certificate',
  //       url: proficiencyUrl,
  //       documentDescription:
  //         'Upload a valid language proficiency certificate (IELTS, TOEFL, etc.)',
  //       documentStatus: proficiencyFound
  //         ? DocumentStatus.UPLOADED
  //         : DocumentStatus.REQUESTED,
  //       uploadedAt: proficiencyFound ? new Date() : null,
  //       ...baseDefaults
  //     });

  //     // Publications
  //     for (const pub of publicationRecords) {
  //       const subjRaw = (pub as any)?.subject || '';
  //       const tag = subjRaw.toString().trim().toLowerCase();
  //       if (!tag) continue;

  //       const doc = docMap.get(`publication::${tag}`);
  //       const chosenUrl = doc || (pub as any)?.link || null;
  //       const hasFile = !!chosenUrl;

  //       requirements.push({
  //         applicationId: Number(application.id),
  //         studentId: createApplicationDto.studentId,
  //         documentTitle: 'publication',
  //         documentName: subjRaw,
  //         url: chosenUrl,
  //         documentDescription: `Publication on ${subjRaw} in ${
  //           (pub as any).journal || 'Unknown Journal'
  //         }`,
  //         documentStatus: hasFile
  //           ? DocumentStatus.UPLOADED
  //           : DocumentStatus.REQUESTED,
  //         uploadedAt: hasFile ? new Date() : null,
  //         ...baseDefaults
  //       });
  //     }

  //     // Other activities
  //     for (const activity of otherActivities) {
  //       const subjRaw = (activity as any)?.subject || '';
  //       const tag = subjRaw.toString().trim().toLowerCase();
  //       if (!tag) continue;

  //       const doc = docMap.get(`other::${tag}`);
  //       const chosenUrl =
  //         doc ||
  //         (activity as any)?.certificationLink ||
  //         (activity as any)?.certificateLink ||
  //         null;
  //       const hasFile = !!chosenUrl;

  //       requirements.push({
  //         applicationId: Number(application.id),
  //         studentId: createApplicationDto.studentId,
  //         documentTitle: 'other',
  //         documentName: subjRaw,
  //         url: chosenUrl,
  //         documentDescription: `Certificate or proof for ${subjRaw}`,
  //         documentStatus: hasFile
  //           ? DocumentStatus.UPLOADED
  //           : DocumentStatus.REQUESTED,
  //         uploadedAt: hasFile ? new Date() : null,
  //         ...baseDefaults
  //       });
  //     }

  //     // 6) Upsert into DB (match by title+documentName)
  //     if (requirements.length > 0) {
  //       const existing = await this.applicationDocumentRequirementModel.findAll(
  //         {
  //           where: { applicationId: Number(application.id) }
  //         }
  //       );

  //       for (const r of requirements) {
  //         const found = existing.find(
  //           (e: any) =>
  //             e.documentTitle === r.documentTitle &&
  //             e.documentName === r.documentName
  //         );

  //         if (found) {
  //           await found.update({
  //             documentStatus: r.documentStatus,
  //             url: r.url,
  //             uploadedAt: r.uploadedAt,
  //             documentDescription: r.documentDescription,
  //             updatedAt: new Date()
  //           });
  //         } else {
  //           await this.applicationDocumentRequirementModel.create(r);
  //         }
  //       }

  //       this.logger.log(
  //         `Processed ${requirements.length} document requirements for application: ${application.id}`
  //       );
  //     } else {
  //       this.logger.log('No document requirements to create');
  //     }

  //     this.logger.log(
  //       `Document requirements initialized successfully for application: ${application.id}`
  //     );
  //   } catch (error: any) {
  //     this.logger.error(
  //       `Failed to initialize document requirements: ${error?.message}`,
  //       error?.stack
  //     );
  //   }
  // }
}
