import { Table, Column, DataType, ForeignKey, BelongsTo } from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { ApplicationDocumentRequirement } from './application-document-requirement.model';

@Table({ tableName: 'application_document_notes' })
export class ApplicationDocumentNote extends BaseModel {
  @ForeignKey(() => ApplicationDocumentRequirement)
  @Column(DataType.BIGINT)
  requirementId!: number;

  @Column(DataType.BIGINT)
  createdBy!: number;

  @Column(DataType.TEXT)
  note!: string;

  @Column(DataType.DATE)
  createdAt!: Date;

  @BelongsTo(() => ApplicationDocumentRequirement)
  requirement!: ApplicationDocumentRequirement;
}
