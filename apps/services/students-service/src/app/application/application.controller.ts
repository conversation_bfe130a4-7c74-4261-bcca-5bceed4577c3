import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  HttpException,
  HttpStatus,
  Logger
} from '@nestjs/common';
import { ApplicationService, CreateApplicationDto, UpdateApplicationDto } from './application.service';
import { ApplicationStatus } from './application.model';

@Controller('applications')
export class ApplicationController {
  private readonly logger = new Logger(ApplicationController.name);

  constructor(private readonly applicationService: ApplicationService) {}

  @Post()
  async createApplication(@Body() createApplicationDto: CreateApplicationDto) {
    try {
      this.logger.log(`Creating application for student: ${createApplicationDto.studentId}`);

      const application = await this.applicationService.createApplication(createApplicationDto);

      return {
        status: 'success',
        message: 'Application created successfully',
        data: application
      };
    } catch (error) {
      this.logger.error(`Failed to create application: ${error.message}`);
      throw new HttpException(
        error.message || 'Failed to create application',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get(':id')
  async getApplication(@Param('id') id: string) {
    try {
      const application = await this.applicationService.findOne(Number(id));

      return {
        status: 'success',
        message: 'Application retrieved successfully',
        data: application
      };
    } catch (error) {
      this.logger.error(`Failed to get application: ${error.message}`);
      throw new HttpException(
        error.message || 'Failed to get application',
        error.status || HttpStatus.NOT_FOUND
      );
    }
  }

  @Get('student/:studentId')
  async getApplicationsByStudent(@Param('studentId') studentId: string) {
    try {
      const applications = await this.applicationService.findByStudentId(studentId);

      return {
        status: 'success',
        message: 'Applications retrieved successfully',
        data: applications
      };
    } catch (error) {
      this.logger.error(`Failed to get applications for student: ${error.message}`);
      throw new HttpException(
        error.message || 'Failed to get applications',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Put(':id')
  async updateApplication(
    @Param('id') id: string,
    @Body() updateApplicationDto: UpdateApplicationDto
  ) {
    try {
      const application = await this.applicationService.update(Number(id), updateApplicationDto);

      return {
        status: 'success',
        message: 'Application updated successfully',
        data: application
      };
    } catch (error) {
      this.logger.error(`Failed to update application: ${error.message}`);
      throw new HttpException(
        error.message || 'Failed to update application',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Delete(':id')
  async deleteApplication(@Param('id') id: string) {
    try {
      await this.applicationService.remove(Number(id));

      return {
        status: 'success',
        message: 'Application deleted successfully'
      };
    } catch (error) {
      this.logger.error(`Failed to delete application: ${error.message}`);
      throw new HttpException(
        error.message || 'Failed to delete application',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get()
  async listApplications(
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('studentId') studentId?: string,
    @Query('status') status?: ApplicationStatus,
    @Query('sortBy') sortBy?: string,
    @Query('sortOrder') sortOrder?: 'asc' | 'desc'
  ) {
    try {
      const result = await this.applicationService.findAll({
        page: page ? parseInt(page) : 1,
        limit: limit ? parseInt(limit) : 10,
        studentId,
        status,
        sortBy: sortBy || 'createdAt',
        sortOrder: sortOrder || 'desc'
      });

      return {
        status: 'success',
        message: 'Applications retrieved successfully',
        data: {
          applications: result.rows,
          total: result.count,
          page: page ? parseInt(page) : 1,
          limit: limit ? parseInt(limit) : 10
        }
      };
    } catch (error) {
      this.logger.error(`Failed to list applications: ${error.message}`);
      throw new HttpException(
        error.message || 'Failed to list applications',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
} 