import {
  Table,
  Column,
  DataType,
  ForeignKey,
  BelongsTo,
  HasMany
} from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { Application } from './application.model';
import { ApplicationDocumentNote } from './application-documents-notes';
import { Col } from 'sequelize/types/utils';

export enum DocumentStatus {
  REQUESTED = 'requested',
  UPLOADED = 'uploaded',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  MISSING = 'missing',
  NO_STATUS='no status',
  REVIEW="review",
  COMPLETED="completed",
  PENDING="pending"

}

@Table({ tableName: 'application_document_requirements' })
export class ApplicationDocumentRequirement extends BaseModel {
  @ForeignKey(() => Application)
  @Column(DataType.BIGINT)
  applicationId!: number;

  @Column(DataType.STRING)
  studentId!: string;

  @Column({ type: DataType.STRING, allowNull: false })
  documentTitle!: string; // e.g., English Proficiency, Passport

  @Column({ type: DataType.STRING, allowNull: false })
  documentName!: string; // e.g., IELTS, TOEFL, etc.

  @Column({ type: DataType.STRING, allowNull: true })
  url!: string;

  @Column({ type: DataType.TEXT })
  documentDescription!: string;

  @Column({ type: DataType.BOOLEAN, allowNull: false, defaultValue: true })
  isRequired!: boolean;

  @Column({
    type: DataType.ARRAY(DataType.STRING),
    allowNull: false,
    defaultValue: ['pdf', 'jpg', 'png']
  })
  allowedFormats!: string[];

  @Column({
    type: DataType.ENUM(...Object.values(DocumentStatus)),
    defaultValue: DocumentStatus.REQUESTED
  })
  documentStatus!: DocumentStatus;

  @Column(DataType.DATE)
  requiredByDate!: Date;

  @Column(DataType.DATE)
  uploadedAt!: Date;

  @Column(DataType.DATE)
  verifiedAt!: Date;

  @Column(DataType.BIGINT)
  verifiedBy!: number;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
    defaultValue: null
  })
  acceptedByApplyGoal!: boolean | null;

  @Column(DataType.BIGINT)
  acceptedByApplyGoalUserId!: number;

  @Column(DataType.BOOLEAN)
  acceptedByUniversity!: boolean;

  @Column(DataType.BIGINT)
  acceptedByUniversityUserId!: number;

  @Column(DataType.TEXT)
  rejectionReason!: string;

  @Column({ type: DataType.BOOLEAN, allowNull: false, defaultValue: false })
  isNew!: boolean;

  @BelongsTo(() => Application)
  application!: Application;

  @HasMany(() => ApplicationDocumentNote)
  notes!: ApplicationDocumentNote[];
}
