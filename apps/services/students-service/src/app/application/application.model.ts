import { Table, Column, DataType } from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';

export enum ApplicationStatus {
  APPLIED = 'applied',
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  WITHDRAWN = 'withdrawn',
  CAL_GENERATED = 'cal_generated',
  ADM_GENERATED = 'adm_generated',
  AP_GENERATED = 'ap_generated',
  COLLECTING_DOCUMENTS = 'collecting_documents'
}

export enum PaymentStatus {
  PAID = 'paid',
  UNPAID = 'unpaid'
}

@Table({ tableName: 'applications' })
export class Application extends BaseModel {
  @Column(DataType.STRING)
  studentId!: string;

  @Column(DataType.BIGINT)
  universityId!: number;

  @Column(DataType.BIGINT)
  universityCountryId!: number;

  @Column(DataType.BIGINT)
  universityCountryCampus!: number;

  @Column(DataType.BIGINT)
  programId!: number;

  @Column(DataType.BIGINT)
  intakeId!: number;

  @Column(DataType.BIGINT)
  courseId!: number;

  @Column(DataType.TEXT)
  note!: string;

  @Column({
    type: DataType.ENUM(...Object.values(ApplicationStatus)),
    allowNull: false,
    defaultValue: ApplicationStatus.APPLIED
  })
  status!: ApplicationStatus;

  @Column({
    type: DataType.ENUM(...Object.values(PaymentStatus)),
    allowNull: false,
    defaultValue: PaymentStatus.UNPAID
  })
  paymentStatus!: PaymentStatus;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    defaultValue: 'F-1 initial'
  })
  applicationType!: string;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  applicationId!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    defaultValue: 'collecting_documents'
  })
  currentStage!: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: 0
  })
  overallProgress!: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: true
  })
  totalAmount!: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0
  })
  paidAmount!: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0
  })
  refundAmount!: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    defaultValue: 'online'
  })
  deliveryMethod!: string;

  // FIX: Make date fields nullable
  @Column({
    type: DataType.DATE,
    allowNull: true // <-- ADD THIS
  })
  submissionDate?: Date; // <-- Make optional with ?

  @Column({
    type: DataType.DATE,
    allowNull: true // <-- ADD THIS
  })
  deadlineDate?: Date; // <-- Make optional with ?

  @Column({
    type: DataType.DATE,
    allowNull: true // <-- ADD THIS
  })
  appliedAt?: Date; // <-- Make optional with ?

  // Note: updatedAt should be handled by BaseModel, remove if it's there
  // @Column(DataType.DATE)
  // updatedAt!: Date;

  @Column({
    type: DataType.STRING, // Changed to STRING to accommodate invoice number format like "INV-2025-08-28-668405"
    allowNull: true
  })
  invoiceId?: string; // Made optional and changed to string

  @Column({ type: DataType.TEXT, allowNull: true })
  paymentInvoiceUrl!: string;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  paymentMethod!: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false
  })
  applicationAcceptedByApplyGoal!: boolean;

  @Column({
    type: DataType.DATE,
    allowNull: true // <-- This one was already nullable, good
  })
  applicationAcceptedByApplyGoalAt?: Date; // <-- Make optional with ?

  @Column({
    type: DataType.BIGINT,
    allowNull: true
  })
  applicationAcceptedByApplyGoalUserId!: number;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
    defaultValue: false
  })
  isCalGenerated!: boolean;

  @Column({ type: DataType.TEXT, allowNull: true })
  calUrl!: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
    defaultValue: false
  })
  isAdmGenerated!: boolean;

  @Column({ type: DataType.TEXT, allowNull: true })
  admUrl!: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
    defaultValue: false
  })
  isApGenerated!: boolean;

  @Column({ type: DataType.TEXT, allowNull: true })
  apUrl!: string;

  @Column({
    type: DataType.BIGINT,
    allowNull: true
  })
  savisId!: number;

  @Column({ type: DataType.TEXT, allowNull: true })
  i20Url!: string;

  @Column({ type: DataType.TEXT, allowNull: true ,defaultValue: null})
  studentI20Url!: string;

  @Column({ type: DataType.TEXT, allowNull: true })
  i94Url!: string;

  @Column({ type: DataType.TEXT, allowNull: true })
  i797cUrl!: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
    defaultValue: null,
    comment: 'Flag to track if AP (Admission Portfolio) is approved'
  })
  isApApproved!: boolean | null;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: 'Notes for AP rejection if applicable'
  })
  apRejectionNotes!: string;
  // Document status fields
  @Column({ 
    type: DataType.ENUM('pending', 'approved', 'rejected', 'requested'), 
    allowNull: true,
    defaultValue: 'requested'
  })
  studentI20UrlStatus!: string;

  @Column({ 
    type: DataType.ENUM('pending', 'approved', 'rejected', 'requested'), 
    allowNull: true,
    defaultValue: 'requested'
  })
  i94UrlStatus!: string;

  @Column({ 
    type: DataType.ENUM('pending', 'approved', 'rejected', 'requested'), 
    allowNull: true,
    defaultValue: 'requested'
  })
  i797cUrlStatus!: string;

  @Column({
    type: DataType.BIGINT,
    allowNull: true,
    defaultValue: null,
    comment: 'User ID of the application officer who created this application (null if created by student)'
  })
  createdBy!: number | null;
}
