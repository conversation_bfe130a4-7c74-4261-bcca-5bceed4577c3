import {
  Table,
  Column,
  DataType,
  Foreign<PERSON>ey,
  BelongsTo
} from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { Application } from './application.model';

@Table({ tableName: 'application_notes' })
export class ApplicationNote extends BaseModel {
  @ForeignKey(() => Application)
  @Column(DataType.BIGINT)
  applicationId!: number;

  @Column({
    type: DataType.STRING,
    allowNull: false
  })
  noteType!: string; // internal, student, university

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  title!: string;

  @Column(DataType.TEXT)
  content!: string;

  @Column(DataType.BIGINT)
  createdBy!: number;

  @ForeignKey(() => ApplicationNote)
  @Column({
    type: DataType.BIGINT,
    allowNull: true,
    comment: 'Reference to parent note ID for replies. NULL for original notes.'
  })
  replyId!: number | null;

  @Column(DataType.DATE)
  createdAt!: Date;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false
  })
  isPrivate!: boolean;

  // New field to store user information as JSON
  @Column({
    type: DataType.JSON,
    allowNull: true,
    comment:
      'JSON object containing user information (student, applyBoard, university)'
  })
  userInfo!: any;

  @BelongsTo(() => Application)
  application!: Application;

  // Self-referencing association for replies
  @BelongsTo(() => ApplicationNote, 'replyId')
  parentNote!: ApplicationNote;
}
