import { Table, Column, DataType, Foreign<PERSON>ey, BelongsTo } from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { Application } from './application.model';

export enum ProgressRecordStatus {
  COMPLETED = 'completed',
  IN_PROGRESS = 'in_progress',
  FAILED = 'failed'
}

@Table({ tableName: 'application_progress_records' })
export class ApplicationProgressRecord extends BaseModel {
  @ForeignKey(() => Application)
  @Column(DataType.BIGINT)
  applicationId!: number;

  @Column({
    type: DataType.STRING,
    allowNull: false
  })
  recordType!: string; // document_upload, status_change, payment, offer_letter, etc.

  @Column({
    type: DataType.STRING,
    allowNull: false
  })
  title!: string;

  @Column(DataType.TEXT)
  description!: string;

  @Column({
    type: DataType.ENUM(...Object.values(ProgressRecordStatus)),
    allowNull: false,
    defaultValue: ProgressRecordStatus.COMPLETED
  })
  status!: ProgressRecordStatus;

  @Column(DataType.DATE)
  recordDate!: Date;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: true
  })
  amount!: number; // for payment records

  @Column({
    type: DataType.STRING,
    allowNull: false,
    defaultValue: 'USD'
  })
  currency!: string;

  @Column(DataType.JSONB)
  proofLinks!: any; // array of URLs

  @Column(DataType.JSONB)
  attachments!: any; // array of file paths

  @Column(DataType.BIGINT)
  createdBy!: number;

  @BelongsTo(() => Application)
  application!: Application;
} 