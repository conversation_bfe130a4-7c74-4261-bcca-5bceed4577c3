import { Table, Column, DataType, Foreign<PERSON>ey, BelongsTo } from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { Application } from './application.model';

export enum ApplicationStageStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

@Table({ tableName: 'application_stages' })
export class ApplicationStage extends BaseModel {
  @ForeignKey(() => Application)
  @Column(DataType.BIGINT)
  applicationId!: number;

  @Column({
    type: DataType.STRING,
    allowNull: false
  })
  stageName!: string; // collecting_documents, submitted, application, review, interview, offer_letter, visa_application, enrollment

  @Column({
    type: DataType.INTEGER,
    allowNull: false
  })
  stageOrder!: number;

  @Column({
    type: DataType.ENUM(...Object.values(ApplicationStageStatus)),
    allowNull: false,
    defaultValue: ApplicationStageStatus.PENDING
  })
  status!: ApplicationStageStatus;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: 0
  })
  progressPercentage!: number;

  @Column(DataType.DATE)
  startDate!: Date;

  @Column(DataType.DATE)
  completedDate!: Date;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: 0
  })
  requirementsMet!: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: 0
  })
  totalRequirements!: number;

  @BelongsTo(() => Application)
  application!: Application;
} 