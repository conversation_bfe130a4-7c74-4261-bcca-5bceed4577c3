import { Table, Column, DataType, Foreign<PERSON>ey, BelongsTo } from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { Application } from './application.model';

export enum PaymentStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  FAILED = 'failed',
  REFUNDED = 'refunded'
}

@Table({ tableName: 'application_payments' })
export class ApplicationPayment extends BaseModel {
  @ForeignKey(() => Application)
  @Column(DataType.BIGINT)
  applicationId!: number;

  @Column({
    type: DataType.STRING,
    allowNull: false
  })
  paymentType!: string; // application_fee, tuition_deposit, visa_fee, etc.

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false
  })
  amount!: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    defaultValue: 'USD'
  })
  currency!: string;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  paymentMethod!: string; // cash, bank_transfer, western_union, check, etc.

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  transactionReference!: string; // Manual reference number

  @Column({
    type: DataType.ENUM(...Object.values(PaymentStatus)),
    allowNull: false,
    defaultValue: PaymentStatus.PENDING
  })
  status!: PaymentStatus;

  @Column(DataType.DATE)
  paymentDate!: Date;

  @Column(DataType.DATE)
  confirmedAt!: Date;

  @Column(DataType.BIGINT)
  confirmedBy!: number; // Admin who confirmed the payment

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  receiptPath!: string; // Uploaded receipt file

  @Column(DataType.TEXT)
  notes!: string;

  @BelongsTo(() => Application)
  application!: Application;
} 