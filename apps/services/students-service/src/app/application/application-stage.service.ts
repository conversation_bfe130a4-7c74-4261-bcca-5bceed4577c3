import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { ApplicationStage, ApplicationStageStatus } from './application-stage.model';
import { Application } from './application.model';
import { BaseRepository } from '@apply-goal-backend/database';

export interface CreateApplicationStageDto {
  applicationId: number;
  stageName: string;
  stageOrder: number;
  status?: ApplicationStageStatus;
  progressPercentage?: number;
  startDate?: Date;
  requirementsMet?: number;
  totalRequirements?: number;
}

export interface UpdateApplicationStageDto {
  stageName?: string;
  stageOrder?: number;
  status?: ApplicationStageStatus;
  progressPercentage?: number;
  startDate?: Date;
  completedDate?: Date;
  requirementsMet?: number;
  totalRequirements?: number;
}

@Injectable()
export class ApplicationStageService {
  private applicationStageRepository: BaseRepository<ApplicationStage>;
  private readonly logger = new Logger(ApplicationStageService.name);

  constructor(
    @InjectModel(ApplicationStage)
    private applicationStageModel: typeof ApplicationStage
  ) {
    this.applicationStageRepository = new BaseRepository<ApplicationStage>(
      applicationStageModel
    );
  }

  async createStage(createStageDto: CreateApplicationStageDto): Promise<ApplicationStage> {
    try {
      this.logger.log(
        `Creating stage ${createStageDto.stageName} for application: ${createStageDto.applicationId}`
      );

      const stage = await this.applicationStageRepository.create({
        ...createStageDto,
        status: createStageDto.status || ApplicationStageStatus.PENDING,
        progressPercentage: createStageDto.progressPercentage || 0,
        requirementsMet: createStageDto.requirementsMet || 0,
        totalRequirements: createStageDto.totalRequirements || 0
      });

      this.logger.log(`Stage created successfully with ID: ${stage.id}`);
      return stage;
    } catch (error) {
      this.logger.error(`Failed to create stage: ${error.message}`);
      throw error;
    }
  }

  async findStagesByApplicationId(applicationId: number): Promise<ApplicationStage[]> {
    const stages = await this.applicationStageRepository.findAll({
      where: { applicationId },
      order: [['stageOrder', 'ASC']]
    });

    return stages;
  }

  async updateStage(
    id: number,
    updateStageDto: UpdateApplicationStageDto
  ): Promise<ApplicationStage> {
    const stage = await this.applicationStageRepository.findById(id.toString());
    if (!stage) {
      throw new Error(`Stage with ID ${id} not found`);
    }

    await stage.update(updateStageDto);
    return stage;
  }

  async updateStageProgress(
    applicationId: number,
    stageName: string,
    requirementsMet: number,
    totalRequirements: number
  ): Promise<ApplicationStage> {
    const stage = await this.applicationStageRepository.findOne({
      where: { applicationId, stageName }
    });

    if (!stage) {
      throw new Error(`Stage ${stageName} not found for application ${applicationId}`);
    }

    const progressPercentage = Math.round((requirementsMet / totalRequirements) * 100);
    const status = progressPercentage === 100 ? ApplicationStageStatus.COMPLETED : ApplicationStageStatus.IN_PROGRESS;

    await stage.update({
      requirementsMet,
      totalRequirements,
      progressPercentage,
      status,
      completedDate: progressPercentage === 100 ? new Date() : null
    });

    return stage;
  }

  async getApplicationProgress(applicationId: number): Promise<{
    totalStages: number;
    completedStages: number;
    overallProgress: number;
    currentStage: string;
    stages: ApplicationStage[];
  }> {
    const stages = await this.findStagesByApplicationId(applicationId);
    
    const totalStages = stages.length;
    const completedStages = stages.filter(stage => stage.status === ApplicationStageStatus.COMPLETED).length;
    const overallProgress = totalStages > 0 ? Math.round((completedStages / totalStages) * 100) : 0;
    
    const currentStage = stages.find(stage => 
      stage.status === ApplicationStageStatus.IN_PROGRESS || 
      stage.status === ApplicationStageStatus.PENDING
    )?.stageName || 'completed';

    return {
      totalStages,
      completedStages,
      overallProgress,
      currentStage,
      stages
    };
  }

  async initializeApplicationStages(applicationId: number): Promise<ApplicationStage[]> {
    const defaultStages = [
      { name: 'collecting_documents', order: 1 },
      { name: 'submitted', order: 2 },
      { name: 'application', order: 3 },
      { name: 'review', order: 4 },
      { name: 'interview', order: 5 },
      { name: 'offer_letter', order: 6 },
      { name: 'visa_application', order: 7 },
      { name: 'enrollment', order: 8 }
    ];

    const stages = [];
    for (const stageData of defaultStages) {
      const stage = await this.createStage({
        applicationId,
        stageName: stageData.name,
        stageOrder: stageData.order,
        status: ApplicationStageStatus.PENDING,
        progressPercentage: 0,
        requirementsMet: 0,
        totalRequirements: 0
      });
      stages.push(stage);
    }

    return stages;
  }
} 