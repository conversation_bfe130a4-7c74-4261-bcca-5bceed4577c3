import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { UploadService } from '@apply-goal-backend/common';
import {
  HtmlTemplateService,
  HtmlCalDocumentData,
  PdfTemplateService,
  CalDocumentData
} from '@apply-goal-backend/utils';
import { Application } from './application.model';

export type TemplateType = 'pdf' | 'html';

@Injectable()
export class CalDocumentService {
  private readonly logger = new Logger(CalDocumentService.name);

  constructor(
    private readonly uploadService: UploadService,
    private readonly htmlTemplateService: HtmlTemplateService,
    private readonly pdfTemplateService: PdfTemplateService,
    @InjectModel(Application)
    private readonly applicationModel: typeof Application
  ) {}

  async generateCalDocumentWithMinio(
    applicationData: any,
    templateType: TemplateType = 'html'
  ): Promise<string> {
    try {
      this.logger.log(
        `Generating CAL document for application ${applicationData.id} using ${templateType} templates`
      );

      // Prepare template data
      const templateData = await this.prepareTemplateData(applicationData);

      this.logger.debug(
        `${templateType} template data`,
        JSON.stringify(templateData, null, 2)
      );

      // Generate CAL document based on template type
      let result;
      if (templateType === 'html') {
        result = await this.generateCalDocumentFromHtmlTemplate(templateData);
      } else {
        result = await this.generateCalDocumentFromPdfTemplate(templateData);
      }

      if (!result.success) {
        throw new Error(result.error || 'Failed to generate CAL document');
      }

      // Upload to MinIO
      const filename = `CAL-Document-${applicationData.id}.pdf`;

      // Create a MulterFile-like object for the upload service
      const file = {
        fieldname: 'cal-document',
        originalname: filename,
        encoding: '7bit',
        mimetype: 'application/pdf',
        size: result.documentContent!.length,
        buffer: result.documentContent!
      };

      const uploadResult = await this.uploadService.uploadFile(file, 'file');

      this.logger.log(`Document uploaded to MinIO: ${uploadResult.url}`);

      return uploadResult.url;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(`Error generating CAL document: ${errorMessage}`);
      throw error;
    }
  }

  private async generateCalDocumentFromHtmlTemplate(
    templateData: HtmlCalDocumentData
  ): Promise<any> {
    this.logger.log(
      `Generating CAL document from HTML template for application ${templateData.applicationId}`
    );

    try {
      const result = await this.htmlTemplateService.generateCalDocument(
        templateData
      );

      if (result.success) {
        this.logger.log(
          `HTML template generation successful, size: ${result.documentContent?.length} bytes`
        );
      } else {
        this.logger.error(`HTML template generation failed: ${result.error}`);
      }

      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(`Error in HTML template generation: ${errorMessage}`);
      throw error;
    }
  }

  private async generateCalDocumentFromPdfTemplate(
    templateData: CalDocumentData
  ): Promise<any> {
    this.logger.log(
      `Generating CAL document from PDF template for application ${templateData.applicationId}`
    );

    try {
      const result = await this.pdfTemplateService.generateCalDocument(
        templateData
      );

      if (result.success) {
        this.logger.log(
          `PDF template generation successful, size: ${result.documentContent?.length} bytes`
        );
      } else {
        this.logger.error(`PDF template generation failed: ${result.error}`);
      }

      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(`Error in PDF template generation: ${errorMessage}`);
      throw error;
    }
  }

  private async prepareTemplateData(applicationData: any): Promise<any> {
    const now = new Date();

    // Generate and save invoice number if not exists
    let invoiceNumber = applicationData.invoiceId;
    if (!invoiceNumber) {
      invoiceNumber = await this.generateAndSaveInvoiceNumber(
        applicationData.id
      );
      this.logger.log(
        `Generated new invoice number: ${invoiceNumber} for application ${applicationData.id}`
      );
    } else {
      this.logger.log(
        `Using existing invoice number: ${invoiceNumber} for application ${applicationData.id}`
      );
    }

    // Extract student information
    const student = applicationData.student || {};
    const studentPersonalInfo = applicationData.studentPersonalInfo || {};
    const course = applicationData.course || {};
    const program = applicationData.program || {};
    const intake = applicationData.intake || {};
    const campus = applicationData.campus || {};
    const university = applicationData.university || {};
    const fees =
      applicationData.fees?.length > 0 ? applicationData.fees[0] : [];

    // Log the raw data being extracted
    this.logger.debug('Raw application data:', {
      applicationId: applicationData.id,
      studentId: applicationData.studentId,
      student: student,
      studentPersonalInfo: studentPersonalInfo,
      course: course,
      program: program,
      intake: intake,
      campus: campus,
      university: university,
      fees: fees
    });

    // Format dates properly
    const formatDate = (date: any): string => {
      if (!date) return 'N/A';
      const d = new Date(date);
      if (isNaN(d.getTime())) return 'N/A';
      return d.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    };

    // Format currency properly
    const formatCurrency = (amount: any): string => {
      if (!amount || isNaN(amount)) return 'N/A';
      return `$${parseFloat(amount).toFixed(2)}`;
    };

    // Get student name (prefer personal info, fallback to student data)
    const getStudentName = (): string => {
      if (studentPersonalInfo.firstName && studentPersonalInfo.lastName) {
        return `${studentPersonalInfo.firstName} ${studentPersonalInfo.lastName}`;
      }
      if (studentPersonalInfo.firstName) return studentPersonalInfo.firstName;
      if (studentPersonalInfo.lastName) return studentPersonalInfo.lastName;
      if (student.fullName) return student.fullName;
      if (student.firstName && student.lastName) {
        return `${student.firstName} ${student.lastName}`;
      }
      if (student.firstName) return student.firstName;
      if (student.lastName) return student.lastName;
      return 'N/A';
    };

    // Get course name (prefer course name, fallback to program name)
    const getCourseName = (): string => {
      if (course.courseTitle) return course.courseTitle;
      if (course.name) return course.name;
      if (course.id) return `Course ${course.id}`;
      if (applicationData.courseId) return `Course ${applicationData.courseId}`;
      return 'N/A';
    };

    const getProgramName = (): string => {
      if (program.programLevelName) return program.programLevelName;
      if (program.name) return program.name;
      if (program.id) return `Program ${program.id}`;
      if (applicationData.programId)
        return `Program ${applicationData.programId}`;
      return 'N/A';
    };

    // Get tuition fee (prefer course tuition, fallback to application totalAmount)
    const getTuitionFee = (): string => {
      if (fees?.tuitionFee) return formatCurrency(fees.tuitionFee);
      if (applicationData.totalAmount)
        return formatCurrency(applicationData.totalAmount);
      return 'N/A';
    };
    const totalAmount = (): string => {
      // Calculate total amount logic
      if (fees?.tuitionFee && applicationData.adminFee) {
        const total =
          parseFloat(fees.tuitionFee) + parseFloat(applicationData.adminFee);
        return formatCurrency(total);
      }
      if (fees?.tuitionFee) return formatCurrency(fees.tuitionFee);
      if (applicationData.totalAmount)
        return formatCurrency(applicationData.totalAmount);
      return 'N/A';
    };

    // Get course dates
    // Get course dates
    const getCourseStartDate = (): string => {
      // Get the date from intake.classStartDate, intake.startDate, or course.startDate
      const date =
        intake.classStartDate || intake.startDate || course.startDate;

      if (!date) return 'N/A';

      // Handle dates that are just month/day without year (like "September 2" or "August 5")
      if (
        typeof date === 'string' &&
        !date.includes(',') &&
        !date.match(/\d{4}/)
      ) {
        const currentYear = now.getFullYear(); // 2025
        const nextYear = currentYear + 1; // 2026

        // Always use next year for course start dates
        const d = new Date(`${date}, ${nextYear}`);

        if (!isNaN(d.getTime())) {
          return d.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          });
        }
      }

      // Handle normal date formats with year - force next year
      const d = new Date(date);
      if (!isNaN(d.getTime())) {
        const nextYear = now.getFullYear() + 1; // 2026
        d.setFullYear(nextYear);
        return d.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });
      }

      return 'N/A';
    };

    const getCourseEndDate = (): string => {
      // Get the date from course.endDate or intake.endDate
      const date = course.endDate || intake.endDate;

      if (!date) return 'N/A';

      // Handle dates that are just month/day without year (like "August 18")
      if (
        typeof date === 'string' &&
        !date.includes(',') &&
        !date.match(/\d{4}/)
      ) {
        const currentYear = now.getFullYear(); // 2025
        const endYear = currentYear + 2; // 2027 (add 2 years)

        // Parse with the end year (current year + 2)
        const d = new Date(`${date}, ${endYear}`);

        if (!isNaN(d.getTime())) {
          return d.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          });
        }
      }

      // Handle normal date formats with year - still add 2 years
      const d = new Date(date);
      if (!isNaN(d.getTime())) {
        const endYear = now.getFullYear() + 2;
        d.setFullYear(endYear);
        return d.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });
      }

      return 'N/A';
    };

    const baseData = {
      applicationId: applicationData.id,
      studentName: getStudentName(),
      idNumber:
        applicationData.applicationId || applicationData.idNumber || 'N/A',
      passport: studentPersonalInfo.passport || 'N/A',
      dateOfBirth: formatDate(studentPersonalInfo.dateOfBirth),
      courseName: getCourseName(),
      programName: getProgramName(),
      courseDate: new Date().getFullYear() + 1,
      intake: intake.name || 'N/A',
      universityName: university.name || 'N/A',
      campusName: campus.campusName || university.name || 'N/A',
      tuitionFee: getTuitionFee(),
      totalAmount: totalAmount(),
      courseStartDate: getCourseStartDate(),
      courseEndDate: getCourseEndDate(),
      issueDate: now.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }),
      invoiceId: invoiceNumber || 'N/A',
      invoiceDate:
        new Date().toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        }) || 'N/A',
      adminFee: applicationData.adminFee || '$0'
    };

    // Log the processed template data
    this.logger.debug('Processed template data:', baseData);

    // Return data that works for both HTML and PDF templates
    return {
      ...baseData,
      // Additional fields for HTML templates
      ...baseData
    };
  }

  // Generate a unique invoice number and save it to the application
  private async generateAndSaveInvoiceNumber(
    applicationId: number | bigint
  ): Promise<string> {
    try {
      // Generate invoice number with format: INV-YYYY-MM-DD-XXXXXX
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');

      // Generate a random 6-digit number
      const randomNumber = Math.floor(100000 + Math.random() * 900000);
      const invoiceNumber = `INV-${year}-${month}-${day}-${randomNumber}`;

      // Update the application with the invoice number
      await this.applicationModel.update(
        { invoiceId: invoiceNumber },
        { where: { id: applicationId } }
      );

      this.logger.log(
        `Invoice number ${invoiceNumber} saved to application ${applicationId}`
      );
      return invoiceNumber;
    } catch (error) {
      this.logger.error(
        `Error generating invoice number for application ${applicationId}:`,
        error
      );
      // Return a fallback invoice number if database update fails
      const fallbackNumber = `INV-${Date.now()}`;
      this.logger.warn(`Using fallback invoice number: ${fallbackNumber}`);
      return fallbackNumber;
    }
  }
}
