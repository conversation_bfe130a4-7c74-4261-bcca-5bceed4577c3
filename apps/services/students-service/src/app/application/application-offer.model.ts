import { Table, Column, DataType, Foreign<PERSON>ey, BelongsTo } from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { Application } from './application.model';

export enum OfferStatus {
  PENDING = 'pending',
  ACCEPTED = 'accepted',
  DECLINED = 'declined',
  EXPIRED = 'expired'
}

@Table({ tableName: 'application_offers' })
export class ApplicationOffer extends BaseModel {
  @ForeignKey(() => Application)
  @Column(DataType.BIGINT)
  applicationId!: number;

  @Column({
    type: DataType.STRING,
    allowNull: false
  })
  offerType!: string; // conditional, unconditional, scholarship

  @Column({
    type: DataType.ENUM(...Object.values(OfferStatus)),
    allowNull: false,
    defaultValue: OfferStatus.PENDING
  })
  offerStatus!: OfferStatus;

  @Column(DataType.DATE)
  offerDate!: Date;

  @Column(DataType.DATE)
  acceptanceDeadline!: Date;

  @Column(DataType.DATE)
  acceptedAt!: Date;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  offerLetterPath!: string;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: true
  })
  scholarshipAmount!: number;

  @Column(DataType.TEXT)
  conditions!: string;

  @BelongsTo(() => Application)
  application!: Application;
} 