import { Table, Column, DataType, Foreign<PERSON>ey, BelongsTo } from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { Application } from './application.model';

@Table({ tableName: 'application_activities' })
export class ApplicationActivity extends BaseModel {
  @ForeignKey(() => Application)
  @Column(DataType.BIGINT)
  applicationId!: number;

  @Column({
    type: DataType.STRING,
    allowNull: false
  })
  activityType!: string; // status_change, document_upload, payment, communication

  @Column(DataType.TEXT)
  activityDescription!: string;

  @Column(DataType.BIGINT)
  performedBy!: number;

  @Column(DataType.DATE)
  performedAt!: Date;

  @Column(DataType.JSONB)
  metadata!: any;

  @BelongsTo(() => Application)
  application!: Application;
} 