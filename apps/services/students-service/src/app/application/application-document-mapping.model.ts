import { Table, Column, DataType, Foreign<PERSON>ey, BelongsTo } from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { Application } from './application.model';
import { ApplicationDocumentRequirement } from './application-document-requirement.model';

@Table({ tableName: 'application_document_mappings' })
export class ApplicationDocumentMapping extends BaseModel {
  @ForeignKey(() => Application)
  @Column(DataType.BIGINT)
  applicationId!: number;

  @ForeignKey(() => ApplicationDocumentRequirement)
  @Column(DataType.BIGINT)
  requirementId!: number;

  @Column(DataType.BIGINT)
  studentDocumentId!: number; // References existing student.document model

  @Column(DataType.DATE)
  mappedAt!: Date;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false
  })
  isVerified!: boolean;

  @Column(DataType.DATE)
  verifiedAt!: Date;

  @Column(DataType.BIGINT)
  verifiedBy!: number;

  @Column(DataType.TEXT)
  verificationNotes!: string;

  @BelongsTo(() => Application)
  application!: Application;

  @BelongsTo(() => ApplicationDocumentRequirement)
  requirement!: ApplicationDocumentRequirement;
} 