import { Test, TestingModule } from '@nestjs/testing';
import { CalDocumentService } from './cal-document.service';
import { PdfConverterService } from '@apply-goal-backend/utils';

describe('CalDocumentService', () => {
  let service: CalDocumentService;
  let pdfConverter: PdfConverterService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CalDocumentService,
        {
          provide: PdfConverterService,
          useValue: {
            convertHtmlToPdf: jest.fn().mockResolvedValue(Buffer.from('test pdf')),
            convertHtmlStringToPdf: jest.fn().mockResolvedValue(Buffer.from('test pdf')),
            processHtmlTemplate: jest.fn().mockResolvedValue('<html>test</html>'),
            getAvailableTemplates: jest.fn().mockResolvedValue(['CAL-Page-01', 'CAL-Page-02'])
          }
        }
      ],
    }).compile();

    service = module.get<CalDocumentService>(CalDocumentService);
    pdfConverter = module.get<PdfConverterService>(PdfConverterService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getAvailableCalPages', () => {
    it('should return available CAL pages', async () => {
      const pages = await service.getAvailableCalPages();
      
      expect(Array.isArray(pages)).toBe(true);
      // Should include the existing CAL pages
      expect(pages.some(p => p.pageNumber === 1)).toBe(true);
      expect(pages.some(p => p.pageNumber === 2)).toBe(true);
      expect(pages.some(p => p.pageNumber === 3)).toBe(true);
      expect(pages.some(p => p.pageNumber === 4)).toBe(true);
    });
  });

  describe('generateCalDocument', () => {
    it('should generate PDF document', async () => {
      const applicationData = {
        id: 1,
        student: {
          fullName: 'John Doe',
          passport: 'AB123456',
          dateOfBirth: '1990-01-01'
        },
        applicationId: 'APP001',
        course: {
          name: 'Computer Science',
          startDate: '2024-09-01',
          endDate: '2028-05-31'
        },
        intake: {
          name: 'Fall 2024'
        },
        campus: {
          name: 'Los Angeles Campus'
        },
        totalAmount: 25000
      };

      const result = await service.generateCalDocument(1, applicationData, {
        outputFormat: 'pdf',
        includeAllPages: true
      });

      expect(result.success).toBe(true);
      expect(result.documentContent).toBeDefined();
      expect(result.filename).toContain('CAL-Document-1.pdf');
      expect(result.contentType).toBe('application/pdf');
    });

    it('should generate HTML document', async () => {
      const applicationData = {
        id: 1,
        student: {
          fullName: 'Jane Smith',
          passport: 'CD789012',
          dateOfBirth: '1995-05-15'
        },
        applicationId: 'APP002',
        course: {
          name: 'Business Administration',
          startDate: '2024-09-01',
          endDate: '2028-05-31'
        },
        intake: {
          name: 'Fall 2024'
        },
        campus: {
          name: 'Los Angeles Campus'
        },
        totalAmount: 30000
      };

      const result = await service.generateCalDocument(1, applicationData, {
        outputFormat: 'html',
        includeAllPages: true
      });

      expect(result.success).toBe(true);
      expect(result.documentContent).toBeDefined();
      expect(result.filename).toContain('CAL-Document-1.html');
      expect(result.contentType).toBe('text/html');
    });

    it('should generate document for specific pages', async () => {
      const applicationData = {
        id: 1,
        student: {
          fullName: 'Bob Johnson',
          passport: 'EF345678',
          dateOfBirth: '1992-12-25'
        },
        applicationId: 'APP003',
        course: {
          name: 'Engineering',
          startDate: '2024-09-01',
          endDate: '2028-05-31'
        },
        intake: {
          name: 'Fall 2024'
        },
        campus: {
          name: 'Los Angeles Campus'
        },
        totalAmount: 35000
      };

      const result = await service.generateCalDocument(1, applicationData, {
        outputFormat: 'pdf',
        includeAllPages: false,
        pageNumbers: [1, 2]
      });

      expect(result.success).toBe(true);
      expect(result.documentContent).toBeDefined();
    });
  });

  describe('getTemplateVariables', () => {
    it('should return template variables for a specific page', async () => {
      const variables = await service.getTemplateVariables(1);
      
      expect(Array.isArray(variables)).toBe(true);
      // Should include variables found in the template
      expect(variables).toContain('intake');
      expect(variables).toContain('name');
      expect(variables).toContain('idNumber');
    });
  });

  describe('validateTemplateData', () => {
    it('should validate template data correctly', () => {
      const templateData = {
        applicationId: 1,
        studentName: 'John Doe',
        idNumber: 'APP001',
        passport: 'AB123456',
        dateOfBirth: '1990-01-01',
        courseName: 'Computer Science',
        intake: 'Fall 2024',
        campusName: 'Los Angeles Campus',
        tuitionFee: '$25000',
        courseStartDate: '2024-09-01',
        courseEndDate: '2028-05-31',
        issueDate: 'January 15, 2024',
        name: 'John Doe'
      };

      const requiredVariables = ['name', 'idNumber', 'courseName', 'intake'];
      const validation = service.validateTemplateData(templateData, requiredVariables);

      expect(validation.isValid).toBe(true);
      expect(validation.missingVariables).toHaveLength(0);
    });

    it('should detect missing variables', () => {
      const templateData = {
        applicationId: 1,
        studentName: 'John Doe',
        idNumber: 'APP001',
        passport: 'AB123456',
        dateOfBirth: '1990-01-01',
        courseName: 'Computer Science',
        intake: 'Fall 2024',
        campusName: 'Los Angeles Campus',
        tuitionFee: '$25000',
        courseStartDate: '2024-09-01',
        courseEndDate: '2028-05-31',
        issueDate: 'January 15, 2024',
        name: 'John Doe'
      };

      const requiredVariables = ['name', 'idNumber', 'courseName', 'intake'];
      const validation = service.validateTemplateData(templateData, requiredVariables);

      expect(validation.isValid).toBe(false);
      expect(validation.missingVariables).toContain('courseName');
      expect(validation.missingVariables).toContain('intake');
    });
  });

  describe('prepareTemplateData', () => {
    it('should prepare template data from application data', async () => {
      const applicationData = {
        id: 1,
        student: {
          fullName: 'John Doe',
          passport: 'AB123456',
          dateOfBirth: '1990-01-01'
        },
        applicationId: 'APP001',
        course: {
          name: 'Computer Science',
          startDate: '2024-09-01',
          endDate: '2028-05-31'
        },
        intake: {
          name: 'Fall 2024'
        },
        campus: {
          name: 'Los Angeles Campus'
        },
        totalAmount: 25000
      };

      const result = await service.generateCalDocument(1, applicationData, {
        outputFormat: 'html'
      });

      expect(result.success).toBe(true);
      expect(result.documentContent).toContain('John Doe');
      expect(result.documentContent).toContain('APP001');
      expect(result.documentContent).toContain('Computer Science');
      expect(result.documentContent).toContain('Fall 2024');
    });
  });
}); 