import { <PERSON>, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import {
  ApplicationService,
  CreateApplicationDto,
  UpdateApplicationDto
} from './application.service';
import {
  Application,
  ApplicationStatus,
  PaymentStatus
} from './application.model';
import { CalDocumentService } from './cal-document.service';

@Controller()
export class ApplicationGrpcController {
  private readonly logger = new Logger(ApplicationGrpcController.name);

  constructor(
    private readonly applicationService: ApplicationService,
    private readonly calDocumentService: CalDocumentService
  ) {}

  // Helper method to safely convert gRPC ID to number
  private convertIdToNumber(id: any): number | bigint {
    if (id === null || id === undefined) {
      throw new Error('ID cannot be null or undefined');
    }

    if (typeof id === 'object' && 'low' in id) {
      // This is a gRPC Long object
      return BigInt(id.low);
    } else if (typeof id === 'string') {
      const parsed = parseInt(id, 10);
      if (isNaN(parsed)) {
        throw new Error(`Invalid ID string: ${id}`);
      }
      return parsed;
    } else if (typeof id === 'number') {
      return id;
    } else if (typeof id === 'bigint') {
      return id;
    }

    throw new Error(`Invalid ID type: ${typeof id}`);
  }

  // Helper method to process and validate update data
  private processUpdateData(updateData: any): UpdateApplicationDto {
    const processedData: any = {};

    this.logger.log('Processing update data with smart field detection...');

    // Process each field with proper validation and type conversion
    Object.keys(updateData).forEach((key) => {
      const value = updateData[key];

      // Smart field detection - only include fields that have meaningful values
      if (this.shouldIncludeField(key, value)) {
        // Handle specific field types
        switch (key) {
          case 'universityId':
          case 'universityCountryId':
          case 'universityCountryCampus':
          case 'programId':
          case 'intakeId':
          case 'courseId':
          case 'overallProgress':
          case 'applicationAcceptedByApplyGoalUserId': {
            // Only include ID fields if they have non-zero values
            const numericValue = this.convertIdToNumber(value);
            if (numericValue > 0) {
              processedData[key] = numericValue;
            }
            break;
          }

          case 'savisId':
            // Handle savisId - convert string to number if provided
            if (typeof value === 'string' && value.trim() !== '') {
              const numValue = parseInt(value.trim(), 10);
              if (!isNaN(numValue) && numValue > 0) {
                processedData[key] = numValue;
              }
            } else if (typeof value === 'number' && value > 0) {
              processedData[key] = value;
            }
            break;

          case 'totalAmount':
          case 'paidAmount':
          case 'refundAmount': {
            // Only include amount fields if they have non-zero values
            const numValue = parseFloat(value.toString());
            if (numValue > 0) {
              processedData[key] = numValue;
            }
            break;
          }

          case 'submissionDate':
          case 'deadlineDate':
          case 'applicationAcceptedByApplyGoalAt':
            // Handle date fields - convert string to Date if needed
            if (typeof value === 'string' && value.trim() !== '') {
              try {
                processedData[key] = new Date(value);
              } catch (error) {
                this.logger.warn(`Invalid date format for ${key}: ${value}`);
              }
            } else if (value instanceof Date) {
              processedData[key] = value;
            }
            break;

          case 'applicationAcceptedByApplyGoal':
            // Handle boolean fields - include both true and false values
            processedData[key] = Boolean(value);
            break;

          case 'status':
            // Validate status enum
            if (
              [
                'applied',
                'pending',
                'approved',
                'rejected',
                'withdrawn'
              ].includes(value)
            ) {
              processedData[key] = value;
            }
            break;

          case 'paymentStatus':
            // Validate payment status enum
            if (['paid', 'unpaid'].includes(value)) {
              processedData[key] = value;
            }
            break;

          case 'paymentInvoiceUrl':
          case 'paymentMethod':
            // Include payment fields even though they're not in the model
            // (they might be added later or handled differently)
            if (typeof value === 'string' && value.trim() !== '') {
              processedData[key] = value.trim();
              this.logger.log(`Including payment field ${key}: ${value}`);
            }
            break;

          case 'i20Url':
          case 'i94Url':
          case 'i797cUrl':
            // Handle immigration document URL fields
            if (typeof value === 'string' && value.trim() !== '') {
              processedData[key] = value.trim();
              this.logger.log(
                `Including immigration document URL ${key}: ${value}`
              );
            }
            break;

          case 'applicationId':
            // Handle applicationId field - preserve custom application IDs
            if (typeof value === 'string' && value.trim() !== '') {
              processedData[key] = value.trim();
              this.logger.log(
                `Including applicationId: ${value}`
              );
            }
            break;

          case 'id':
            // Skip the primary key ID field - it should not be updated
            this.logger.log(`Skipping primary key field: ${key}`);
            break;

          case 'createdBy':
            // Skip createdBy field - it should not be updated after creation
            this.logger.log(`Skipping createdBy field: ${key} (cannot be modified after creation)`);
            break;

          default:
            // For all other string fields, include as-is if not empty
            if (typeof value === 'string' && value.trim() !== '') {
              processedData[key] = value.trim();
            } else if (
              typeof value !== 'string' &&
              value !== null &&
              value !== undefined
            ) {
              processedData[key] = value;
            }
            break;
        }
      }
    });

    this.logger.log(
      `Fields to update: ${Object.keys(processedData).join(', ')}`
    );
    return processedData;
  }

  // Helper method to determine if a field should be included in the update
  private shouldIncludeField(key: string, value: any): boolean {
    // Always skip undefined and null
    if (value === undefined || value === null) {
      return false;
    }

    // Handle BigInt values - convert to number for comparison
    let numericValue = value;
    if (typeof value === 'bigint') {
      numericValue = Number(value);
    }

    // For string fields, skip empty strings (gRPC default)
    if (typeof value === 'string' && value.trim() === '') {
      return false;
    }

    // For number fields, we need to be more careful
    // Skip 0 values for ID fields (gRPC default), but allow 0 for amounts and progress
    const idFields = [
      'universityId',
      'universityCountryId',
      'universityCountryCampus',
      'programId',
      'intakeId',
      'courseId',
      'applicationAcceptedByApplyGoalUserId'
    ];

    if (idFields.includes(key) && numericValue === 0) {
      return false;
    }

    // For amount fields, allow 0 values (user might want to set amount to 0)
    const amountFields = [
      'totalAmount',
      'paidAmount',
      'refundAmount',
      'overallProgress'
    ];
    if (amountFields.includes(key)) {
      return true; // Include all amount values, even 0
    }

    // For boolean fields, include both true and false
    if (typeof value === 'boolean') {
      return true;
    }

    // For all other cases, include the field
    return true;
  }

  @GrpcMethod('StudentService', 'CreateApplication')
  async createApplication(request: { applications: CreateApplicationDto[] }) {
    const startTime = Date.now();
    try {
      this.logger.log('gRPC CreateApplication request');
      // 1. Validate application type
      const validApplicationTypes = [
        'F-1 initial',
        'Transfer',
        'Reinstatement',
        'Non F-1',
        'New Program',
        'Change of Status'
      ];

      // 2. Validate each application in the array
      for (let i = 0; i < request.applications.length; i++) {
        const createApplicationDto = request.applications[i];

        // Validate application type for each item
        if (!createApplicationDto.applicationType) {
          return {
            status: 'false',
            message: `Application type is required for item ${i + 1}`,
            data: [],
            error: {
              details: `Application type is required for item ${i + 1}`,
              itemIndex: i + 1
            }
          };
        }

        if (
          !validApplicationTypes.includes(createApplicationDto.applicationType)
        ) {
          return {
            status: 'false',
            message: `Invalid application type: ${
              createApplicationDto.applicationType
            } for item ${i + 1}. Valid types are: ${validApplicationTypes.join(
              ', '
            )}`,
            data: [],
            error: {
              details: `Invalid application type: ${
                createApplicationDto.applicationType
              } for item ${i + 1}`,
              itemIndex: i + 1,
              validTypes: validApplicationTypes
            }
          };
        }

        const lastAcademiceLevel =
          await this.applicationService.validateCourseRequirements(
            createApplicationDto.courseId,
            createApplicationDto.programId
          );
          console.log("LastAcademice Record",JSON.stringify(lastAcademiceLevel))

          this.logger.log(
            `Last Academic Level for course ${createApplicationDto.courseId} and program ${createApplicationDto.programId} is ${lastAcademiceLevel}`
          );
        if (!lastAcademiceLevel) {
          return {
            status: 'false',
            message: `Failed to validate course requirements for item ${i + 1}.LastAcademice Record not added in course Data.`,
            data: [],
            error: {
              details: `Failed to validate course requirements for item ${
                i + 1
              }`,
              itemIndex: i + 1
            }
          };
        }
      }

      const createdApplications: Application[] = [];

      for (const item of request.applications) {
        const app = await this.applicationService.createApplication(item);
        createdApplications.push(app);
      }

      const result = createdApplications.map((application) => ({
        id: application.id,
        studentId: application.studentId,
        universityId: application.universityId,
        universityCountryId: application.universityCountryId,
        universityCountryCampus: application.universityCountryCampus,
        programId: application.programId,
        intakeId: application.intakeId,
        courseId: application.courseId,
        note: application.note,
        status: application.status,
        paymentStatus: application.paymentStatus,
        applicationType: application.applicationType,
        applicationId: application.applicationId,
        currentStage: application.currentStage,
        overallProgress: application.overallProgress,
        totalAmount: application.totalAmount,
        paidAmount: application.paidAmount,
        refundAmount: application.refundAmount,
        deliveryMethod: application.deliveryMethod,
        // Payment-related fields
        paymentInvoiceUrl: application.paymentInvoiceUrl || '',
        paymentMethod: application.paymentMethod || '',
        submissionDate: application.submissionDate?.toISOString(),
        deadlineDate: application.deadlineDate?.toISOString(),
        appliedAt: application.appliedAt.toISOString(),
        updatedAt: application.updatedAt.toISOString(),
        createdAt: application.createdAt.toISOString(),
        // ApplyGoal acceptance fields
        applicationAcceptedByApplyGoal:
          application.applicationAcceptedByApplyGoal,
        applicationAcceptedByApplyGoalAt:
          application.applicationAcceptedByApplyGoalAt?.toISOString(),
        applicationAcceptedByApplyGoalUserId:
          application.applicationAcceptedByApplyGoalUserId,
        // Created by field
        createdBy: application.createdBy || null
      }));

      return {
        status: 200,
        message: `${result.length} applications created successfully`,
        data: result,
        error: null
      };
    } catch (error) {
      this.logger.error(`Failed to create application: ${error.message}`);
      return {
        status: error?.response?.statusCode || 500,
        message: error?.response?.message || 'Failed to create application',
        data: [],
        error: {
          details: error.message
        }
      };
    }
  }

  @GrpcMethod('StudentService', 'GetApplication')
  async getApplication(request: { id: number | bigint }) {
    const startTime = Date.now();
    try {
      this.logger.log('gRPC GetApplication request');

      const numericId = this.convertIdToNumber(request.id);

      this.logger.log(`Converted ID: ${numericId}, Type: ${typeof numericId}`);

      const application = await this.applicationService.findOne(numericId);

      if (!application) {
        throw new Error(`Application with ID ${numericId} not found`);
      }

      return {
        status: 200,
        message: 'Application retrieved successfully',
        data: [{
          id: application.id,
          studentId: application.studentId,
          universityId: application.universityId,
          universityCountryId: application.universityCountryId,
          universityCountryCampus: application.universityCountryCampus,
          programId: application.programId,
          intakeId: application.intakeId,
          courseId: application.courseId,
          note: application.note,
          status: application.status,
          paymentStatus: application.paymentStatus,
          applicationType: application.applicationType,
          // Enhanced fields
          applicationId: application.applicationId,
          currentStage: application.currentStage,
          overallProgress: application.overallProgress,
          totalAmount: application.totalAmount,
          paidAmount: application.paidAmount,
          refundAmount: application.refundAmount,
          deliveryMethod: application.deliveryMethod,
          // Payment-related fields
          paymentInvoiceUrl: application.paymentInvoiceUrl || '',
          paymentMethod: application.paymentMethod || '',
          submissionDate: application.submissionDate?.toISOString(),
          deadlineDate: application.deadlineDate?.toISOString(),
          appliedAt: application.appliedAt.toISOString(),
          updatedAt: application.updatedAt.toISOString(),
          createdAt: application.createdAt.toISOString(),
          // ApplyGoal acceptance fields
          applicationAcceptedByApplyGoal:
            application.applicationAcceptedByApplyGoal,
          applicationAcceptedByApplyGoalAt:
            application.applicationAcceptedByApplyGoalAt?.toISOString(),
          applicationAcceptedByApplyGoalUserId:
            application.applicationAcceptedByApplyGoalUserId,
          // Immigration document fields
          savisId: application.savisId?.toString() || '',
          i20Url: application.i20Url || '',
          i94Url: application.i94Url || '',
          i797cUrl: application.i797cUrl || '',
          calUrl: application.calUrl || '',
          admUrl: application.admUrl || '',
          apUrl: application.apUrl || '',
          // AP approval fields
          isApApproved: application.isApApproved,
          apRejectionNotes: application.apRejectionNotes || '',
          // Created by field
          createdBy: application.createdBy || null
        }],
        error: null
      };
    } catch (error) {
      this.logger.error(`Failed to get application: ${error.message}`);
      return {
        status: error?.response?.statusCode || 500,
        message: error?.response?.message || 'Failed to get application',
        data: [],
        error: {
          details: error.message
        }
      };
    }
  }

  @GrpcMethod('StudentService', 'GetApplicationsByStudent')
  async getApplicationsByStudent(request: { studentId: string }) {
    const startTime = Date.now();
    try {
      this.logger.log('gRPC GetApplicationsByStudent request');

      const applications = await this.applicationService.findByStudentId(
        request.studentId
      );

      return {
        status: 200,
        message: 'Applications retrieved successfully',
        data: {
          applications: applications.map((app) => ({
            id: app.id,
            studentId: app.studentId,
            universityId: app.universityId,
            universityCountryId: app.universityCountryId,
            universityCountryCampus: app.universityCountryCampus,
            programId: app.programId,
            intakeId: app.intakeId,
            courseId: app.courseId,
            note: app.note,
            status: app.status,
            paymentStatus: app.paymentStatus,
            applicationType: app.applicationType,
            // Enhanced fields
            applicationId: app.applicationId,
            currentStage: app.currentStage,
            overallProgress: app.overallProgress,
            totalAmount: app.totalAmount,
            paidAmount: app.paidAmount,
            refundAmount: app.refundAmount,
            deliveryMethod: app.deliveryMethod,
            // Payment-related fields
            paymentInvoiceUrl: app.paymentInvoiceUrl || '',
            paymentMethod: app.paymentMethod || '',
            submissionDate: app.submissionDate?.toISOString(),
            deadlineDate: app.deadlineDate?.toISOString(),
            appliedAt: app.appliedAt.toISOString(),
            updatedAt: app.updatedAt.toISOString(),
            createdAt: app.createdAt.toISOString(),
            // ApplyGoal acceptance fields
            applicationAcceptedByApplyGoal: app.applicationAcceptedByApplyGoal,
            applicationAcceptedByApplyGoalAt:
              app.applicationAcceptedByApplyGoalAt?.toISOString(),
            applicationAcceptedByApplyGoalUserId:
              app.applicationAcceptedByApplyGoalUserId,
            // Immigration document fields
            savisId: app.savisId?.toString() || '',
            i20Url: app.i20Url || '',
            studentI20Url: app.studentI20Url || '',
            i94Url: app.i94Url || '',
            i797cUrl: app.i797cUrl || '',
            // AP approval fields
            isApApproved: app.isApApproved || false,
            apRejectionNotes: app.apRejectionNotes || '',
            // Document status fields
            studentI20UrlStatus: app.studentI20UrlStatus || 'requested',
            i94UrlStatus: app.i94UrlStatus || 'requested',
            i797cUrlStatus: app.i797cUrlStatus || 'requested',
            // Created by field
            createdBy: app.createdBy || null
          }))
        },
        error: null
      };
    } catch (error) {
      this.logger.error(
        `Failed to get applications by student: ${error.message}`
      );
      return {
        status: error?.response?.statusCode || 500,
        message:
          error?.response?.message || 'Failed to get applications by student',
        data: null,
        error: {
          details: error.message
        }
      };
    }
  }

  @GrpcMethod('StudentService', 'GetApplicationsByOfficer')
  async getApplicationsByOfficer(request: { createdBy: number | bigint }) {
    const startTime = Date.now();
    try {
      this.logger.log('gRPC GetApplicationsByOfficer request');

      // Convert the createdBy ID to number
      const numericCreatedBy = this.convertIdToNumber(request.createdBy);

      const applications = await this.applicationService.findByCreatedBy(
        Number(numericCreatedBy)
      );

      return {
        status: 200,
        message: `Found ${applications.length} applications created by officer ${numericCreatedBy}`,
        data: {
          applications: applications.map((app) => ({
            id: app.id,
            studentId: app.studentId,
            universityId: app.universityId,
            universityCountryId: app.universityCountryId,
            universityCountryCampus: app.universityCountryCampus,
            programId: app.programId,
            intakeId: app.intakeId,
            courseId: app.courseId,
            note: app.note,
            status: app.status,
            paymentStatus: app.paymentStatus,
            applicationType: app.applicationType,
            // Enhanced fields
            applicationId: app.applicationId,
            currentStage: app.currentStage,
            overallProgress: app.overallProgress,
            totalAmount: app.totalAmount,
            paidAmount: app.paidAmount,
            refundAmount: app.refundAmount,
            deliveryMethod: app.deliveryMethod,
            // Payment-related fields
            paymentInvoiceUrl: app.paymentInvoiceUrl || '',
            paymentMethod: app.paymentMethod || '',
            submissionDate: app.submissionDate?.toISOString(),
            deadlineDate: app.deadlineDate?.toISOString(),
            appliedAt: app.appliedAt.toISOString(),
            updatedAt: app.updatedAt.toISOString(),
            createdAt: app.createdAt.toISOString(),
            // ApplyGoal acceptance fields
            applicationAcceptedByApplyGoal: app.applicationAcceptedByApplyGoal,
            applicationAcceptedByApplyGoalAt:
              app.applicationAcceptedByApplyGoalAt?.toISOString(),
            applicationAcceptedByApplyGoalUserId:
              app.applicationAcceptedByApplyGoalUserId,
            // Immigration document fields
            savisId: app.savisId?.toString() || '',
            i20Url: app.i20Url || '',
            studentI20Url: app.studentI20Url || '',
            i94Url: app.i94Url || '',
            i797cUrl: app.i797cUrl || '',
            calUrl: app.calUrl || '',
            admUrl: app.admUrl || '',
            apUrl: app.apUrl || '',
            // AP approval fields
            isApApproved: app.isApApproved || false,
            apRejectionNotes: app.apRejectionNotes || '',
            // Document status fields
            studentI20UrlStatus: app.studentI20UrlStatus || 'requested',
            i94UrlStatus: app.i94UrlStatus || 'requested',
            i797cUrlStatus: app.i797cUrlStatus || 'requested',
            // Created by field
            createdBy: app.createdBy || null
          }))
        },
        error: null
      };
    } catch (error) {
      this.logger.error(
        `Failed to get applications by officer: ${error.message}`
      );
      return {
        status: error?.response?.statusCode || 500,
        message:
          error?.response?.message || 'Failed to get applications by officer',
        data: null,
        error: {
          details: error.message
        }
      };
    }
  }

  @GrpcMethod('StudentService', 'UpdateApplication')
  async updateApplication(request: UpdateApplicationDto) {
    const startTime = Date.now();
    try {
      this.logger.log('gRPC UpdateApplication request');
      this.logger.log('Request object:', this.safeStringify(request));

      // Validate required ID field
      if (!request.id) {
        throw new Error('Application ID is required for update');
      }

      // Extract the ID from the request
      const numericId = this.convertIdToNumber(request.id);

      this.logger.log(`Converted ID: ${numericId}, Type: ${typeof numericId}`);

      // Create update DTO by removing the id field
      const { id, ...updateData } = request;

      // Process and validate update data
      const processedUpdateData = this.processUpdateData(updateData);
      this.logger.log(
        `Processed update data: ${this.safeStringify(processedUpdateData)}`
      );

      // Perform the update
      const application = await this.applicationService.update(
        numericId,
        processedUpdateData
      );

      const executionTime = Date.now() - startTime;
      this.logger.log(`UpdateApplication completed in ${executionTime}ms`);

      return {
        status: 200,
        message: 'Application updated successfully',
        data: [
          {
            id: application.id,
            studentId: application.studentId,
            universityId: application.universityId,
            universityCountryId: application.universityCountryId,
            universityCountryCampus: application.universityCountryCampus,
            programId: application.programId,
            intakeId: application.intakeId,
            courseId: application.courseId,
            note: application.note,
            status: application.status,
            paymentStatus: application.paymentStatus,
            applicationType: application.applicationType,
            // Enhanced fields
            applicationId: application.applicationId,
            currentStage: application.currentStage,
            overallProgress: application.overallProgress,
            totalAmount: application.totalAmount,
            paidAmount: application.paidAmount,
            refundAmount: application.refundAmount,
            deliveryMethod: application.deliveryMethod,
            submissionDate: application.submissionDate?.toISOString(),
            deadlineDate: application.deadlineDate?.toISOString(),
            // Payment-related fields
            paymentInvoiceUrl: application.paymentInvoiceUrl,
            paymentMethod: application.paymentMethod,
            appliedAt: application.appliedAt?.toISOString(),
            updatedAt: application.updatedAt.toISOString(),
            createdAt: application.createdAt.toISOString(),
            // ApplyGoal acceptance fields
            applicationAcceptedByApplyGoal:
              application.applicationAcceptedByApplyGoal || false,
            applicationAcceptedByApplyGoalAt:
              application.applicationAcceptedByApplyGoalAt?.toISOString(),
            applicationAcceptedByApplyGoalUserId:
              application.applicationAcceptedByApplyGoalUserId,
            // Immigration document fields
            savisId: application.savisId?.toString() || '',
            i20Url: application.i20Url || '',
            studentI20Url: application.studentI20Url || '',
            i94Url: application.i94Url || '',
            i797cUrl: application.i797cUrl || '',
            // Document status fields
            studentI20UrlStatus: application.studentI20UrlStatus || 'requested',
            i94UrlStatus: application.i94UrlStatus || 'requested',
            i797cUrlStatus: application.i797cUrlStatus || 'requested',
            // Created by field
            createdBy: application.createdBy || null
          }
        ],
        error: null
      };
    } catch (error) {
      const executionTime = Date.now() - startTime;
      this.logger.error(
        `UpdateApplication failed in ${executionTime}ms: ${error.message}`
      );
      this.logger.error('Error stack:', error.stack);

      return {
        status: error?.response?.statusCode || 500,
        message: error?.response?.message || 'Failed to update application',
        data: [],
        error: {
          details: error.message,
          stack:
            process.env.NODE_ENV === 'development' ? error.stack : undefined
        }
      };
    }
  }

  @GrpcMethod('StudentService', 'DeleteApplication')
  async deleteApplication(request: { id: number | bigint }) {
    const startTime = Date.now();
    try {
      this.logger.log('gRPC DeleteApplication request');

      const numericId = this.convertIdToNumber(request.id);

      await this.applicationService.remove(numericId);

      return {
        status: 200,
        message: 'Application deleted successfully',
        data: null,
        error: null
      };
    } catch (error) {
      this.logger.error(`Failed to delete application: ${error.message}`);
      return {
        status: error?.response?.statusCode || 500,
        message: error?.response?.message || 'Failed to delete application',
        data: null,
        error: {
          details: error.message
        }
      };
    }
  }

  @GrpcMethod('StudentService', 'ListApplications')
  async listApplications(request: {
    page?: number;
    limit?: number;
    studentId?: string;
    status?: ApplicationStatus;
    paymentStatus?: PaymentStatus;
    universityId?: number;
    programId?: number;
    intakeId?: number;
    courseId?: number;
    campusId?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }) {
    const startTime = Date.now();
    try {
      this.logger.log('gRPC ListApplications request');

      const result = await this.applicationService.findAll(request);

      return {
        status: 200,
        message: 'Applications retrieved successfully',
        data: {
          applications: result.rows.map((app) => ({
            id: app.id,
            studentId: app.studentId,
            universityId: app.universityId,
            universityCountryId: app.universityCountryId,
            universityCountryCampus: app.universityCountryCampus,
            programId: app.programId,
            intakeId: app.intakeId,
            courseId: app.courseId,
            note: app.note,
            status: app.status,
            paymentStatus: app.paymentStatus,
            applicationType: app.applicationType,
            // Enhanced fields
            applicationId: app.applicationId,
            currentStage: app.currentStage,
            overallProgress: app.overallProgress,
            totalAmount: app.totalAmount,
            paidAmount: app.paidAmount,
            refundAmount: app.refundAmount,
            deliveryMethod: app.deliveryMethod,
            // Payment-related fields
            paymentInvoiceUrl: app.paymentInvoiceUrl || '',
            paymentMethod: app.paymentMethod || '',
            submissionDate: app.submissionDate?.toISOString(),
            deadlineDate: app.deadlineDate?.toISOString(),
            appliedAt: app.appliedAt.toISOString(),
            updatedAt: app.updatedAt.toISOString(),
            createdAt: app.createdAt.toISOString(),
            // ApplyGoal acceptance fields
            applicationAcceptedByApplyGoal: app.applicationAcceptedByApplyGoal,
            applicationAcceptedByApplyGoalAt:
              app.applicationAcceptedByApplyGoalAt?.toISOString(),
            applicationAcceptedByApplyGoalUserId:
              app.applicationAcceptedByApplyGoalUserId,
            // Immigration document fields
            savisId: app.savisId?.toString() || '',
            i20Url: app.i20Url || '',
            studentI20Url: app.studentI20Url || '',
            i94Url: app.i94Url || '',
            i797cUrl: app.i797cUrl || '',
            calUrl: app.calUrl || '',
            admUrl: app.admUrl || '',
            apUrl: app.apUrl || '',
            // AP approval fields
            isApApproved: app.isApApproved || false,
            apRejectionNotes: app.apRejectionNotes || '',
            // Document status fields
            studentI20UrlStatus: app.studentI20UrlStatus || 'requested',
            i94UrlStatus: app.i94UrlStatus || 'requested',
            i797cUrlStatus: app.i797cUrlStatus || 'requested',
            // Created by field
            createdBy: app.createdBy || null
          })),
          total: result.count,
          page: request.page || 1,
          limit: request.limit || 10
        },
        error: null
      };
    } catch (error) {
      this.logger.error(`Failed to list applications: ${error.message}`);
      return {
        status: error?.response?.statusCode || 500,
        message: error?.response?.message || 'Failed to list applications',
        data: null,
        error: {
          details: error.message
        }
      };
    }
  }

  @GrpcMethod('StudentService', 'CreateApplicationRequirements')
  async createApplicationRequirements(request: {
    applicationId: number | bigint;
    items: Array<{
      documentTitle: string;
      documentName: string;
      url?: string;
      documentDescription?: string;
      isRequired?: boolean;
      allowedFormats?: string[];
      requiredByDate?: string;
      isNew?: boolean;
    }>;
    studentId?: string;
  }) {
    try {
      this.logger.log(
        `gRPC CreateApplicationRequirements request for application: ${request.applicationId}`
      );
      return await this.applicationService.createApplicationRequirements(
        request.applicationId,
        request.items,
        request.studentId
      );
    } catch (error) {
      this.logger.error(
        `Failed to create application requirements: ${error.message}`
      );
      return {
        status: error?.response?.statusCode || 500,
        message:
          error?.response?.message ||
          'Failed to create application requirements',
        data: null,
        error: { details: error.message }
      };
    }
  }

  @GrpcMethod('StudentService', 'GetApplicationProgress')
  async getApplicationProgress(request: { id: number | bigint }) {
    const startTime = Date.now();
    try {
      this.logger.log('gRPC GetApplicationProgress request');

      const numericId = this.convertIdToNumber(request.id);
      const result = await this.applicationService.getApplicationProgress(
        numericId
      );

      return {
        status: 200,
        message: 'Application progress retrieved successfully',
        data: {
          application: {
            id: result.application.id,
            studentId: result.application.studentId,
            universityId: result.application.universityId,
            universityCountryId: result.application.universityCountryId,
            universityCountryCampus: result.application.universityCountryCampus,
            programId: result.application.programId,
            intakeId: result.application.intakeId,
            courseId: result.application.courseId,
            note: result.application.note,
            status: result.application.status,
            paymentStatus: result.application.paymentStatus,
            applicationType: result.application.applicationType,
            applicationId: result.application.applicationId,
            currentStage: result.application.currentStage,
            overallProgress: result.application.overallProgress,
            totalAmount: result.application.totalAmount,
            paidAmount: result.application.paidAmount,
            refundAmount: result.application.refundAmount,
            deliveryMethod: result.application.deliveryMethod,
            submissionDate: result.application.submissionDate?.toISOString(),
            deadlineDate: result.application.deadlineDate?.toISOString(),
            appliedAt: result.application.appliedAt.toISOString(),
            updatedAt: result.application.updatedAt.toISOString(),
            createdAt: result.application.createdAt.toISOString()
          },
          progress: {
            totalStages: result.progress.totalStages,
            completedStages: result.progress.completedStages,
            overallProgress: result.progress.overallProgress,
            currentStage: result.progress.currentStage,
            stages: result.progress.stages.map((stage) => ({
              id: stage.id,
              stageName: stage.stageName,
              stageOrder: stage.stageOrder,
              status: stage.status,
              progressPercentage: stage.progressPercentage,
              startDate: stage.startDate?.toISOString(),
              completedDate: stage.completedDate?.toISOString(),
              requirementsMet: stage.requirementsMet,
              totalRequirements: stage.totalRequirements
            }))
          }
        },
        error: null
      };
    } catch (error) {
      this.logger.error(`Failed to get application progress: ${error.message}`);
      return {
        status: error?.response?.statusCode || 500,
        message:
          error?.response?.message || 'Failed to get application progress',
        data: null,
        error: {
          details: error.message
        }
      };
    }
  }

  @GrpcMethod('StudentService', 'GetApplicationRequirements')
  async getApplicationRequirements(request: {
    applicationId: number | bigint;
  }) {
    try {
      this.logger.log(
        `gRPC GetApplicationRequirements request for application: ${request.applicationId}`
      );
      return await this.applicationService.getApplicationRequirements(
        request.applicationId
      );
    } catch (error) {
      this.logger.error(
        `Failed to get application requirements: ${error.message}`
      );
      return {
        status: error?.response?.statusCode || 500,
        message:
          error?.response?.message || 'Failed to get application requirements',
        data: null,
        error: {
          details: error.message
        }
      };
    }
  }

  @GrpcMethod('StudentService', 'UpdateApplicationRequirement')
  async updateApplicationRequirement(request: {
    applicationId: number | bigint;
    requirementId: number | bigint;
    acceptedByApplyGoal?: { value: boolean } | null;
    acceptedByUniversity?: boolean;
    acceptedByApplyGoalUserId?: number;
    acceptedByUniversityUserId?: number;
    rejectionReason?: string;
    status?: string;
    url?: string;
    isNew?: boolean;
  }) {
    try {
      this.logger.log(
        `gRPC UpdateApplicationRequirement request for application: ${request.applicationId}, requirement: ${request.requirementId}`
      );
      console.log('request+++++++++++++++++++=>', request);
      const requirementId = this.convertIdToNumber(request.requirementId);

      // Convert BoolValue to boolean | null
      let acceptedByApplyGoal: boolean | null = null;
      if (
        request.acceptedByApplyGoal !== undefined &&
        request.acceptedByApplyGoal !== null
      ) {
        acceptedByApplyGoal = request.acceptedByApplyGoal.value;
      }

      return await this.applicationService.updateDocumentRequirementStatus(
        requirementId,
        {
          acceptedByApplyGoal: acceptedByApplyGoal,
          acceptedByUniversity: request.acceptedByUniversity,
          acceptedByApplyGoalUserId: request.acceptedByApplyGoalUserId,
          acceptedByUniversityUserId: request.acceptedByUniversityUserId,
          rejectionReason: request.rejectionReason,
          status: request.status,
          url: request.url,
          isNew: request.isNew
        }
      );
    } catch (error) {
      this.logger.error(
        `Failed to update document requirement status: ${error.message}`
      );
      return {
        status: error?.response?.statusCode || 500,
        message:
          error?.response?.message ||
          'Failed to update document requirement status',
        data: null,
        error: {
          details: error.message
        }
      };
    }
  }

  @GrpcMethod('StudentService', 'UpdateApplicationStage')
  async updateApplicationStage(request: {
    applicationId: number | bigint;
    stageId: number | bigint;
    status: string;
  }) {
    try {
      this.logger.log(
        `gRPC UpdateApplicationStage request for stage: ${request.stageId}, status: ${request.status}`
      );

      const applicationId = this.convertIdToNumber(request.applicationId);
      const stageId = this.convertIdToNumber(request.stageId);

      return await this.applicationService.updateApplicationStage(
        applicationId,
        stageId,
        request.status
      );
    } catch (error) {
      this.logger.error(`Failed to update application stage: ${error.message}`);
      return {
        status: error?.response?.statusCode || 500,
        message:
          error?.response?.message || 'Failed to update application stage',
        data: null,
        error: {
          details: error.message
        }
      };
    }
  }

  @GrpcMethod('StudentService', 'CreateApplicationNote')
  async createApplicationNote(request: {
    applicationId: number | bigint;
    noteType: string;
    title?: string;
    content: string;
    createdBy: number | bigint;
    isPrivate?: boolean;
    userInfo?: string;
    replyId?: number | bigint;
  }) {
    try {
      this.logger.log(
        `gRPC CreateApplicationNote request for application: ${request.applicationId}`
      );

      const applicationId = this.convertIdToNumber(request.applicationId);
      const createdBy = this.convertIdToNumber(request.createdBy);

      // Parse userInfo if it's a JSON string
      let userInfo = null;
      if (request.userInfo) {
        try {
          userInfo = JSON.parse(request.userInfo);
        } catch (error) {
          this.logger.warn('Failed to parse userInfo JSON, using as string');
          userInfo = request.userInfo;
        }
      }

      return await this.applicationService.createApplicationNote(
        applicationId,
        {
          noteType: request.noteType,
          title: request.title,
          content: request.content,
          createdBy: Number(createdBy),
          isPrivate: request.isPrivate || false,
          userInfo: userInfo,
          replyId: request.replyId ? Number(request.replyId) : null
        }
      );
    } catch (error) {
      this.logger.error(`Failed to create application note: ${error.message}`);
      return {
        status: error?.response?.statusCode || 500,
        message:
          error?.response?.message || 'Failed to create application note',
        data: null,
        error: {
          details: error.message
        }
      };
    }
  }

  @GrpcMethod('StudentService', 'GetApplicationNotes')
  async getApplicationNotes(request: {
    applicationId: number | bigint;
    noteType?: string;
  }) {
    try {
      this.logger.log(
        `gRPC GetApplicationNotes request for application: ${request.applicationId}`
      );

      const applicationId = this.convertIdToNumber(request.applicationId);

      return await this.applicationService.getApplicationNotes(
        applicationId,
        request.noteType
      );
    } catch (error) {
      this.logger.error(`Failed to get application notes: ${error.message}`);
      return {
        status: error?.response?.statusCode || 500,
        message: error?.response?.message || 'Failed to get application notes',
        data: null,
        error: {
          details: error.message
        }
      };
    }
  }

  @GrpcMethod('StudentService', 'GetApplicationProgressRecords')
  async getApplicationProgressRecords(request: {
    applicationId: number | bigint;
    recordType?: string;
  }) {
    try {
      this.logger.log(
        `gRPC GetApplicationProgressRecords request for application: ${request.applicationId}`
      );

      const applicationId = this.convertIdToNumber(request.applicationId);

      return await this.applicationService.getApplicationProgressRecords(
        applicationId,
        request.recordType
      );
    } catch (error) {
      this.logger.error(
        `Failed to get application progress records: ${error.message}`
      );
      return {
        status: error?.response?.statusCode || 500,
        message:
          error?.response?.message ||
          'Failed to get application progress records',
        data: null,
        error: {
          details: error.message
        }
      };
    }
  }

  @GrpcMethod('StudentService', 'CreateApplicationProgressRecord')
  async createApplicationProgressRecord(request: {
    applicationId: number | bigint;
    recordType: string;
    title: string;
    description: string;
    status?: string;
    recordDate?: string;
    amount?: number;
    currency?: string;
    proofLinks?: string;
    attachments?: string;
    createdBy: number | bigint;
    applicationStage?: string;
    applicationStep?: string;
  }) {
    try {
      this.logger.log(
        `gRPC CreateApplicationProgressRecord request for application: ${request.applicationId}`
      );

      const applicationId = this.convertIdToNumber(request.applicationId);
      const createdBy = this.convertIdToNumber(request.createdBy);

      // Parse JSON strings
      let proofLinks: any[] = [];
      let attachments: any[] = [];

      if (request.proofLinks) {
        try {
          proofLinks = JSON.parse(request.proofLinks);
        } catch (error) {
          this.logger.warn('Failed to parse proofLinks JSON');
        }
      }

      if (request.attachments) {
        try {
          attachments = JSON.parse(request.attachments);
        } catch (error) {
          this.logger.warn('Failed to parse attachments JSON');
        }
      }

      return await this.applicationService.createApplicationProgressRecord(
        applicationId,
        {
          recordType: request.recordType,
          title: request.title,
          description: request.description,
          status: request.status,
          recordDate: request.recordDate
            ? new Date(request.recordDate)
            : undefined,
          amount: request.amount,
          currency: request.currency,
          proofLinks: proofLinks,
          attachments: attachments,
          createdBy: Number(createdBy),
          applicationStage: request.applicationStage,
          applicationStep: request.applicationStep
        }
      );
    } catch (error) {
      this.logger.error(
        `Failed to create application progress record: ${error.message}`
      );
      return {
        status: error?.response?.statusCode || 500,
        message:
          error?.response?.message ||
          'Failed to create application progress record',
        data: null,
        error: {
          details: error.message
        }
      };
    }
  }

  @GrpcMethod('StudentService', 'GenerateCalDocument')
  async generateCalDocument(request: {
    applicationId: number | bigint;
    templateType?: 'pdf' | 'html';
    outputFormat?: string;
    includeAllPages?: boolean;
    pageNumbers?: number[];
  }) {
    const startTime = Date.now();
    try {
      this.logger.log('gRPC GenerateCalDocument request');

      const numericId = this.convertIdToNumber(request.applicationId);

      // Get application data with all related information (student, course, program, intake, campus, university)
      const application = await this.applicationService.findOneWithRelatedData(
        numericId
      );

      if (!application) {
        throw new Error(`Application with ID ${numericId} not found`);
      }

      this.logger.log(
        'Application data=========================================>:',
        application
      );

      // Determine template type (default to 'html' if not specified)
      const templateType = request.templateType || 'html';
      this.logger.log(
        `Using ${templateType} templates for CAL document generation`
      );

      // Generate CAL document with MinIO upload
      const result = await this.calDocumentService.generateCalDocumentWithMinio(
        application,
        templateType
      );

      // Update the application to mark CAL document as generated
      await this.applicationService.update(numericId, {
        isCalGenerated: true,
        calUrl: result,
        status:ApplicationStatus.CAL_GENERATED

      });

      this.logger.log(
        `Updated application ${numericId} with isCalGenerated = true`
      );

      return {
        status: 200,
        message: `CAL document generated successfully using ${templateType} templates`,
        data: {
          documentContent: Buffer.from(''), // Content is uploaded to MinIO, not returned
          filename: `CAL-Document-${numericId}.pdf`,
          contentType: 'application/pdf',
          fileSize: 0, // Size not available in new implementation
          generatedAt: new Date().toISOString(),
          minioUrl: result,
          objectKey: result.split('/').pop() || '',
          templateType: templateType
        },
        error: null
      };
    } catch (error) {
      this.logger.error(`Failed to generate CAL document: ${error.message}`);
      return {
        status: error?.response?.statusCode || 500,
        message: error?.response?.message || 'Failed to generate CAL document',
        data: null,
        error: {
          details: error.message
        }
      };
    }
  }

  /**
   * Get dashboard data by campus ID
   * Returns counts for total applications, pending applications, CAL generated, and Admission Portfolio generated
   */
  @GrpcMethod('StudentService', 'GetDashboardDataByCampus')
  async getDashboardDataByCampus(request: {
    campusId: number | bigint;
  }): Promise<{
    status: number;
    message: string;
    data: {
      totalApplications: number;
      pendingApplications: number;
      calGenerated: number;
      admissionPortfolioGenerated: number;
      campusId: number;
    } | null;
    error: { details: string } | null;
  }> {
    try {
      this.logger.log(
        `Getting dashboard data for campus ID: ${request.campusId}`
      );

      // Convert campusId to number
      const campusId = this.convertIdToNumber(request.campusId);

      // Get dashboard data from service
      const dashboardData =
        await this.applicationService.getDashboardDataByCampus(
          Number(campusId)
        );

      this.logger.log(
        `Dashboard data retrieved successfully for campus ${campusId}`
      );

      return {
        status: 200,
        message: 'Dashboard data retrieved successfully',
        data: dashboardData,
        error: null
      };
    } catch (error) {
      this.logger.error(`Failed to get dashboard data: ${error.message}`);
      return {
        status: error?.response?.statusCode || 500,
        message: error?.response?.message || 'Failed to get dashboard data',
        data: null,
        error: {
          details: error.message
        }
      };
    }
  }

  // ✅ Helper method to safely stringify objects with BigInt values
  private safeStringify(obj: any): string {
    return JSON.stringify(obj, (key, value) =>
      typeof value === 'bigint' ? value.toString() : value
    );
  }
}
