import { forwardRef, Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { Application } from './application.model';
import { ApplicationStage } from './application-stage.model';
import { ApplicationDocumentRequirement } from './application-document-requirement.model';
import { ApplicationDocumentMapping } from './application-document-mapping.model';
import { ApplicationProgressRecord } from './application-progress-record.model';
import { ApplicationPayment } from './application-payment.model';
import { ApplicationOffer } from './application-offer.model';
import { ApplicationNote } from './application-note.model';
import { ApplicationActivity } from './application-activity.model';
import { ApplicationService } from './application.service';
import { ApplicationStageService } from './application-stage.service';
import { ApplicationController } from './application.controller';
import { ApplicationGrpcController } from './application.grpc.controller';
import { StudentModule } from '../student/student.module';
import { Student } from '../student/models/student.model';
import { StudentPersonalInfo } from '../student/models/student-personal-info.model';
import { StudentAcademicRecord } from '../student/models/student-academic-record.model';
import { StudentProficiencyRecord } from '../student/models/student-proficiency-record.model';
import { StudentAcademicBackground } from '../student/models/student-academic.model';
import { ApplicationDocumentNote } from './application-documents-notes';
import { CalDocumentService } from './cal-document.service';
import { PdfModule } from '@apply-goal-backend/utils';
import { UploadModule } from '@apply-goal-backend/common';
import { UniversityModule } from '../university/university.module';
import { StudentService } from '../student/student.service';
// import { StudentDocumentsService } from '../student/services/student-documents.service';
import { StudentDocumentsModule } from '../student/student-documents.module';
import { Enrollment } from '../student/models/enrollment.model';
import { Grade } from '../student/models/grade.model';
import { EmergencyContact } from '../student/models/emergency-contact.model';
import { StudentPublication } from '../student/models/student-publication.model';
import { StudentSocialLink } from '../student/models/student-social-link.model';
import { StudentOtherActivity } from '../student/models/student-other-activity.model';

@Module({
  imports: [
    SequelizeModule.forFeature([
      Application,
      ApplicationStage,
      ApplicationDocumentRequirement,
      ApplicationDocumentMapping,
      ApplicationProgressRecord,
      ApplicationPayment,
      ApplicationOffer,
      ApplicationNote,
      ApplicationActivity,
      Student,
      ApplicationDocumentNote,
      Enrollment,
      Grade,
      StudentAcademicBackground,
      StudentPersonalInfo,
      EmergencyContact,
      StudentAcademicRecord,
      StudentProficiencyRecord,
      StudentPublication,
      StudentSocialLink,
      StudentOtherActivity
    ]),
    UniversityModule,
    PdfModule,
    UploadModule.forRoot({
      provider: 's3',
      s3: {
        endpoint: process.env.S3_ENDPOINT || 'http://localhost:9000',
        accessKey: process.env.S3_ACCESS_KEY || 'minioadmin',
        secretKey: process.env.S3_SECRET_KEY || 'minioadmin123',
        bucket: process.env.S3_BUCKET || 'applygoal-files',
        region: process.env.S3_REGION || 'us-east-1',
        forcePathStyle: process.env.S3_FORCE_PATH_STYLE === 'true' || true,
        publicEndpoint:
          process.env.S3_PUBLIC_ENDPOINT ||
          process.env.S3_ENDPOINT ||
          'http://localhost:9000'
      }
    }),
    PdfModule,
    // forwardRef(() => StudentModule),
    forwardRef(() => StudentDocumentsModule)
  ],
  controllers: [ApplicationController, ApplicationGrpcController],
  providers: [
    ApplicationService,
    ApplicationStageService,
    CalDocumentService,
    // StudentService
    // StudentDocumentsService
  ],
  exports: [ApplicationService, ApplicationStageService, CalDocumentService]
})
export class ApplicationModule {}
