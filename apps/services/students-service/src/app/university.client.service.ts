import { Injectable, OnModuleInit } from '@nestjs/common';
import { Client, ClientGrpc, Transport } from '@nestjs/microservices';
import { join } from 'path';
import { Observable } from 'rxjs';

interface UniversityGrpcService {
  getCourseRequirements(data: { courseId: number; programLevelId: number }): Observable<any>;
}

@Injectable()
export class UniversityClientService implements OnModuleInit {
  @Client({
    transport: Transport.GRPC,
    options: {
      package: 'university',
      protoPath: join(process.cwd(), 'libs/shared/dto/src/lib/university/university.proto'),
      url: 'university-service:50057',
    },
  })
  private readonly client: ClientGrpc;

  private universityService: UniversityGrpcService;

  onModuleInit() {
    this.universityService = this.client.getService<UniversityGrpcService>('UniversityService');
  }

  getCourseRequirements(data: { courseId: number; programLevelId: number }): Observable<any> {
    return this.universityService.getCourseRequirements(data);
  }
}
