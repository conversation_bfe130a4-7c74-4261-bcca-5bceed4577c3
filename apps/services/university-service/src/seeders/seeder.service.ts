import { Injectable, Logger } from '@nestjs/common';
import { UniversitySeeder } from './university.seeder';

@Injectable()
export class SeederService {
  private readonly logger = new Logger(SeederService.name);

  constructor(private readonly universitySeeder: UniversitySeeder) {}

  async runAllSeeders() {
    try {
      this.logger.log('🌱 Starting database seeding process...');
      
      // Run university seeder
      await this.universitySeeder.seed();
      
      this.logger.log('✅ All seeders completed successfully!');
    } catch (error) {
      this.logger.error('❌ Seeding failed:', error);
      throw error;
    }
  }
}
