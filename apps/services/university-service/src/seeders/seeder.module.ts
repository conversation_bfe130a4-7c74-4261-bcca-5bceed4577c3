import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { UniversitySeeder } from './university.seeder';
import { SeederService } from './seeder.service';
import { AuthClientService } from '../app/auth.service';

// Import all models
import { University } from '../app/university/university.model';
import { Campus } from '../app/campus/campus.model';
import { Country } from '../app/country/country.model';
import { ProgramLevel } from '../app/program-level/programLevel.model';
import { Intake } from '../app/intake/intake.model';
import { IntakeSession } from '../app/intake/intake-sessions.model';
import { Course } from '../app/course/course.model';
import { CourseFeeCategory } from '../app/course/courseFee.model';
import { CourseIntakeSessions } from '../app/course/courseIntakeSessions.model';
import { ProgramLevelIntake } from '../app/program-level/programLevelIntake.model';
import { IntakeStudentType } from '../app/intake/intake-student-type.model';
import { ProgramLevelApplicationStep } from '../app/program-level/applicationStep.model';
import { ProgramLevelTestScore } from '../app/program-level/applicationTestScore.model';
import { FieldOfStudy } from '../app/field-of-study/field-of-study.model';
import { ProgramLevelIntakeFee } from '../app/fee/fee.model';
import { UniversityGeneral } from '../app/general/university-general.model';
import { Alumni } from '../app/alumni/alumni.model';
import { CampusProgramLevel } from '../app/campus/campus-program-level.model';
import { UniversityContactDetails } from '../app/university-contact/university-contact.model';
import { UniversityCommission } from '../app/commission/university-commission.model';
import { ProgramCommission } from '../app/commission/program-commission.model';
import { StudentBaseCommission } from '../app/commission/student-base-commission.model';

@Module({
  imports: [
    SequelizeModule.forFeature([
      University,
      Campus,
      Country,
      ProgramLevel,
      Intake,
      IntakeSession,
      Course,
      CourseFeeCategory,
      CourseIntakeSessions,
      ProgramLevelIntake,
      IntakeStudentType,
      ProgramLevelApplicationStep,
      ProgramLevelTestScore,
      FieldOfStudy,
      ProgramLevelIntakeFee,
      UniversityGeneral,
      Alumni,
      CampusProgramLevel,
      UniversityContactDetails,
      UniversityCommission,
      ProgramCommission,
      StudentBaseCommission,
    ]),
  ],
  providers: [UniversitySeeder, SeederService, AuthClientService],
  exports: [UniversitySeeder, SeederService],
})
export class SeederModule {}
