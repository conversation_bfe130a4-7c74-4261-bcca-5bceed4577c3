import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { University } from '../app/university/university.model';
import { Campus } from '../app/campus/campus.model';
import { Country } from '../app/country/country.model';
import { ProgramLevel } from '../app/program-level/programLevel.model';
import { Intake } from '../app/intake/intake.model';
import { IntakeSession } from '../app/intake/intake-sessions.model';
import { Course } from '../app/course/course.model';
import { CourseIntakeSessions } from '../app/course/courseIntakeSessions.model';
import { ProgramLevelIntake } from '../app/program-level/programLevelIntake.model';
import { IntakeStudentType } from '../app/intake/intake-student-type.model';
import { ProgramLevelApplicationStep } from '../app/program-level/applicationStep.model';
import { ProgramLevelTestScore } from '../app/program-level/applicationTestScore.model';
import { FieldOfStudy } from '../app/field-of-study/field-of-study.model';
import { ProgramLevelIntakeFee } from '../app/fee/fee.model';
import { CourseFeeCategory } from '../app/course/courseFee.model';
import { UniversityContactDetails } from '../app/university-contact/university-contact.model';
import { UniversityGeneral } from '../app/general/university-general.model';
import { Alumni } from '../app/alumni/alumni.model';
import { CampusProgramLevel } from '../app/campus/campus-program-level.model';
import { UniversityCommission } from '../app/commission/university-commission.model';
import { ProgramCommission } from '../app/commission/program-commission.model';
import { StudentBaseCommission } from '../app/commission/student-base-commission.model';
import { AuthClientService } from '../app/auth.service';
import { firstValueFrom, throwError } from 'rxjs';
import { timeout, catchError } from 'rxjs/operators';

@Injectable()
export class UniversitySeeder {
  private readonly logger = new Logger(UniversitySeeder.name);

  constructor(
    @InjectModel(University) private universityModel: typeof University,
    @InjectModel(Campus) private campusModel: typeof Campus,
    @InjectModel(Country) private countryModel: typeof Country,
    @InjectModel(ProgramLevel) private programLevelModel: typeof ProgramLevel,
    @InjectModel(Intake) private intakeModel: typeof Intake,
    @InjectModel(IntakeSession) private intakeSessionModel: typeof IntakeSession,
    @InjectModel(Course) private courseModel: typeof Course,
    @InjectModel(CourseIntakeSessions)
    private courseIntakeSessionsModel: typeof CourseIntakeSessions,
    @InjectModel(ProgramLevelIntake)
    private programLevelIntakeModel: typeof ProgramLevelIntake,
    @InjectModel(IntakeStudentType)
    private intakeStudentTypeModel: typeof IntakeStudentType,
    @InjectModel(ProgramLevelApplicationStep)
    private applicationStepModel: typeof ProgramLevelApplicationStep,
    @InjectModel(ProgramLevelTestScore)
    private testScoreModel: typeof ProgramLevelTestScore,
    @InjectModel(FieldOfStudy) private fieldOfStudyModel: typeof FieldOfStudy,
    @InjectModel(ProgramLevelIntakeFee)
    private feeModel: typeof ProgramLevelIntakeFee,
    @InjectModel(CourseFeeCategory)
    private courseFeeCategoryModel: typeof CourseFeeCategory,
    @InjectModel(UniversityContactDetails)
    private contactModel: typeof UniversityContactDetails,
    @InjectModel(UniversityGeneral)
    private generalModel: typeof UniversityGeneral,
    @InjectModel(Alumni) private alumniModel: typeof Alumni,
    @InjectModel(CampusProgramLevel)
    private campusProgramLevelModel: typeof CampusProgramLevel,
    @InjectModel(UniversityCommission)
    private universityCommissionModel: typeof UniversityCommission,
    @InjectModel(ProgramCommission)
    private programCommissionModel: typeof ProgramCommission,
    @InjectModel(StudentBaseCommission)
    private studentBaseCommissionModel: typeof StudentBaseCommission,
    private authClientService: AuthClientService
  ) {}

  /**
   * Clears all existing data to allow fresh seeding
   */
  private async clearExistingData() {
    try {
      this.logger.log('🧹 Clearing existing data...');
      
      // Clear data in reverse dependency order (respecting foreign key constraints)
      await this.courseFeeCategoryModel.destroy({ where: {}, force: true });
      await this.feeModel.destroy({ where: {}, force: true });
      await this.courseModel.destroy({ where: {}, force: true });
      await this.programLevelIntakeModel.destroy({ where: {}, force: true });
      await this.courseIntakeSessionsModel.destroy({ where: {}, force: true });
      await this.intakeSessionModel.destroy({ where: {}, force: true });
      await this.intakeModel.destroy({ where: {}, force: true });
      await this.programLevelModel.destroy({ where: {}, force: true });
      await this.fieldOfStudyModel.destroy({ where: {}, force: true });
      await this.campusModel.destroy({ where: {}, force: true });
      
      // Clear additional tables that might have foreign key relationships
      await this.universityModel.sequelize.query('DELETE FROM "university_contact_details" WHERE 1=1');
      await this.universityModel.sequelize.query('DELETE FROM "university_general" WHERE 1=1');
      await this.universityModel.sequelize.query('DELETE FROM "university_bank_details" WHERE 1=1');
      await this.universityModel.sequelize.query('DELETE FROM "university_commission" WHERE 1=1');
      await this.universityModel.sequelize.query('DELETE FROM "program_commissions" WHERE 1=1');
      await this.universityModel.sequelize.query('DELETE FROM "student_base_commissions" WHERE 1=1');
      await this.universityModel.sequelize.query('DELETE FROM "alumni" WHERE 1=1');
      await this.universityModel.sequelize.query('DELETE FROM "campus_program_levels" WHERE 1=1');
      await this.universityModel.sequelize.query('DELETE FROM "intake_student_types" WHERE 1=1');
      await this.universityModel.sequelize.query('DELETE FROM "program_level_application_steps" WHERE 1=1');
      await this.universityModel.sequelize.query('DELETE FROM "program_level_test_scores" WHERE 1=1');
      await this.universityModel.sequelize.query('DELETE FROM "course_test_scores" WHERE 1=1');
      
      await this.countryModel.destroy({ where: {}, force: true });
      await this.universityModel.destroy({ where: {}, force: true });
      
      this.logger.log('✅ Existing data cleared successfully');
    } catch (error) {
      this.logger.error('❌ Error clearing existing data:', error);
      throw error;
    }
  }

  async seed() {
    try {
      this.logger.log('Starting university seeding process...');

      // ✅ Force fresh seeding by clearing existing data
      await this.clearExistingData();
      this.logger.log('Cleared existing data, starting fresh seeding...');

      const transaction = await this.universityModel.sequelize.transaction();

      try {
        // Check if auth-service is available before proceeding
        try {
          this.logger.log('🔍 Checking auth-service availability...');
          // Try to create a test call to see if auth-service is responsive
          await firstValueFrom(
            this.authClientService.createUniversity({
              name: 'Test University',
              address: 'Test Address',
              country: 'Test Country',
              phone: 'Test Phone',
              email: '<EMAIL>',
              ipAddress: '127.0.0.1',
              userAgent: 'HealthCheck'
            }).pipe(
              timeout(5000),
              catchError(() => {
                throw new Error('Auth-service not responding');
              })
            )
          );
          this.logger.log('✅ Auth-service is available, proceeding with seeding...');
        } catch (authCheckError) {
          this.logger.warn('⚠️  Auth-service health check failed, will retry during individual university creation');
          this.logger.warn(`   - Error: ${authCheckError.message}`);
        }

        // 1. Create Universities
        const universities = await this.createUniversities(transaction);
        this.logger.log(`Created ${universities.length} universities`);

        // 2. Create Countries
        const countries = await this.createCountries(
          Number(universities[0].id),
          transaction
        );
        this.logger.log(`Created ${countries.length} countries`);

        // 3. Create Field of Studies
        const fieldOfStudies = await this.createFieldOfStudies(
          Number(universities[0].id),
          transaction
        );
        this.logger.log(`Created ${fieldOfStudies.length} field of studies`);

        // 4. Create Intakes
        const intakes = await this.createIntakes(
          Number(universities[0].id),
          transaction
        );
        this.logger.log(`Created ${intakes.length} intakes`);

        // 4.5. Create Intake Sessions
        const intakeSessions = await this.createIntakeSessions(intakes, transaction);
        this.logger.log(`Created ${intakeSessions.length} intake sessions`);

        // 5. Create Program Levels
        const programLevels = await this.createProgramLevels(
          Number(universities[0].id),
          intakes,
          transaction
        );
        this.logger.log(`Created ${programLevels.length} program levels`);

        // 6. Create Campuses
        const campuses = await this.createCampuses(
          Number(universities[0].id),
          countries,
          programLevels,
          transaction
        );
        this.logger.log(`Created ${campuses.length} campuses`);

        // 7. Create Fees
        const fees = await this.createFeesForAllProgramLevels(
          Number(universities[0].id),
          programLevels,
          intakes,
          campuses,
          transaction
        );
        this.logger.log(`Created ${fees.length} fees`);

        // 8. Create Courses
        const courses = await this.createCourses(
          Number(universities[0].id),
          fieldOfStudies,
          programLevels,
          fees,
          transaction
        );
        this.logger.log(`Created ${courses.length} courses`);
        
        // Create Course Fee
        // Map ProgramLevelIntakeFee model by program id from course and input into CourseFeeCategory model
        await this.createCourseFeeMappings(courses, fees, transaction);
        this.logger.log('Created course fee mappings');

        // 9. Create University Contact
        await this.createUniversityContact(
          Number(universities[0].id),
          transaction
        );
        this.logger.log('Created university contact');

        // 10. Create University General Info
        await this.createUniversityGeneral(
          Number(universities[0].id),
          transaction
        );
        this.logger.log('Created university general info');

        // 11. Create Alumni
        await this.createAlumni(Number(universities[0].id), transaction);
        this.logger.log('Created alumni');

        await this.createUniversityCommission(
          Number(universities[0].id),
          '1',
          programLevels,
          transaction
        );

        this.logger.log('Created university commission data');

        // Map Course to IntakeSessions via CourseIntakeSessions
        await this.createCourseIntakeSessionMappings(courses, intakes, transaction);
        this.logger.log('Created course intake session mappings');

        await transaction.commit();
        this.logger.log('University seeding completed successfully!');
      } catch (error) {
        await transaction.rollback();
        throw error;
      }
    } catch (error) {
      this.logger.error('Error during university seeding:', error);
      throw error;
    }
  }

  private async createUniversities(transaction: any) {
    const universitiesData = [
      {
        name: 'International American University',
        about:
          'International American University (IAU) is a U.S.-based institution committed to delivering affordable, quality, and career-relevant education to students from around the world. With multiple campus locations across California, IAU offers a range of programs including language training, undergraduate certificates and degrees, as well as graduate and doctoral-level education in business and technology. IAU is dedicated to academic excellence, professional development, and global student success.',
        type: 'For Profit',
        website: 'https://iaula.edu',
        foundedOn: '2005-01-01',
        institutionCode: 'LOS214F01373000',
        address: '3440 Wilshire Blvd., 10th Floor, #1000 Los Angeles, CA 90010, USA',
        country: 'USA',
        state: 'California',
        city: 'Los Angeles',
        postalCode: '1000',
        logo: 'https://example.com/uploads/du-logo.jpg',
        email: '<EMAIL>',
        primaryContactNumber: '+****************'
      }
    ];

    const universities = await this.universityModel.bulkCreate(universitiesData, {
      transaction
    });

    // Create organizations and users in auth-service for each university
    let successCount = 0;
    let failureCount = 0;
    
    for (const university of universities) {
      let retryCount = 0;
      const maxRetries = 3;
      
      while (retryCount < maxRetries) {
        try {
          this.logger.log(`Creating organization and user for ${university.name} in auth-service... (attempt ${retryCount + 1}/${maxRetries})`);
          
          const authResponse = await firstValueFrom(
            this.authClientService.createUniversity({
              name: university.name,
              address: university.address,
              country: university.country,
              phone: university.primaryContactNumber,
              email: university.email,
              ipAddress: '127.0.0.1',
              userAgent: 'UniversitySeeder'
            })
          );

          if (authResponse.success) {
            this.logger.log(`✅ Successfully created organization and user for ${university.name}: ${authResponse.message}`);
            if (authResponse.organization) {
              this.logger.log(`   - Organization ID: ${authResponse.organization.id}`);
              
              // Update university record with organization ID
              try {
                await university.update({ organizationId: BigInt(authResponse.organization.id) }, { transaction });
                this.logger.log(`   - Updated university record with organization ID: ${authResponse.organization.id}`);
              } catch (updateError) {
                this.logger.error(`   - Failed to update university organization ID: ${updateError.message}`);
              }
            }
            if (authResponse.user) {
              this.logger.log(`   - User ID: ${authResponse.user.id}, Email: ${authResponse.user.email}`);
              
              // Update university record with user ID if available
              try {
                await university.update({ userId: BigInt(authResponse.user.id) }, { transaction });
                this.logger.log(`   - Updated university record with user ID: ${authResponse.user.id}`);
              } catch (updateError) {
                this.logger.error(`   - Failed to update university user ID: ${updateError.message}`);
              }
            }
            successCount++;
            break; // Success, exit retry loop
          } else {
            this.logger.warn(`⚠️  Failed to create organization and user for ${university.name}: ${authResponse.message}`);
            failureCount++;
            break; // Don't retry on business logic failures
          }
        } catch (error) {
          retryCount++;
          this.logger.error(`❌ Error creating organization and user for ${university.name} (attempt ${retryCount}/${maxRetries}):`, error);
          this.logger.error(`   - Error details: ${error.message || 'Unknown error'}`);
          
          if (retryCount < maxRetries) {
            this.logger.log(`   - Retrying in 2 seconds...`);
            await new Promise(resolve => setTimeout(resolve, 2000));
          } else {
            this.logger.error(`   - Max retries reached for ${university.name}, continuing with next university`);
            failureCount++;
          }
        }
      }
    }

    // Log summary
    this.logger.log(`🎯 Auth-service integration summary: ${successCount} successful, ${failureCount} failed out of ${universities.length} universities`);
    
    if (successCount > 0) {
      this.logger.log(`🔑 University users created with default password: 'DefaultPassword123!'`);
      this.logger.log(`📧 Password reset tokens have been sent to university email addresses`);
    }

    return universities;
  }

  private async createCountries(universityId: number, transaction: any) {
    const countriesData = [
      {
        universityId,
        countryName: 'United States',
        isActive: true
      }
    ];

    return await this.countryModel.bulkCreate(countriesData, { transaction });
  }

  private async createFieldOfStudies(universityId: number, transaction: any) {
    const fieldOfStudiesData = [
      {
        universityId,
        name: 'Language Program'
      },
      {
        universityId,
        name: 'Accounting'
      },
      {
        universityId,
        name: 'Business'
      },
      {
        universityId,
        name: 'Information Technology'
      }
    ];

    return await this.fieldOfStudyModel.bulkCreate(fieldOfStudiesData, {
      transaction
    });
  }

  private async createIntakes(universityId: number, transaction: any) {
    // First create intakes
    const intakesData = [
      {
        universityId,
        name: 'Fall 1',
        endDate: 'August 18',
        classStartDate: 'Septmeber 2',
        enrollmentDate: 'August 25',
        applicationType: [
          {
            applicationType: 'Initial',
            startDate: 'July 14',
            endDate: null,
            enrollementDate: null,
          },
          {
            applicationType: 'Transfer',
            startDate: null,
            endDate: null,
            enrollementDate: null,
          },
          {
            applicationType: 'COS',
            startDate: null,
            endDate: null,
            enrollementDate: null,
          },
          {
            applicationType: 'Reinstatement',
            startDate: null,
            endDate: null,
            enrollementDate: null,
          },
          {
            applicationType: 'Non F-1',
            startDate: null,
            endDate: null,
            enrollementDate: null,
          }
        ],
        applicationProcessDuration: 'September 2 - October 8'
      },
      {
        universityId,
        name: 'Fall 2',
        endDate: 'October 14',
        classStartDate: 'October 27',
        enrollmentDate: 'October 20',
        applicationType: [
          {
            applicationType: 'Transfer',
            startDate: null,
            endDate: null,
            enrollementDate: null,
          },
          {
            applicationType: 'COS',
            startDate: null,
            endDate: null,
            enrollementDate: null,
          },
          {
            applicationType: 'Reinstatement',
            startDate: null,
            endDate: null,
            enrollementDate: null,
          },
          {
            applicationType: 'Non F-1',
            startDate: null,
            endDate: null,
            enrollementDate: null,
          }
        ],
        applicationProcessDuration: 'October 27 - December 21'
      },
      {
        universityId,
        name: 'Spring 1',
        endDate: 'December 15',
        classStartDate: 'January 5',
        enrollmentDate: 'December 22',
        applicationType: [
          {
            applicationType: 'Initial',
            startDate: 'November 17',
            endDate: null,
            enrollementDate: null,
          },
          {
            applicationType: 'Transfer',
            startDate: null,
            endDate: null,
            enrollementDate: null,
          },
          {
            applicationType: 'COS',
            startDate: null,
            endDate: null,
            enrollementDate: null,
          },
          {
            applicationType: 'Reinstatement',
            startDate: null,
            endDate: null,
            enrollementDate: null,
          },
          {
            applicationType: 'Non F-1',
            startDate: null,
            endDate: null,
            enrollementDate: null,
          },
          {
            applicationType: 'New Program',
            startDate: null,
            endDate: null,
            enrollementDate: null,
          }
        ],
        applicationProcessDuration: 'January 5 - March 1'
      },
      {
        universityId,
        name: 'Spring 2',
        endDate: 'February 17',
        classStartDate: 'March 2',
        enrollmentDate: 'February 23',
        applicationType: [
          {
            applicationType: 'Transfer',
            startDate: null,
            endDate: null,
            enrollementDate: null,
          },
          {
            applicationType: 'COS',
            startDate: null,
            endDate: null,
            enrollementDate: null,
          },
          {
            applicationType: 'Reinstatement',
            startDate: null,
            endDate: null,
            enrollementDate: null,
          },
          {
            applicationType: 'Non F-1',
            startDate: null,
            endDate: null,
            enrollementDate: null,
          }
        ],
        applicationProcessDuration: 'March 2 - April 26'
      },
      {
        universityId,
        name: 'Summer 1',
        endDate: 'April 20',
        classStartDate: 'May 4',
        enrollmentDate: 'April 27',
        applicationType: [
          {
            applicationType: 'Initial',
            startDate: 'March 23',
            endDate: null,
            enrollementDate: null,
          },
          {
            applicationType: 'Transfer',
            startDate: null,
            endDate: null,
            enrollementDate: null,
          },
          {
            applicationType: 'COS',
            startDate: null,
            endDate: null,
            enrollementDate: null,
          },
          {
            applicationType: 'Reinstatement',
            startDate: null,
            endDate: null,
            enrollementDate: null,
          },
          {
            applicationType: 'New Program',
            startDate: null,
            endDate: null,
            enrollementDate: null,
          },
          {
            applicationType: 'Non F-1',
            startDate: null,
            endDate: null,
            enrollementDate: null,
          }
        ],
        applicationProcessDuration: 'May 4 - June 28'
      },
      {
        universityId,
        name: 'Summer 2',
        endDate: 'June 15',
        classStartDate: 'June 29',
        enrollmentDate: 'June 22',
        applicationType: [
          {
            applicationType: 'Transfer',
            startDate: null,
            endDate: null,
            enrollementDate: null,
          },
          {
            applicationType: 'COS',
            startDate: null,
            endDate: null,
            enrollementDate: null,
          },
          {
            applicationType: 'Reinstatement',
            startDate: null,
            endDate: null,
            enrollementDate: null,
          },
          {
            applicationType: 'Non F-1',
            startDate: null,
            endDate: null,
            enrollementDate: null,
          }
        ],
        applicationProcessDuration: 'June 29 - August 23'
      },
    ];

    const intakes = await this.intakeModel.bulkCreate(intakesData, {
      transaction
    });

    // Create student types for each intake
    const studentTypesData = [];
    for (const intake of intakes) {
      studentTypesData.push(
        {
          intakeId: intake.id,
          studentType: 'Initial',
        },
        {
          intakeId: intake.id,
          studentType: 'Transfer',
        },
        {
          intakeId: intake.id,
          studentType: 'COS',
        },
        {
          intakeId: intake.id,
          studentType: 'Reinstatement',
        },
        {
          intakeId: intake.id,
          studentType: 'Non F-1',
        },
        {
          intakeId: intake.id,
          studentType: 'New Program',
        }
      );
    }

    await this.intakeStudentTypeModel.bulkCreate(studentTypesData, {
      transaction
    });

    return intakes;
  }

  private async createProgramLevels(
    universityId: number,
    intakes: any[],
    transaction: any
  ) {
    const programLevelsData = [
      {
        universityId,
        programLevelName: "ESL / Pathway",
        credits: 36
      },
      {
        universityId,
        programLevelName: "Certificate",
        credits: 45
      },
      {
        universityId,
        programLevelName: "Associate's Degree",
        credits: 60
      },
      {
        universityId,
        programLevelName: "Bachelor's Degree",
        credits: 120
      },
      {
        universityId,
        programLevelName: "Master's Degree",
        credits: 36
      },
      {
        universityId,
        programLevelName: 'DBA',
        credits: 60
      },
      {
        universityId,
        programLevelName: 'DM',
        credits: 42
      }
    ];

    const programLevels = await this.programLevelModel.bulkCreate(
      programLevelsData,
      { transaction }
    );

    // Create program level intakes (many-to-many relationship)
    const programLevelIntakesData = [];
    for (const programLevel of programLevels) {
      for (const intake of intakes) {
        programLevelIntakesData.push({
          universityId,
          programLevelId: programLevel.id,
          intakeId: intake.id,
          isActive: true
        });
      }
    }

    await this.programLevelIntakeModel.bulkCreate(programLevelIntakesData, {
      transaction
    });

    // Create application steps for each program level
    const applicationStepsData = [];
    for (const programLevel of programLevels) {
      applicationStepsData.push(
        {
          programLevelId: programLevel.id,
          title: 'Collecting Documents',
          description: 'Students gather required application materials (passport, transcripts, financial proof, etc.).',
          instructions: 'Untill all missing doc uploaded',
          required: true,
          weight: 1
        },
        {
          programLevelId: programLevel.id,
          title: 'Applied',
          description: 'Application submitted to the university.',
          instructions: 'Via ApplyGoal ',
          required: true,
          weight: 2
        },
        {
          programLevelId: programLevel.id,
          title: 'Accepted / Rejected',
          description: 'University reviews the application and issues a letter of acceptance.',
          instructions: 'Both AG and Uni Can Reject',
          required: true,
          weight: 3
        },
        {
          programLevelId: programLevel.id,
          title: 'CAL Issued',
          description: 'Conditional Admission Letter (CAL) or Confirmation Letter issued.',
          instructions: 'After Generation of CAL',
          required: true,
          weight: 4
        },
        {
          programLevelId: programLevel.id,
          title: 'I-20 Issued',
          description: "The university sends the official I-20 form for the student's F-1 visa application.",
          instructions: 'After Uploading I-20',
          required: true,
          weight: 5
        },
        {
          programLevelId: programLevel.id,
          title: 'Appointment Confirmation',
          description: 'This Doc will be uploaded bu Student/ Agent with the appointment Date',
          instructions: 'After the doc upload',
          required: true,
          weight: 6
        },
        {
          programLevelId: programLevel.id,
          title: 'VISA Processing',
          description: 'After uploading the appointment confirmation upload, this step will be started',
          instructions: null,
          required: true,
          weight: 7
        },
        {
          programLevelId: programLevel.id,
          title: 'Visa Decision',
          description: 'Student/agent will upload visa copy if approved otherwise will say rejected',
          instructions: null,
          required: true,
          weight: 8
        },
        {
          programLevelId: programLevel.id,
          title: 'Enrollment',
          description: 'if approved! will start enrollment.  Otherwise can ask for intake change / deffer. If deffered/intake changed new i-20 will be issued for new intake. (Student can request for intake change without attending visa interview.)',
          instructions: null,
          required: true,
          weight: 9
        }
      );
    }

    await this.applicationStepModel.bulkCreate(applicationStepsData, {
      transaction
    });

    // ✅ Create test scores dynamically using the actual program level IDs
    const testScoresData = [];
    
    // Find program levels by name for mapping
    const programLevelMap = {
      'ESL / Pathway': programLevels.find(pl => pl.programLevelName === 'ESL / Pathway'),
      'Certificate': programLevels.find(pl => pl.programLevelName === 'Certificate'),
      'Associate\'s Degree': programLevels.find(pl => pl.programLevelName === 'Associate\'s Degree'),
      'Bachelor\'s Degree': programLevels.find(pl => pl.programLevelName === 'Bachelor\'s Degree'),
      'Master\'s Degree': programLevels.find(pl => pl.programLevelName === 'Master\'s Degree'),
      'DBA': programLevels.find(pl => pl.programLevelName === 'DBA'),
      'DM': programLevels.find(pl => pl.programLevelName === 'DM')
    };

    // ESL / Pathway
    if (programLevelMap['ESL / Pathway']) {
      testScoresData.push(
        {
          programLevelId: programLevelMap['ESL / Pathway'].id,
          test: 'TOFEL iBT',
          testScore: { overallScore: '32+', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['ESL / Pathway'].id,
          test: 'TOFEL PBT',
          testScore: { overallScore: '400+', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['ESL / Pathway'].id,
          test: 'TOEIC',
          testScore: { overallScore: '345+', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['ESL / Pathway'].id,
          test: 'GRE(Old/New)',
          testScore: { overallScore: null, reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['ESL / Pathway'].id,
          test: 'IELTS',
          testScore: { overallScore: '2.5+', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['ESL / Pathway'].id,
          test: 'IETP',
          testScore: { overallScore: '2.5+', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['ESL / Pathway'].id,
          test: 'PTE',
          testScore: { overallScore: '30-35', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['ESL / Pathway'].id,
          test: 'Duolingo English Test',
          testScore: { overallScore: '70+', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['ESL / Pathway'].id,
          test: 'LanguageCert',
          testScore: { overallScore: '50–59', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['ESL / Pathway'].id,
          test: 'IAU/CLA/LSI/Internexus ESL Completion',
          testScore: { overallScore: 'Level 6', reading: 0, writing: 0, speaking: 0, listening: 0 }
        }
      );
    }

    // Certificate
    if (programLevelMap['Certificate']) {
      testScoresData.push(
        {
          programLevelId: programLevelMap['Certificate'].id,
          test: 'TOEFL iBT',
          testScore: { overallScore: '61+', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['Certificate'].id,
          test: 'TOEFL PBT',
          testScore: { overallScore: '500+', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['Certificate'].id,
          test: 'TOEIC',
          testScore: { overallScore: '345+', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['Certificate'].id,
          test: 'GRE(Old/New)',
          testScore: { overallScore: '400/140', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['Certificate'].id,
          test: 'IELTS',
          testScore: { overallScore: '4.5+', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['Certificate'].id,
          test: 'IETP',
          testScore: { overallScore: '3.5+', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['Certificate'].id,
          test: 'PTE',
          testScore: { overallScore: '50–53', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['Certificate'].id,
          test: 'Duolingo English Test',
          testScore: { overallScore: '90+', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['Certificate'].id,
          test: 'LanguageCert',
          testScore: { overallScore: '60–64', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['Certificate'].id,
          test: 'IAU/CLA/LSI/Internexus ESL Completion',
          testScore: { overallScore: 'Level 6', reading: 0, writing: 0, speaking: 0, listening: 0 }
        }
      );
    }

    // Associate's Degree
    if (programLevelMap['Associate\'s Degree']) {
      testScoresData.push(
        {
          programLevelId: programLevelMap['Associate\'s Degree'].id,
          test: 'TOEIC',
          testScore: { overallScore: '345+', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['Associate\'s Degree'].id,
          test: 'GRE(Old/New)',
          testScore: { overallScore: '400/140', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['Associate\'s Degree'].id,
          test: 'IELTS',
          testScore: { overallScore: '4.5+', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['Associate\'s Degree'].id,
          test: 'IETP',
          testScore: { overallScore: '3.5+', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['Associate\'s Degree'].id,
          test: 'PTE',
          testScore: { overallScore: '50–53', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['Associate\'s Degree'].id,
          test: 'Duolingo English Test',
          testScore: { overallScore: '90+', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['Associate\'s Degree'].id,
          test: 'LanguageCert',
          testScore: { overallScore: '60–64', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['Associate\'s Degree'].id,
          test: 'IAU/CLA/LSI/Internexus ESL Completion',
          testScore: { overallScore: 'Level 6', reading: 0, writing: 0, speaking: 0, listening: 0 }
        }
      );
    }

    // Bachelor's Degree
    if (programLevelMap['Bachelor\'s Degree']) {
      testScoresData.push(
        {
          programLevelId: programLevelMap['Bachelor\'s Degree'].id,
          test: 'TOEFL iBT',
          testScore: { overallScore: '61+', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['Bachelor\'s Degree'].id,
          test: 'TOEFL PBT',
          testScore: { overallScore: '500+', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['Bachelor\'s Degree'].id,
          test: 'TOEIC',
          testScore: { overallScore: '345+', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['Bachelor\'s Degree'].id,
          test: 'GRE(Old/New)',
          testScore: { overallScore: '400/140', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['Bachelor\'s Degree'].id,
          test: 'IELTS',
          testScore: { overallScore: '4.5+', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['Bachelor\'s Degree'].id,
          test: 'IETP',
          testScore: { overallScore: '3.5+', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['Bachelor\'s Degree'].id,
          test: 'PTE',
          testScore: { overallScore: '50–53', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['Bachelor\'s Degree'].id,
          test: 'Duolingo English Test',
          testScore: { overallScore: '90+', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['Bachelor\'s Degree'].id,
          test: 'LanguageCert',
          testScore: { overallScore: '60–64', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['Bachelor\'s Degree'].id,
          test: 'IAU/CLA/LSI/Internexus ESL Completion',
          testScore: { overallScore: 'Level 6', reading: 0, writing: 0, speaking: 0, listening: 0 }
        }
      );
    }

    // Master's Degree
    if (programLevelMap['Master\'s Degree']) {
      testScoresData.push(
        {
          programLevelId: programLevelMap['Master\'s Degree'].id,
          test: 'TOEFL iBT',
          testScore: { overallScore: '71+', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['Master\'s Degree'].id,
          test: 'TOEFL PBT',
          testScore: { overallScore: '530+', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['Master\'s Degree'].id,
          test: 'TOEIC',
          testScore: { overallScore: '720+', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['Master\'s Degree'].id,
          test: 'GRE(Old/New)',
          testScore: { overallScore: '400/140', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['Master\'s Degree'].id,
          test: 'IELTS',
          testScore: { overallScore: '6.0+', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['Master\'s Degree'].id,
          test: 'IETP',
          testScore: { overallScore: '3.7+', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['Master\'s Degree'].id,
          test: 'PTE',
          testScore: { overallScore: '54–58', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['Master\'s Degree'].id,
          test: 'Duolingo English Test',
          testScore: { overallScore: '95+', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['Master\'s Degree'].id,
          test: 'LanguageCert',
          testScore: { overallScore: '65–69', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['Master\'s Degree'].id,
          test: 'IAU/CLA/LSI/Internexus ESL Completion',
          testScore: { overallScore: 'Level 6', reading: 0, writing: 0, speaking: 0, listening: 0 }
        }
      );
    }

    // DBA
    if (programLevelMap['DBA']) {
      testScoresData.push(
        {
          programLevelId: programLevelMap['DBA'].id,
          test: 'TOEFL iBT',
          testScore: { overallScore: '80+', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['DBA'].id,
          test: 'TOEFL PBT',
          testScore: { overallScore: '550+', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['DBA'].id,
          test: 'TOEIC',
          testScore: { overallScore: '720+', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['DBA'].id,
          test: 'GRE(Old/New)',
          testScore: { overallScore: '400/140', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['DBA'].id,
          test: 'IELTS',
          testScore: { overallScore: '6.0+', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['DBA'].id,
          test: 'IETP',
          testScore: { overallScore: '3.9+', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['DBA'].id,
          test: 'PTE',
          testScore: { overallScore: '58–61', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['DBA'].id,
          test: 'Duolingo English Test',
          testScore: { overallScore: '105+', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['DBA'].id,
          test: 'LanguageCert',
          testScore: { overallScore: '65–69', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['DBA'].id,
          test: 'IAU/CLA/LSI/Internexus ESL Completion',
          testScore: { overallScore: 'Level 6', reading: 0, writing: 0, speaking: 0, listening: 0 }
        }
      );
    }

    // DM
    if (programLevelMap['DM']) {
      testScoresData.push(
        {
          programLevelId: programLevelMap['DM'].id,
          test: 'TOEFL iBT',
          testScore: { overallScore: '80+', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['DM'].id,
          test: 'TOEFL PBT',
          testScore: { overallScore: '550+', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['DM'].id,
          test: 'TOEIC',
          testScore: { overallScore: '720+', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['DM'].id,
          test: 'GRE(Old/New)',
          testScore: { overallScore: '400/140', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['DM'].id,
          test: 'IELTS',
          testScore: { overallScore: '6.0+', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['DM'].id,
          test: 'IETP',
          testScore: { overallScore: '3.9+', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['DM'].id,
          test: 'PTE',
          testScore: { overallScore: '58–61', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['DM'].id,
          test: 'Duolingo English Test',
          testScore: { overallScore: '105+', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['DM'].id,
          test: 'LanguageCert',
          testScore: { overallScore: '65–69', reading: 0, writing: 0, speaking: 0, listening: 0 }
        },
        {
          programLevelId: programLevelMap['DM'].id,
          test: 'IAU/CLA/LSI/Internexus ESL Completion',
          testScore: { overallScore: 'Level 6', reading: 0, writing: 0, speaking: 0, listening: 0 }
        }
      );
    }

    await this.testScoreModel.bulkCreate(testScoresData, { transaction });

    return programLevels;
  }

  private async createCampuses(
    universityId: number,
    countries: any[],
    programLevels: any[],
    transaction: any
  ) {
    const campusesData = [
      {
        universityId,
        campusName: 'International American University - Los Angeles (IAULA)',
        address: '3440 Wilshire Blvd., 10th Floor, #1000, Los Angeles, CA 90010, USA',
        countryId: countries[0].id,
        state: 'California',
        city: 'Los Angeles',
        postalCode: 'CA 90010',
        contactNumber: '+****************',
        email: '<EMAIL>',
        description: 'Located in the heart of Los Angeles, the IAU LA campus serves as a central hub for international and domestic students. This campus offers a full suite of programs including ESL, business, IT, and graduate degrees, making it a top choice for students seeking access to a global city and robust academic support.'
      },
      {
        universityId,
        campusName: 'International American University - Orange County (IAUOC)',
        address: '17801 Cartwright Rd.Irvine, CA 92614, USA',
        countryId: countries[0].id,
        state: 'California',
        city: 'Orange County',
        postalCode: 'CA 92614',
        contactNumber: '+1 (619) 937-3939',
        email: '<EMAIL>',
        description: 'IAU OC is situated in one of California\'s most vibrant business communities. Known for its welcoming atmosphere and student-focused services, the OC campus provides comprehensive undergraduate and graduate programs in business and management, along with English language instruction to prepare students for academic and professional advancement.'
      },
      {
        universityId,
        campusName: 'International American University - San Diego (IAUSD)',
        address: '444 West C Street, #240 San Diego, CA 92101, USA',
        countryId: countries[0].id,
        state: 'California',
        city: 'San Diego',
        postalCode: 'CA 92101',
        contactNumber: '+****************',
        email: '<EMAIL>',
        description: 'The San Diego campus specializes in select business and accounting programs, providing students with a focused and career-driven educational experience. With its proximity to innovation hubs and tech industries, IAU SD is an excellent choice for students pursuing practical skills in a professional environment.'
      },
      {
        universityId,
        campusName: 'International American University - LA International Airport (IAULAX)',
        address: '8632 S Sepulveda Blvd # 203 Los Angeles, CA 90045, USA',
        countryId: countries[0].id,
        state: 'California',
        city: 'San Francisco',
        postalCode: 'CA 90045',
        contactNumber: '+****************',
        email: '<EMAIL>',
        description: 'Located near Los Angeles International Airport, the IAU LAX campus is designed for accessibility and flexibility. It offers key business programs for undergraduate and graduate students and is ideal for international learners seeking convenience and quality in a global gateway city.'
      }
    ];

    const campuses = await this.campusModel.bulkCreate(campusesData, {
      transaction
    });

    // Create campus-program level relationships
    const campusProgramLevelsData = [];
    for (const campus of campuses) {
      for (const programLevel of programLevels) {
        if(programLevel.programLevelName === 'ESL / Pathway' && campus.campusName === 'International American University - San Diego (IAUSD)') continue;
        if(programLevel.programLevelName === 'ESL / Pathway' && campus.campusName === 'International American University - LA International Airport (IAULAX)') continue;
        campusProgramLevelsData.push({
          campusId: campus.id,
          programLevelId: programLevel.id
        });
      }
    }

    await this.campusProgramLevelModel.bulkCreate(campusProgramLevelsData, {
      transaction
    });

    // Create organizations and admin users for each campus
    this.logger.log('Creating organizations and admin users for campuses...');
    
    for (const campus of campuses) {
      try {
        // Create campus organization
        const orgResponse = await firstValueFrom(
          this.authClientService.createCampusOrganization({
            name: `${campus.campusName} Campus Organization`,
            description: `Campus organization for ${campus.campusName}`,
            address: campus.address,
            country: 'USA', // Hardcoded for now since we have countryId
            state: campus.state,
            city: campus.city,
            postalCode: campus.postalCode,
            phone: campus.contactNumber,
            email: campus.email,
            universityId: BigInt(universityId),
            campusId: BigInt(campus.id),  // NEW: Pass the campus ID
            ipAddress: '127.0.0.1',
            userAgent: 'UniversitySeeder'
          }).pipe(
            timeout(10000),
            catchError((error) => {
              this.logger.error(`Failed to create organization for campus ${campus.campusName}: ${error.message}`);
              return throwError(() => error);
            })
          )
        );

        if (orgResponse.success && orgResponse.organization) {
          // Update campus with organization ID
          await campus.update({ organizationId: BigInt(orgResponse.organization.id) }, { transaction });
          this.logger.log(`✅ Created organization for campus: ${campus.campusName} (ID: ${orgResponse.organization.id})`);

          // Create campus admin user
          const userResponse = await firstValueFrom(
            this.authClientService.createCampusAdminUser({
              name: `${campus.campusName} Administrator`,
              email: campus.email,
              phone: campus.contactNumber,
              organizationId: BigInt(orgResponse.organization.id),
              ipAddress: '127.0.0.1',
              userAgent: 'UniversitySeeder'
            }).pipe(
              timeout(10000),
              catchError((error) => {
                this.logger.error(`Failed to create admin user for campus ${campus.campusName}: ${error.message}`);
                return throwError(() => error);
              })
            )
          );

          if (userResponse.success && userResponse.user) {
            // Update campus with admin user ID
            await campus.update({ adminUserId: BigInt(userResponse.user.id) }, { transaction });
            this.logger.log(`✅ Created admin user for campus: ${campus.campusName} (ID: ${userResponse.user.id})`);
          } else {
            this.logger.warn(`⚠️ Failed to create admin user for campus: ${campus.campusName}`);
          }

          // Create DSO user for the campus
          this.logger.log(`Creating DSO user for campus: ${campus.campusName}`);
          const dsoResponse = await firstValueFrom(
            this.authClientService.createCampusUser({
              name: `${campus.campusName} DSO`,
              email: `dso.${campus.campusName.toLowerCase().replace(/[^a-zA-Z0-9]/g, '')}@example.com`,
              phone: campus.contactNumber,
              organizationId: BigInt(orgResponse.organization.id),
              roleName: 'Designated School Official (DSO)',
              ipAddress: '127.0.0.1',
              userAgent: 'UniversitySeeder'
            }).pipe(
              timeout(10000),
              catchError((error) => {
                this.logger.error(`Failed to create DSO user for campus ${campus.campusName}: ${error.message}`);
                return throwError(() => error);
              })
            )
          );

          if (dsoResponse.success && dsoResponse.user) {
            this.logger.log(`✅ Created DSO user for campus: ${campus.campusName} (ID: ${dsoResponse.user.id})`);
          } else {
            this.logger.warn(`⚠️ Failed to create DSO user for campus: ${campus.campusName}`);
          }

          // Create Admission Team user for the campus
          this.logger.log(`Creating Admission Team user for campus: ${campus.campusName}`);
          const admissionResponse = await firstValueFrom(
            this.authClientService.createCampusUser({
              name: `${campus.campusName} Admission Team`,
              email: `admission.${campus.campusName.toLowerCase().replace(/[^a-zA-Z0-9]/g, '')}@example.com`,
              phone: campus.contactNumber,
              organizationId: BigInt(orgResponse.organization.id),
              roleName: 'Admission Team',
              ipAddress: '127.0.0.1',
              userAgent: 'UniversitySeeder'
            }).pipe(
              timeout(10000),
              catchError((error) => {
                this.logger.error(`Failed to create Admission Team user for campus ${campus.campusName}: ${error.message}`);
                return throwError(() => error);
              })
            )
          );

          if (admissionResponse.success && admissionResponse.user) {
            this.logger.log(`✅ Created Admission Team user for campus: ${campus.campusName} (ID: ${admissionResponse.user.id})`);
          } else {
            this.logger.warn(`⚠️ Failed to create Admission Team user for campus: ${campus.campusName}`);
          }
        } else {
          this.logger.warn(`⚠️ Failed to create organization for campus: ${campus.campusName}`);
        }
      } catch (error) {
        this.logger.error(`❌ Error creating auth entities for campus ${campus.campusName}: ${error.message}`);
        // Continue with other campuses even if one fails
      }
    }

    return campuses;
  }

  private async createFeesForAllProgramLevels(
    universityId: number,
    programLevels: any[],
    intakes: any[],
    campuses: any[],
    transaction: any
  ) {
    const feesData = [];
    
    // Create campus mapping for dynamic ID lookup
    const campusMap = {
      'International American University - Los Angeles (IAULA)': campuses.find(c => c.campusName === 'International American University - Los Angeles (IAULA)'),
      'International American University - Orange County (IAUOC)': campuses.find(c => c.campusName === 'International American University - Orange County (IAUOC)'),
      'International American University - San Diego (IAUSD)': campuses.find(c => c.campusName === 'International American University - San Diego (IAUSD)'),
      'International American University - LA International Airport (IAULAX)': campuses.find(c => c.campusName === 'International American University - LA International Airport (IAULAX)')
    };

    for (const programLevel of programLevels) {
      for (const intake of intakes) {
        // ✅ Find matching programLevelIntake
        const programLevelIntake = await this.programLevelIntakeModel.findOne({
          where: {
            universityId,
            programLevelId: programLevel.id,
            intakeId: intake.id
          },
          transaction
        });

        if (!programLevelIntake) {
          throw new Error(
            `No programLevelIntake found for intakeId=${intake.id}`
          );
        }

        // ✅ Now use programLevelIntakeId in the fee data
        // #region ESL / Pathway
        if(programLevel.programLevelName === 'ESL / Pathway'){
          feesData.push({
            universityId,
            campusId: campusMap['International American University - Los Angeles (IAULA)'].id,
            programLevelIntakeId: programLevelIntake.id, // ✅ Correct foreign key
            feeTitle: `Tuition Fee - ${intake.name}`,
            tuitionFee: 8100,
            creditFee: 225,
            semisterFee: 3600,
            yearlyFee: 5400,
            isActive: true,
            isRefundableToStudent: false,
            isVisibleToStudent: true
          });
          feesData.push({
            universityId,
            campusId: campusMap['International American University - Orange County (IAUOC)'].id,
            programLevelIntakeId: programLevelIntake.id, // ✅ Correct foreign key
            feeTitle: `Tuition Fee - ${intake.name}`,
            tuitionFee: 8100,
            creditFee: 225,
            semisterFee: 3600,
            yearlyFee: 5400,
            isActive: true,
            isRefundableToStudent: false,
            isVisibleToStudent: true
          });
          feesData.push({
            universityId,
            campusId: campusMap['International American University - San Diego (IAUSD)'].id,
            programLevelIntakeId: programLevelIntake.id, // ✅ Correct foreign key
            feeTitle: `Tuition Fee - ${intake.name}`,
            tuitionFee: 8100,
            creditFee: 225,
            semisterFee: 3600,
            yearlyFee: 5400,
          });
          feesData.push({
            universityId,
            campusId: campusMap['International American University - LA International Airport (IAULAX)'].id,
            programLevelIntakeId: programLevelIntake.id, // ✅ Correct foreign key
            feeTitle: `Tuition Fee - ${intake.name}`,
            tuitionFee: 8100,
            creditFee: 225,
            semisterFee: 2700,
            yearlyFee: 5400,
          });
          // #endregion ESL / Pathway
        } else if(programLevel.programLevelName === 'Certificate'){
          // #region Certificate
          feesData.push({
            universityId,
            campusId: campusMap['International American University - Los Angeles (IAULA)'].id,
            programLevelIntakeId: programLevelIntake.id, // ✅ Correct foreign key
            feeTitle: `Tuition Fee - ${intake.name}`,
            tuitionFee: 13500,
            creditFee: 300,
            semisterFee: 3600,
            yearlyFee: 7200,
          });
          feesData.push({
            universityId,
            campusId: campusMap['International American University - Orange County (IAUOC)'].id,
            programLevelIntakeId: programLevelIntake.id, // ✅ Correct foreign key
            feeTitle: `Tuition Fee - ${intake.name}`,
            tuitionFee: 13500,
            creditFee: 300,
            semisterFee: 3600,
            yearlyFee: 7200,
          });
          feesData.push({
            universityId,
            campusId: campusMap['International American University - San Diego (IAUSD)'].id,
            programLevelIntakeId: programLevelIntake.id, // ✅ Correct foreign key
            feeTitle: `Tuition Fee - ${intake.name}`,
            tuitionFee: 10125,
            creditFee: 225,
            semisterFee: 2700,
            yearlyFee: 5400,
          });
          feesData.push({
            universityId,
            campusId: campusMap['International American University - LA International Airport (IAULAX)'].id,
            programLevelIntakeId: programLevelIntake.id, // ✅ Correct foreign key
            feeTitle: `Tuition Fee - ${intake.name}`,
            tuitionFee: 10125,
            creditFee: 225,
            semisterFee: 2700,
            yearlyFee: 5400,
          });
          // #endregion Certificate
        } else if(programLevel.programLevelName === "Associate's Degree"){
          // #region Associate
          feesData.push({
            universityId,
            campusId: campusMap['International American University - Los Angeles (IAULA)'].id,
            programLevelIntakeId: programLevelIntake.id, // ✅ Correct foreign key
            feeTitle: `Tuition Fee - ${intake.name}`,
            tuitionFee: 18000,
            creditFee: 300,
            semisterFee: 3600,
            yearlyFee: 7200,
          });
          feesData.push({
            universityId,
            campusId: campusMap['International American University - Orange County (IAUOC)'].id,
            programLevelIntakeId: programLevelIntake.id, // ✅ Correct foreign key
            feeTitle: `Tuition Fee - ${intake.name}`,
            tuitionFee: 18000,
            creditFee: 300,
            semisterFee: 3600,
            yearlyFee: 7200,
          });
          feesData.push({
            universityId,
            campusId: campusMap['International American University - San Diego (IAUSD)'].id,
            programLevelIntakeId: programLevelIntake.id, // ✅ Correct foreign key
            feeTitle: `Tuition Fee - ${intake.name}`,
            tuitionFee: 13500,
            creditFee: 225,
            semisterFee: 2700,
            yearlyFee: 5400,
          });
          feesData.push({
            universityId,
            campusId: campusMap['International American University - LA International Airport (IAULAX)'].id,
            programLevelIntakeId: programLevelIntake.id, // ✅ Correct foreign key
            feeTitle: `Tuition Fee - ${intake.name}`,
            tuitionFee: 13500,
            creditFee: 225,
            semisterFee: 2700,
            yearlyFee: 5400,
          });
          // #endregion Associate
        } else if(programLevel.programLevelName === "Bachelor's Degree"){
          // #region Bachelor
          feesData.push({
            universityId,
            campusId: campusMap['International American University - Los Angeles (IAULA)'].id,
            programLevelIntakeId: programLevelIntake.id, // ✅ Correct foreign key
            feeTitle: `Tuition Fee - ${intake.name}`,
            tuitionFee: 36000,
            creditFee: 300,
            semisterFee: 3600,
            yearlyFee: 7200,
          });
          feesData.push({
            universityId,
            campusId: campusMap['International American University - Orange County (IAUOC)'].id,
            programLevelIntakeId: programLevelIntake.id, // ✅ Correct foreign key
            feeTitle: `Tuition Fee - ${intake.name}`,
            tuitionFee: 36000,
            creditFee: 300,
            semisterFee: 3600,
            yearlyFee: 7200,
          });
          feesData.push({
            universityId,
            campusId: campusMap['International American University - San Diego (IAUSD)'].id,
            programLevelIntakeId: programLevelIntake.id, // ✅ Correct foreign key
            feeTitle: `Tuition Fee - ${intake.name}`,
            tuitionFee: 27000,
            creditFee: 225,
            semisterFee: 2700,
            yearlyFee: 5400,
          });
          feesData.push({
            universityId,
            campusId: campusMap['International American University - LA International Airport (IAULAX)'].id,
            programLevelIntakeId: programLevelIntake.id, // ✅ Correct foreign key
            feeTitle: `Tuition Fee - ${intake.name}`,
            tuitionFee: 27000,
            creditFee: 225,
            semisterFee: 2700,
            yearlyFee: 5400,
          });
          // #endregion Bachelor
        } else if(programLevel.programLevelName === "Master's Degree"){
          // #region Master
          feesData.push({
            universityId,
            campusId: campusMap['International American University - Los Angeles (IAULA)'].id,
            programLevelIntakeId: programLevelIntake.id, // ✅ Correct foreign key
            feeTitle: `Tuition Fee - ${intake.name}`,
            tuitionFee: 14400,
            creditFee: 400,
            semisterFee: 3600,
            yearlyFee: 7200,
          });
          feesData.push({
            universityId,
            campusId: campusMap['International American University - Orange County (IAUOC)'].id,
            programLevelIntakeId: programLevelIntake.id, // ✅ Correct foreign key
            feeTitle: `Tuition Fee - ${intake.name}`,
            tuitionFee: 14400,
            creditFee: 400,
            semisterFee: 3600,
            yearlyFee: 7200,
          });
          feesData.push({
            universityId,
            campusId: campusMap['International American University - San Diego (IAUSD)'].id,
            programLevelIntakeId: programLevelIntake.id, // ✅ Correct foreign key
            feeTitle: `Tuition Fee - ${intake.name}`,
            tuitionFee: 12600,
            creditFee: 350,
            semisterFee: 3150,
            yearlyFee: 6300,
          });
          feesData.push({
            universityId,
            campusId: campusMap['International American University - LA International Airport (IAULAX)'].id,
            programLevelIntakeId: programLevelIntake.id, // ✅ Correct foreign key
            feeTitle: `Tuition Fee - ${intake.name}`,
            tuitionFee: 12600,
            creditFee: 350,
            semisterFee: 3150,
            yearlyFee: 6300,
          });
          // #endregion Master
        } else if(programLevel.programLevelName === 'DBA'){
          // #region DBA
          feesData.push({
            universityId,
            campusId: campusMap['International American University - Los Angeles (IAULA)'].id,
            programLevelIntakeId: programLevelIntake.id, // ✅ Correct foreign key
            feeTitle: `Tuition Fee - ${intake.name}`,
            tuitionFee: 25500,
            creditFee: 425,
            semisterFee: 3825,
            yearlyFee: 7650,
          });
          feesData.push({
            universityId,
            campusId: campusMap['International American University - Orange County (IAUOC)'].id,
            programLevelIntakeId: programLevelIntake.id, // ✅ Correct foreign key
            feeTitle: `Tuition Fee - ${intake.name}`,
            tuitionFee: 25500,
            creditFee: 425,
            semisterFee: 3825,
            yearlyFee: 7650,
          });
          feesData.push({
            universityId,
            campusId: campusMap['International American University - San Diego (IAUSD)'].id,
            programLevelIntakeId: programLevelIntake.id, // ✅ Correct foreign key
            feeTitle: `Tuition Fee - ${intake.name}`,
            tuitionFee: 22500,
            creditFee: 375,
            semisterFee: 3375,
            yearlyFee: 6750,
          });
          feesData.push({
            universityId,
            campusId: campusMap['International American University - LA International Airport (IAULAX)'].id,
            programLevelIntakeId: programLevelIntake.id, // ✅ Correct foreign key
            feeTitle: `Tuition Fee - ${intake.name}`,
            tuitionFee: 22500,
            creditFee: 375,
            semisterFee: 3375,
            yearlyFee: 6750,
          });
          // #endregion DBA
        } else if(programLevel.programLevelName === 'DM'){
          // #region DM
          feesData.push({
            universityId,
            campusId: campusMap['International American University - Los Angeles (IAULA)'].id,
            programLevelIntakeId: programLevelIntake.id, // ✅ Correct foreign key
            feeTitle: `Tuition Fee - ${intake.name}`,
            tuitionFee: 18900,
            creditFee: 450,
            semisterFee: 4050,
            yearlyFee: 8100,
          });
          feesData.push({
            universityId,
            campusId: campusMap['International American University - Orange County (IAUOC)'].id,
            programLevelIntakeId: programLevelIntake.id, // ✅ Correct foreign key
            feeTitle: `Tuition Fee - ${intake.name}`,
            tuitionFee: 18900,
            creditFee: 450,
            semisterFee: 4050,
            yearlyFee: 8100,
          });
          feesData.push({
            universityId,
            campusId: campusMap['International American University - San Diego (IAUSD)'].id,
            programLevelIntakeId: programLevelIntake.id, // ✅ Correct foreign key
            feeTitle: `Tuition Fee - ${intake.name}`,
            tuitionFee: 18900,
            creditFee: 450,
            semisterFee: 4050,
            yearlyFee: 8100,
          });
          feesData.push({
            universityId,
            campusId: campusMap['International American University - LA International Airport (IAULAX)'].id,
            programLevelIntakeId: programLevelIntake.id, // ✅ Correct foreign key
            feeTitle: `Tuition Fee - ${intake.name}`,
            tuitionFee: 18900,
            creditFee: 450,
            semisterFee: 4050,
            yearlyFee: 8100,
          });
          // #endregion DM
        }
      }
    }

    // Add missing fields to all fee entries
    const enrichedFeesData = feesData.map(fee => ({
      ...fee,
      isActive: true,
      isRefundableToStudent: false,
      isVisibleToStudent: true
    }));

    return await this.feeModel.bulkCreate(enrichedFeesData, { transaction });
  }

  private async createCourses(
    universityId: number,
    fieldOfStudies: any[],
    programLevels: any[],
    fees: any[],
    transaction: any
  ) {
    const coursesData = [
      {
        universityId,
        courseTitle: 'Pathway Program',
        fieldOfStudyId: fieldOfStudies[0].id,
        format: 'On-Campus',
        programLevelId: programLevels[0].id,
        lastAcademic: '12th grade',
        minimumGpa: '2.0',
      },
      {
        universityId,
        courseTitle: 'English as a Second Language (ESL)',
        fieldOfStudyId: fieldOfStudies[0].id,
        format: 'On-Campus',
        programLevelId: programLevels[0].id,
        lastAcademic: '12th grade',
        minimumGpa: '2.0',
      },
      {
        universityId,
        courseTitle: 'Certificate of Accounting (C.Acc.)',
        fieldOfStudyId: fieldOfStudies[1].id,
        format: 'On-Campus',
        programLevelId: programLevels[1].id,
        lastAcademic: '12th grade',
        minimumGpa: '2.0',
        additionalRequirements: 'Study Gap 6 Years'
      },
      {
        universityId,
        courseTitle: 'Certificate of Business (CBus)',
        fieldOfStudyId: fieldOfStudies[2].id,
        format: 'On-Campus',
        programLevelId: programLevels[1].id,
        lastAcademic: '12th grade',
        minimumGpa: '2.0',
        additionalRequirements: 'Study Gap 6 Years'
      },
      {
        universityId,
        courseTitle: 'Associate of Science in Business Administration (ASBA)',
        fieldOfStudyId: fieldOfStudies[2].id,
        format: 'On-Campus',
        programLevelId: programLevels[2].id,
        lastAcademic: '12th grade',
        minimumGpa: '2.0',
        additionalRequirements: 'Study Gap 6 Years'
      },
      {
        universityId,
        courseTitle: 'Bachelor of Business Administration (BBA)',
        fieldOfStudyId: fieldOfStudies[2].id,
        format: 'On-Campus',
        programLevelId: programLevels[3].id,
        lastAcademic: '12th grade',
        minimumGpa: '2.0',
        additionalRequirements: 'Study Gap 6 Years'
      },
      {
        universityId,
        courseTitle: 'Bachelor of Business Administration in Accounting (B.B.A.-Acc.)',
        fieldOfStudyId: fieldOfStudies[2].id,
        format: 'On-Campus',
        programLevelId: programLevels[3].id,
        lastAcademic: '12th grade',
        minimumGpa: '2.0',
        additionalRequirements: 'Study Gap 6 Years'
      },
      {
        universityId,
        courseTitle: 'Bachelor of Information (BIT)',
        fieldOfStudyId: fieldOfStudies[3].id,
        format: 'On-Campus',
        programLevelId: programLevels[3].id,
        lastAcademic: '12th grade',
        minimumGpa: '2.0',
        additionalRequirements: 'Study Gap 6 Years'
      },
      {
        universityId,
        courseTitle: 'Master of Business Administration (MBA)',
        fieldOfStudyId: fieldOfStudies[3].id,
        format: 'On-Campus',
        programLevelId: programLevels[4].id,
        lastAcademic: 'Bachelor`s Program',
        minimumGpa: '2.5',
        additionalRequirements: 'Study Gap 12 Years'
      },
      {
        universityId,
        courseTitle: 'Master of Business Administration in Business Analytics (MBA-BAn)',
        fieldOfStudyId: fieldOfStudies[3].id,
        format: 'On-Campus',
        programLevelId: programLevels[4].id,
        lastAcademic: 'Bachelor`s Program',
        minimumGpa: '2.5',
        additionalRequirements: 'Study Gap 12 Years'
      },
      {
        universityId,
        courseTitle: 'Master of Business Administration in Management Information Systems (MBA-MIS)',
        fieldOfStudyId: fieldOfStudies[3].id,
        format: 'On-Campus',
        programLevelId: programLevels[4].id,
        lastAcademic: 'Bachelor`s Program',
        minimumGpa: '2.5',
        additionalRequirements: 'Study Gap 12 Years'
      },
      {
        universityId,
        courseTitle: 'Doctor of Business Administration (DBA)',
        fieldOfStudyId: fieldOfStudies[3].id,
        format: 'On-Campus',
        programLevelId: programLevels[5].id,
        lastAcademic: 'Bachelor`s Program',
        additionalRequirements: 'Accepted any with proper explanation'
      },
      {
        universityId,
        courseTitle: 'Doctor of Management (DM)',
        fieldOfStudyId: fieldOfStudies[3].id,
        format: 'On-Campus',
        programLevelId: programLevels[6].id,
        lastAcademic: 'Bachelor`s Program',
        additionalRequirements: 'Accepted any with proper explanation'
      },
    ];

    // Add missing fields to all course entries
    const enrichedCoursesData = coursesData.map(course => ({
      ...course,
      isActive: true
    }));

    return await this.courseModel.bulkCreate(enrichedCoursesData, { transaction });
  }

  private async createUniversityContact(
    universityId: number,
    transaction: any
  ) {
    const contactData = {
      universityId: universityId.toString(),
      contactName: 'John Smith',
      designation: 'Admissions Officer',
      email: '<EMAIL>',
      contactNumber: '+1-555-123-4567',
      alternativeEmail: '<EMAIL>'
    };

    return await this.contactModel.create(contactData, { transaction });
  }

  private async createUniversityGeneral(
    universityId: number,
    transaction: any
  ) {
    const generalData = {
      universityId: universityId.toString(),
      tuitionFeeDiscount: true,
      financialAidAcceptance: true,
      scholarshipOpportunity: true,
      accommodationStatus: true,
      employmentOpportunities: true,
      activeEnrollment: true,
      universityAgreement: [
        'https://example.com/uploads/agreement1.pdf',
        'https://example.com/uploads/agreement2.pdf'
      ],
      universityFeatures: [
        'https://example.com/uploads/1.jpg',
        'https://example.com/uploads/2.jpg',
        'https://example.com/uploads/3.jpg',
        'https://example.com/uploads/4.jpg',
        'https://example.com/uploads/5.jpg'
      ],
      universityProspectus: [
        'https://example.com/uploads/Fall%20Admission%202025.jpg',
        'https://example.com/uploads/Summer%20Admission%202025.jpg'
      ]
    };

    return await this.generalModel.create(generalData, { transaction });
  }

  private async createAlumni(universityId: number, transaction: any) {
    const alumniData = [
      {
        userId: '1',
        universityId,
        name: 'Jane Doe',
        organizationName: 'Google',
        designation: 'Software Engineer',
        imageUrl: 'https://cdn.com/jane.jpg'
      },
      {
        userId: '1',
        universityId,
        name: 'John Smith',
        organizationName: 'Amazon',
        designation: 'Cloud Architect',
        imageUrl: 'https://cdn.com/john.jpg'
      },
      {
        userId: '1',
        universityId,
        name: 'Alice Johnson',
        organizationName: 'Microsoft',
        designation: 'Product Manager',
        imageUrl: 'https://cdn.com/alice.jpg'
      }
    ];

    return await this.alumniModel.bulkCreate(alumniData, { transaction });
  }
  private async createUniversityCommission(
    universityId: number,
    userId: string,
    programLevels: any[],
    transaction: any
  ) {
    // 1. Create university commission
    const universityCommission = await this.universityCommissionModel.create(
      {
        universityId: universityId.toString(),
        userId,
        currency: 'USD',
        paymentFrequency: 'monthly',
        paymentTerm: 'net30',
        paymentMethod: 'bank_transfer',
        commissionPayoutCycle: 'quarterly',
        commissionPeriod: 'academic_year'
      },
      { transaction }
    );

    // 2. Create program commissions
    const programCommission = await this.programCommissionModel.create(
      {
        commissionId: universityCommission.id,
        commissionName: 'abc',
        programLevelId: programLevels[0].id, // Use first program level dynamically
        commissionType: 'fixed',
        commissionValue: 80,
        startDate: '2012-12-12',
        endDate: '2012-12-12',
        status: 'active'
      },
      { transaction }
    );

    // 3. Create student base commissions
    await this.studentBaseCommissionModel.bulkCreate(
      [
        {
          programCommissionId: programCommission.id,
          studentNumber: 5,
          commission: '20%'
        }
      ],
      { transaction }
    );

    return universityCommission;
  }

  /**
   * Creates CourseFeeCategory mappings between courses and fees based on program level IDs
   * This establishes the many-to-many relationship between courses and tuition fees
   */
  private async createCourseFeeMappings(
    courses: any[],
    fees: any[],
    transaction: any
  ) {
    const courseFeeMappings = [];

    // Get all program level intakes for efficient lookup
    const programLevelIntakes = await this.programLevelIntakeModel.findAll({
      where: {
        id: fees.map(fee => fee.programLevelIntakeId).filter(Boolean)
      },
      transaction
    });

    // Create a lookup map for faster access
    const programLevelIntakeMap = new Map();
    programLevelIntakes.forEach(pli => {
      programLevelIntakeMap.set(pli.id, pli);
    });

    for (const course of courses) {
      // Find all fees that match the course's program level
      const matchingFees = fees.filter(fee => {
        const programLevelIntake = programLevelIntakeMap.get(fee.programLevelIntakeId);
        return programLevelIntake && programLevelIntake.programLevelId === course.programLevelId;
      });

      // Create mappings for each matching fee
      for (const fee of matchingFees) {
        courseFeeMappings.push({
          courseId: course.id,
          feeCategoryId: fee.id
        });
      }
    }

    if (courseFeeMappings.length > 0) {
      await this.courseFeeCategoryModel.bulkCreate(courseFeeMappings, { transaction });
      this.logger.log(`Created ${courseFeeMappings.length} course-fee mappings`);
    } else {
      this.logger.warn('No course-fee mappings created - check program level relationships');
    }
  }

  private async createIntakeSessions(intakes: any[], transaction: any) {
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const nextYear = currentYear + 1;

    const intakeSessionsData: any[] = [];

    for (const intake of intakes) {
      const endDateStr = intake.endDate; // e.g., "August 18"
      const currentYearEndDate = new Date(`${endDateStr}, ${currentYear}`);

      const isActive = currentDate > currentYearEndDate;

      intakeSessionsData.push({
        intakeId: intake.id,
        name: `${intake.name} ${nextYear}`,
        isActive,
        activeYear: nextYear.toString(),
      });
    }

    if (intakeSessionsData.length === 0) return [];

    return await this.intakeSessionModel.bulkCreate(intakeSessionsData, { transaction });
  }

  private async createCourseIntakeSessionMappings(
    courses: any[],
    intakes: any[],
    transaction: any
  ) {
    // Strategy: link each course to all sessions of its program level's intakes by name match
    // We created sessions for each intake with name `${intake.name} ${nextYear}`. We'll link all sessions belonging to intakes found.
    const allSessions = await this.intakeSessionModel.findAll({ transaction });

    // Create a map of intakeId -> sessions[] for quick lookup
    const intakeIdToSessions = new Map<number, any[]>();
    for (const session of allSessions) {
      const intakeId = (session as any).intakeId;
      if (!intakeIdToSessions.has(intakeId)) intakeIdToSessions.set(intakeId, []);
      intakeIdToSessions.get(intakeId)!.push(session);
    }

    const joinRows: { courseId: number; intakeSessionId: number }[] = [];

    // For simplicity, link each course to all sessions of all intakes (can be refined if needed)
    // If you want to filter by program level or other criteria, adjust here.
    const allIntakeSessions = Array.from(intakeIdToSessions.values()).flat();
    for (const course of courses) {
      for (const session of allIntakeSessions) {
        joinRows.push({ courseId: Number(course.id), intakeSessionId: Number(session.id) });
      }
    }

    if (joinRows.length > 0) {
      // Deduplicate pairs in case of any duplicates
      const unique = new Map<string, { courseId: number; intakeSessionId: number }>();
      for (const row of joinRows) {
        unique.set(`${row.courseId}-${row.intakeSessionId}`, row);
      }
      await this.courseIntakeSessionsModel.bulkCreate(Array.from(unique.values()), { transaction, ignoreDuplicates: true as any });
    }
  }
}
