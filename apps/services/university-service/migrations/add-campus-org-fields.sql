-- Migration: Add organization and admin user fields to campuses table
-- Date: 2024-01-XX
-- Description: Add organizationId and adminUserId fields to support campus organizations and admin users

-- Add new columns to campuses table
ALTER TABLE campuses 
ADD COLUMN "organizationId" BIGINT,
ADD COLUMN "adminUserId" BIGINT;

-- Add comments for documentation
COMMENT ON COLUMN campuses."organizationId" IS 'Reference to the campus organization in auth service';
COMMENT ON COLUMN campuses."adminUserId" IS 'Reference to the campus admin user in auth service';

-- Create indexes for better performance
CREATE INDEX idx_campuses_organization_id ON campuses("organizationId");
CREATE INDEX idx_campuses_admin_user_id ON campuses("adminUserId");

-- Add foreign key constraints (optional - uncomment if you want referential integrity)
-- ALTER TABLE campuses ADD CONSTRAINT fk_campuses_organization 
--   FOREIGN KEY ("organizationId") REFERENCES organizations(id);
-- 
-- ALTER TABLE campuses ADD CONSTRAINT fk_campuses_admin_user 
--   FOREIGN KEY ("adminUserId") REFERENCES users(id); 