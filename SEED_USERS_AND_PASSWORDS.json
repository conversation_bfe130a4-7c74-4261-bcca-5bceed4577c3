{"seedUsers": {"authService": {"description": "Users created by auth-service seed module", "users": [{"id": 1, "name": "Super Admin", "email": "<EMAIL>", "password": "superadmin123", "role": "Super Admin", "organization": "ApplyGoal", "status": "active", "source": "auth-service seed"}, {"id": 19, "name": "Application Officer", "email": "<EMAIL>", "password": "appofficer123", "role": "Application Officer", "department": "ApplyGoal Admission Team", "organization": "ApplyGoal", "status": "active", "source": "auth-service seed"}]}, "studentsService": {"description": "Student users created by students-service seed module", "password": "TempPassword123!", "note": "All students use the same temporary password", "users": [{"id": 2, "name": "<PERSON>", "email": "<EMAIL>", "password": "TempPassword123!", "role": "Student", "department": "Students", "organization": "ApplyGoal", "status": "active", "source": "students-service seed", "studentId": "ST2025001", "nationality": "Canadian"}, {"id": 3, "name": "<PERSON>", "email": "<EMAIL>", "password": "TempPassword123!", "role": "Student", "department": "Students", "organization": "ApplyGoal", "status": "active", "source": "students-service seed", "studentId": "ST2025002", "nationality": "American"}, {"id": 4, "name": "<PERSON>", "email": "<EMAIL>", "password": "TempPassword123!", "role": "Student", "department": "Students", "organization": "ApplyGoal", "status": "active", "source": "students-service seed", "studentId": "ST2025003", "nationality": "Saudi"}]}, "universityService": {"description": "University and campus users created by university-service seed module", "users": [{"id": 5, "name": "Test University", "email": "<EMAIL>", "password": "DefaultPassword123!", "role": "University Admin", "organization": "Test University", "status": "active", "source": "university-service seed"}, {"id": 6, "name": "International American University", "email": "<EMAIL>", "password": "DefaultPassword123!", "role": "University Admin", "organization": "International American University", "status": "active", "source": "university-service seed"}, {"id": 7, "name": "International American University - Los Angeles (IAULA) Administrator", "email": "<EMAIL>", "password": "DefaultPassword123!", "role": "Campus Admin", "organization": "IAULA Campus Organization", "status": "active", "source": "university-service seed"}, {"id": 8, "name": "International American University - Los Angeles (IAULA) DSO", "email": "<EMAIL>", "password": "DefaultPassword123!", "role": "Designated School Official (DSO)", "organization": "IAULA Campus Organization", "status": "active", "source": "university-service seed"}, {"id": 9, "name": "International American University - Los Angeles (IAULA) Admission Team", "email": "<EMAIL>", "password": "DefaultPassword123!", "role": "Campus Admission Team", "organization": "IAULA Campus Organization", "status": "active", "source": "university-service seed"}, {"id": 10, "name": "International American University - Orange County (IAUOC) Administrator", "email": "<EMAIL>", "password": "DefaultPassword123!", "role": "Campus Admin", "organization": "IAUOC Campus Organization", "status": "active", "source": "university-service seed"}, {"id": 11, "name": "International American University - Orange County (IAUOC) DSO", "email": "<EMAIL>", "password": "DefaultPassword123!", "role": "Designated School Official (DSO)", "organization": "IAUOC Campus Organization", "status": "active", "source": "university-service seed"}, {"id": 12, "name": "International American University - Orange County (IAUOC) Admission Team", "email": "<EMAIL>", "password": "DefaultPassword123!", "role": "Campus Admission Team", "organization": "IAUOC Campus Organization", "status": "active", "source": "university-service seed"}, {"id": 13, "name": "International American University - San <PERSON> (IAUSD) Administrator", "email": "<EMAIL>", "password": "DefaultPassword123!", "role": "Campus Admin", "organization": "IAUSD Campus Organization", "status": "active", "source": "university-service seed"}, {"id": 14, "name": "International American University - San <PERSON> (IAUSD) DSO", "email": "<EMAIL>", "password": "DefaultPassword123!", "role": "Designated School Official (DSO)", "organization": "IAUSD Campus Organization", "status": "active", "source": "university-service seed"}, {"id": 15, "name": "International American University - San Diego (IAUSD) Admission Team", "email": "<EMAIL>", "password": "DefaultPassword123!", "role": "Campus Admission Team", "organization": "IAUSD Campus Organization", "status": "active", "source": "university-service seed"}, {"id": 16, "name": "International American University - LA International Airport (IAULAX) Administrator", "email": "<EMAIL>", "password": "DefaultPassword123!", "role": "Campus Admin", "organization": "IAULAX Campus Organization", "status": "active", "source": "university-service seed"}, {"id": 17, "name": "International American University - LA International Airport (IAULAX) DSO", "email": "<EMAIL>", "password": "DefaultPassword123!", "role": "Designated School Official (DSO)", "organization": "IAULAX Campus Organization", "status": "active", "source": "university-service seed"}, {"id": 18, "name": "International American University - LA International Airport (IAULAX) Admission Team", "email": "<EMAIL>", "password": "DefaultPassword123!", "role": "Campus Admission Team", "organization": "IAULAX Campus Organization", "status": "active", "source": "university-service seed"}]}}, "summary": {"totalUsers": 18, "byService": {"authService": 2, "studentsService": 3, "universityService": 13}, "byRole": {"Super Admin": 1, "Application Officer": 1, "Student": 3, "University Admin": 2, "Campus Admin": 4, "Designated School Official (DSO)": 4, "Campus Admission Team": 4}, "byOrganization": {"ApplyGoal": 5, "Test University": 1, "International American University": 1, "Campus Organizations": 11}}, "notes": {"passwordGeneration": {"authService": "Passwords are hardcoded in seed files", "studentsService": "All students use the same temporary password", "universityService": "Passwords are generated by auth-service when users are created"}, "status": "All users are created with 'active' status", "lastUpdated": "2025-08-11", "source": "Analysis of seed files and database queries"}}