import { IsString, IsN<PERSON>ber, IsEnum, IsOptional, IsDateString } from 'class-validator';

export enum ApplicationStatus {
  APPLIED = 'applied',
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  WITHDRAWN = 'withdrawn'
}

export class CreateApplicationDto {
  @IsString()
  studentId!: string;

  @IsNumber()
  universityId!: number;

  @IsNumber()
  universityCountryId!: number;

  @IsNumber()
  universityCountryCampus!: number;

  @IsNumber()
  programId!: number;

  @IsNumber()
  intakeId!: number;

  @IsNumber()
  courseId!: number;

  @IsString()
  @IsOptional()
  note?: string;

  @IsEnum(ApplicationStatus)
  @IsOptional()
  status?: ApplicationStatus;

  @IsString()
  @IsOptional()
  studentI20Url?: string;

  // Document status fields
  @IsString()
  @IsOptional()
  studentI20UrlStatus?: string;

  @IsString()
  @IsOptional()
  i94UrlStatus?: string;

  @IsString()
  @IsOptional()
  i797cUrlStatus?: string;

  // Created by field
  @IsNumber()
  @IsOptional()
  createdBy?: number | null;
}

export class UpdateApplicationDto {
  @IsString()
  @IsOptional()
  studentId?: string;

  @IsNumber()
  @IsOptional()
  universityId?: number;

  @IsNumber()
  @IsOptional()
  universityCountryId?: number;

  @IsNumber()
  @IsOptional()
  universityCountryCampus?: number;

  @IsNumber()
  @IsOptional()
  programId?: number;

  @IsNumber()
  @IsOptional()
  intakeId?: number;

  @IsNumber()
  @IsOptional()
  courseId?: number;

  @IsString()
  @IsOptional()
  note?: string;

  @IsEnum(ApplicationStatus)
  @IsOptional()
  status?: ApplicationStatus;

  @IsString()
  @IsOptional()
  studentI20Url?: string;

  // Document status fields
  @IsString()
  @IsOptional()
  studentI20UrlStatus?: string;

  @IsString()
  @IsOptional()
  i94UrlStatus?: string;

  @IsString()
  @IsOptional()
  i797cUrlStatus?: string;

  // Created by field
  @IsNumber()
  @IsOptional()
  createdBy?: number | null;
}

export class ApplicationResponseDto {
  id!: number;
  studentId!: string;
  universityId!: number;
  universityCountryId!: number;
  universityCountryCampus!: number;
  programId!: number;
  intakeId!: number;
  courseId!: number;
  note!: string;
  status!: ApplicationStatus;
  appliedAt!: Date;
  updatedAt!: Date;
  createdAt!: Date;
  createdBy?: number | null;
} 