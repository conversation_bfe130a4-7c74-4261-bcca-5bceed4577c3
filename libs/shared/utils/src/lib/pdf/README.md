# PDF Converter Service

A utility service for converting HTML templates to PDF with dynamic data support.

## Features

- Convert HTML templates to PDF with dynamic data using Handlebars
- Support for students-service HTML templates
- Configurable PDF generation options
- Asset handling (CSS, images)
- Template processing without PDF generation

## Installation

To enable actual PDF generation, install puppeteer:

```bash
pnpm add puppeteer
```

## Usage

### Basic Usage

```typescript
import { PdfConverterService } from '@apply-goal-backend/utils';

@Injectable()
export class MyService {
  constructor(private pdfConverter: PdfConverterService) {}

  async generatePdf() {
    const data = {
      studentName: '<PERSON>',
      universityName: 'International American University',
      program: 'Computer Science',
      // ... other dynamic data
    };

    const pdfBuffer = await this.pdfConverter.convertStudentsTemplateToPdf(
      'CAL-Page-4', // template name
      data,
      '/path/to/output.pdf', // optional output path
      {
        format: 'A4',
        margin: {
          top: '1cm',
          right: '1cm',
          bottom: '1cm',
          left: '1cm'
        }
      }
    );

    return pdfBuffer;
  }
}
```

### Custom HTML Template

```typescript
const config = {
  templatePath: '/path/to/template.html',
  assetsPath: '/path/to/assets', // optional
  data: {
    title: 'My Document',
    content: 'Dynamic content here'
  },
  outputPath: '/path/to/output.pdf', // optional
  options: {
    format: 'A4',
    printBackground: true
  }
};

const pdfBuffer = await this.pdfConverter.convertHtmlToPdf(config);
```

### HTML String to PDF

```typescript
const htmlContent = `
  <html>
    <body>
      <h1>{{title}}</h1>
      <p>{{content}}</p>
    </body>
  </html>
`;

const data = {
  title: 'My Document',
  content: 'This is dynamic content'
};

const pdfBuffer = await this.pdfConverter.convertHtmlStringToPdf(
  htmlContent,
  data
);
```

### Template Processing Only

```typescript
// Process template without generating PDF
const processedHtml = await this.pdfConverter.processStudentsTemplate(
  'CAL-Page-4',
  { studentName: 'John Doe' }
);
```

## Template Structure

Students-service templates should be placed in:
```
apps/services/students-service/src/assets/html-templates/
├── template-name/
│   ├── index.html
│   ├── style.css
│   ├── vars.css
│   └── assets/
│       ├── images/
│       └── icons/
```

## Handlebars Template Syntax

Use Handlebars syntax for dynamic content:

```html
<div class="student-info">
  <h1>{{studentName}}</h1>
  <p>Program: {{program}}</p>
  <p>University: {{universityName}}</p>
  
  {{#if hasScholarship}}
    <p>Scholarship: {{scholarshipAmount}}</p>
  {{/if}}
  
  {{#each courses}}
    <li>{{name}} - {{credits}} credits</li>
  {{/each}}
</div>
```

## PDF Generation Options

```typescript
interface PdfGenerationOptions {
  format?: 'A4' | 'Letter' | 'Legal';
  margin?: {
    top?: string;
    right?: string;
    bottom?: string;
    left?: string;
  };
  printBackground?: boolean;
  preferCSSPageSize?: boolean;
  displayHeaderFooter?: boolean;
  headerTemplate?: string;
  footerTemplate?: string;
}
```

## Available Templates

Get list of available templates:

```typescript
const templates = await this.pdfConverter.getAvailableTemplates();
console.log(templates); // ['CAL-Page-4', ...]
```

## Error Handling

The service includes comprehensive error handling:

```typescript
try {
  const pdfBuffer = await this.pdfConverter.convertStudentsTemplateToPdf(
    'template-name',
    data
  );
} catch (error) {
  console.error('PDF generation failed:', error.message);
}
```

## Notes

- Currently returns placeholder PDF buffers until puppeteer is installed
- Install puppeteer to enable actual PDF generation
- Templates support Handlebars syntax for dynamic content
- Assets (CSS, images) are automatically handled when assetsPath is provided 