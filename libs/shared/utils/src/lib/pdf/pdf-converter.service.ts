import { Injectable, Logger } from '@nestjs/common';
import * as fs from 'fs';
import * as path from 'path';
import * as Handlebars from 'handlebars';
import * as puppeteer from 'puppeteer';

export interface PdfGenerationOptions {
  format?: 'A4' | 'Letter' | 'Legal';
  margin?: {
    top?: string;
    right?: string;
    bottom?: string;
    left?: string;
  };
  printBackground?: boolean;
  preferCSSPageSize?: boolean;
  displayHeaderFooter?: boolean;
  headerTemplate?: string;
  footerTemplate?: string;
}

export interface TemplateData {
  [key: string]: any;
}

export interface HtmlTemplateConfig {
  templatePath: string;
  assetsPath?: string;
  data: TemplateData;
  outputPath?: string;
  options?: PdfGenerationOptions;
}

@Injectable()
export class PdfConverterService {
  private readonly logger = new Logger(PdfConverterService.name);
  private browser: puppeteer.Browser | null = null;

  /**
   * Initialize puppeteer browser
   */
  private async getBrowser(): Promise<puppeteer.Browser> {
    if (!this.browser) {
      this.browser = await puppeteer.launch({
        headless: 'new',
        executablePath: process.env['PUPPETEER_EXECUTABLE_PATH'] || '/usr/bin/chromium-browser',
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu',
          '--disable-web-security',
          '--disable-features=VizDisplayCompositor'
        ]
      });
    }
    return this.browser;
  }

  /**
   * Convert HTML template to PDF with dynamic data
   */
  async convertHtmlToPdf(config: HtmlTemplateConfig): Promise<Buffer> {
    try {
      // Read the HTML template
      const htmlContent = await this.readHtmlTemplate(config.templatePath);
      
      // Compile template with Handlebars
      const template = Handlebars.compile(htmlContent);
      const processedHtml = template(config.data);

      this.logger.log('HTML template processed successfully');
      
      // Generate PDF using puppeteer
      const pdfBuffer = await this.generatePdfFromHtml(processedHtml, config.options);
      
      // Save to file if outputPath is provided
      if (config.outputPath) {
        await this.savePdfToFile(pdfBuffer, config.outputPath);
      }

      this.logger.log(`PDF generated successfully${config.outputPath ? ` and saved to ${config.outputPath}` : ''}`);
      return pdfBuffer;

    } catch (error) {
      this.logger.error('Error generating PDF:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new Error(`Failed to generate PDF: ${errorMessage}`);
    }
  }

  /**
   * Convert HTML string to PDF
   */
  async convertHtmlStringToPdf(
    htmlContent: string,
    data: TemplateData = {},
    options?: PdfGenerationOptions
  ): Promise<Buffer> {
    try {
      // Compile template with Handlebars
      const template = Handlebars.compile(htmlContent);
      const processedHtml = template(data);

      this.logger.log('HTML string processed successfully');
      
      // Generate PDF using puppeteer
      const pdfBuffer = await this.generatePdfFromHtml(processedHtml, options);
      
      this.logger.log('PDF generated successfully from HTML string');
      return pdfBuffer;

    } catch (error) {
      this.logger.error('Error generating PDF from HTML string:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new Error(`Failed to generate PDF from HTML string: ${errorMessage}`);
    }
  }

  /**
   * Generate PDF from HTML content using puppeteer
   */
  private async generatePdfFromHtml(htmlContent: string, options?: PdfGenerationOptions): Promise<Buffer> {
    const browser = await this.getBrowser();
    const page = await browser.newPage();

    try {
      // Set viewport for consistent rendering - A4 dimensions at 96 DPI
      await page.setViewport({
        width: 794, // A4 width in pixels at 96 DPI
        height: 1123, // A4 height in pixels at 96 DPI
        deviceScaleFactor: 1 // Reduced from 2 to avoid scaling issues
      });

      // Add additional CSS for better PDF rendering
      const enhancedHtml = this.addPdfEnhancementCss(htmlContent);

      this.logger.debug(`Enhanced HTML length: ${enhancedHtml.length}`);

      // Set content and wait for everything to load
      await page.setContent(enhancedHtml, {
        waitUntil: ['networkidle0', 'domcontentloaded'],
        timeout: 90000 // Increased timeout
      });

      // Wait for images and fonts to load
      await page.evaluate(() => {
        return Promise.all([
          // Wait for images
          ...Array.from(document.images).map(img => {
            if (img.complete) return Promise.resolve();
            return new Promise(resolve => {
              img.onload = img.onerror = resolve;
              // Set a timeout for each image
              setTimeout(resolve, 5000);
            });
          }),
          // Wait for fonts
          document.fonts ? document.fonts.ready : Promise.resolve()
        ]);
      });

      // Additional wait for rendering
      await new Promise(resolve => setTimeout(resolve, 3000)); // Increased wait time

      // Configure PDF options with better defaults
      const pdfOptions: puppeteer.PDFOptions = {
        format: options?.format || 'A4',
        printBackground: options?.printBackground ?? true,
        preferCSSPageSize: options?.preferCSSPageSize ?? false, // Changed to false for better control
        displayHeaderFooter: options?.displayHeaderFooter ?? false,
        margin: {
          top: options?.margin?.top || '0.5cm',
          right: options?.margin?.right || '0.5cm',
          bottom: options?.margin?.bottom || '0.5cm',
          left: options?.margin?.left || '0.5cm'
        },
        // Add scale option for better rendering
        scale: 1.0,
        // Ensure tagged PDF for better accessibility and rendering
        tagged: true,
        // Optimize for print
        omitBackground: false
      };

      // Generate PDF
      const pdfBuffer = await page.pdf(pdfOptions);

      this.logger.log(`PDF generated successfully, size: ${pdfBuffer.length} bytes`);
      return pdfBuffer;

    } finally {
      await page.close();
    }
  }

  /**
   * Add CSS enhancements for better PDF rendering
   */
  private addPdfEnhancementCss(htmlContent: string): string {
    const enhancementCss = `
      <style data-source="pdf-enhancement">
        /* PDF Enhancement CSS */
        * {
          -webkit-print-color-adjust: exact !important;
          color-adjust: exact !important;
          box-sizing: border-box;
        }

        html, body {
          margin: 0;
          padding: 0;
          width: 100%;
          height: 100%;
          font-family: Arial, Helvetica, sans-serif;
          font-size: 12px;
          line-height: 1.4;
        }

        /* Fix positioning issues common in CAL templates */
        .cover-page, .cal-page-02 {
          width: 595px !important;
          height: 842px !important;
          position: relative !important;
          overflow: visible !important;
          margin: 0 auto;
        }

        /* Image optimization */
        img {
          max-width: 100% !important;
          height: auto !important;
          display: block !important;
        }

        /* Fix translate property for older browsers */
        [style*="translate:"] {
          transform: translateX(var(--translate-x, 0)) translateY(var(--translate-y, 0));
        }

        /* Table improvements */
        table {
          border-collapse: collapse;
          width: 100%;
        }

        /* Page break handling */
        .page-break, .cal-page {
          page-break-after: always;
          page-break-inside: avoid;
        }

        .cal-page:last-child {
          page-break-after: auto;
        }

        /* Prevent orphans and widows */
        p, div {
          orphans: 3;
          widows: 3;
        }

        /* Ensure backgrounds and colors are preserved */
        * {
          -webkit-print-color-adjust: exact !important;
          print-color-adjust: exact !important;
        }

        @page {
          size: A4;
          margin: 0.5cm;
        }

        @media print {
          .no-print {
            display: none !important;
          }

          body {
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
          }
        }
      </style>
    `;

    // Insert enhancement CSS at the beginning of head or before closing head tag
    if (htmlContent.includes('<head>')) {
      return htmlContent.replace('<head>', `<head>${enhancementCss}`);
    } else if (htmlContent.includes('</head>')) {
      return htmlContent.replace('</head>', `${enhancementCss}</head>`);
    } else {
      // If no head tag, add at the beginning
      return `${enhancementCss}${htmlContent}`;
    }
  }

  /**
   * Convert students-service HTML template to PDF
   */
  async convertStudentsTemplateToPdf(
    templateName: string,
    data: TemplateData,
    outputPath?: string,
    options?: PdfGenerationOptions
  ): Promise<Buffer> {
    const templatePath = path.join(
      process.cwd(),
      'apps/services/students-service/src/assets/html-templates',
      templateName,
      'index.html'
    );

    const assetsPath = path.join(
      process.cwd(),
      'apps/services/students-service/src/assets/html-templates',
      templateName,
      'assets'
    );

    return this.convertHtmlToPdf({
      templatePath,
      assetsPath,
      data,
      outputPath,
      options
    });
  }

  /**
   * Read HTML template file
   */
  private async readHtmlTemplate(templatePath: string): Promise<string> {
    try {
      return await fs.promises.readFile(templatePath, 'utf-8');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new Error(`Failed to read HTML template at ${templatePath}: ${errorMessage}`);
    }
  }

  /**
   * Save PDF buffer to file
   */
  private async savePdfToFile(pdfBuffer: Buffer, outputPath: string): Promise<void> {
    try {
      // Ensure directory exists
      const dir = path.dirname(outputPath);
      await fs.promises.mkdir(dir, { recursive: true });
      
      await fs.promises.writeFile(outputPath, pdfBuffer);
      this.logger.log(`PDF saved to: ${outputPath}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new Error(`Failed to save PDF to ${outputPath}: ${errorMessage}`);
    }
  }

  /**
   * Get available students-service templates
   */
  async getAvailableTemplates(): Promise<string[]> {
    const templatesPath = path.join(
      process.cwd(),
      'apps/services/students-service/src/assets/html-templates'
    );

    try {
      const items = await fs.promises.readdir(templatesPath, { withFileTypes: true });
      return items
        .filter(item => item.isDirectory())
        .map(item => item.name);
    } catch (error) {
      this.logger.warn(`Error reading templates directory: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return [];
    }
  }

  /**
   * Process HTML template with data (without PDF generation)
   */
  async processHtmlTemplate(
    templatePath: string,
    data: TemplateData
  ): Promise<string> {
    try {
      const htmlContent = await this.readHtmlTemplate(templatePath);
      const template = Handlebars.compile(htmlContent);
      return template(data);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new Error(`Failed to process HTML template: ${errorMessage}`);
    }
  }

  /**
   * Process students-service template with data (without PDF generation)
   */
  async processStudentsTemplate(
    templateName: string,
    data: TemplateData
  ): Promise<string> {
    const templatePath = path.join(
      process.cwd(),
      'apps/services/students-service/src/assets/html-templates',
      templateName,
      'index.html'
    );

    return this.processHtmlTemplate(templatePath, data);
  }

  /**
   * Cleanup browser instance
   */
  async onModuleDestroy() {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
    }
  }
} 
