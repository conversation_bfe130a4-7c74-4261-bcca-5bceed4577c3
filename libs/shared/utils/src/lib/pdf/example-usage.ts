import { Injectable } from '@nestjs/common';
import { PdfConverterService } from './pdf-converter.service';
import * as path from 'path';

/**
 * Example service showing how to integrate PDF converter with students-service
 */
@Injectable()
export class StudentDocumentService {
  constructor(private pdfConverter: PdfConverterService) {}

  /**
   * Generate student application PDF
   */
  async generateStudentApplicationPdf(studentData: any) {
    try {
      const templateData = {
        studentName: studentData.fullName,
        studentId: studentData.studentId,
        program: studentData.program,
        universityName: studentData.universityName,
        applicationDate: new Date().toLocaleDateString(),
        // Add more dynamic data as needed
        hasScholarship: studentData.scholarshipAmount > 0,
        scholarshipAmount: studentData.scholarshipAmount,
        courses: studentData.courses || [],
        // Contact information
        email: studentData.email,
        phone: studentData.phone,
        address: studentData.address
      };

      const pdfBuffer = await this.pdfConverter.convertStudentsTemplateToPdf(
        'CAL-Page-4', // template name
        templateData,
        `/tmp/student-application-${studentData.studentId}.pdf`, // output path
        {
          format: 'A4',
          margin: {
            top: '1cm',
            right: '1cm',
            bottom: '1cm',
            left: '1cm'
          },
          printBackground: true
        }
      );

      return {
        success: true,
        pdfBuffer,
        filename: `student-application-${studentData.studentId}.pdf`
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Generate student transcript PDF
   */
  async generateStudentTranscriptPdf(studentData: any) {
    try {
      const templateData = {
        studentName: studentData.fullName,
        studentId: studentData.studentId,
        program: studentData.program,
        universityName: studentData.universityName,
        graduationDate: studentData.graduationDate,
        gpa: studentData.gpa,
        totalCredits: studentData.totalCredits,
        courses: studentData.courses || [],
        // Academic information
        academicYear: studentData.academicYear,
        semester: studentData.semester,
        // University details
        universityAddress: studentData.universityAddress,
        universityPhone: studentData.universityPhone,
        universityEmail: studentData.universityEmail
      };

      const pdfBuffer = await this.pdfConverter.convertStudentsTemplateToPdf(
        'CAL-Page-4', // You can create a specific transcript template
        templateData,
        `/tmp/student-transcript-${studentData.studentId}.pdf`
      );

      return {
        success: true,
        pdfBuffer,
        filename: `student-transcript-${studentData.studentId}.pdf`
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Generate custom document PDF
   */
  async generateCustomDocumentPdf(
    templateName: string,
    data: any,
    outputPath?: string
  ) {
    try {
      const pdfBuffer = await this.pdfConverter.convertStudentsTemplateToPdf(
        templateName,
        data,
        outputPath
      );

      return {
        success: true,
        pdfBuffer,
        filename: outputPath ? path.basename(outputPath) : 'document.pdf'
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Get available templates
   */
  async getAvailableTemplates() {
    try {
      const templates = await this.pdfConverter.getAvailableTemplates();
      return {
        success: true,
        templates
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        templates: []
      };
    }
  }

  /**
   * Process template without generating PDF (useful for preview)
   */
  async previewTemplate(templateName: string, data: any) {
    try {
      const processedHtml = await this.pdfConverter.processStudentsTemplate(
        templateName,
        data
      );

      return {
        success: true,
        html: processedHtml
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
}

// Example usage in a controller
export class StudentDocumentController {
  constructor(private studentDocumentService: StudentDocumentService) {}

  async generateApplicationPdf(req: any, res: any) {
    const studentData = {
      fullName: 'John Doe',
      studentId: 'STU001',
      program: 'Computer Science',
      universityName: 'International American University',
      email: '<EMAIL>',
      phone: '******-123-4567',
      address: '123 Main St, City, State 12345',
      scholarshipAmount: 5000,
      courses: [
        { name: 'Introduction to Programming', credits: 3, grade: 'A' },
        { name: 'Data Structures', credits: 3, grade: 'A-' },
        { name: 'Algorithms', credits: 3, grade: 'B+' }
      ]
    };

    const result = await this.studentDocumentService.generateStudentApplicationPdf(studentData);

    if (result.success) {
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', `attachment; filename="${result.filename}"`);
      res.send(result.pdfBuffer);
    } else {
      res.status(500).json({ error: result.error });
    }
  }
} 