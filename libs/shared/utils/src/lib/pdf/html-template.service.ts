import { Injectable, Logger } from '@nestjs/common';
import * as puppeteer from 'puppeteer';
import * as fs from 'fs';
import * as path from 'path';
import * as Handlebars from 'handlebars';
import { PDFDocument } from 'pdf-lib';

export interface HtmlCalDocumentData {
  applicationId: number;
  name?: string;
  studentName: string;
  idNumber: string;
  passport: string;
  dateOfBirth: string;
  courseName: string;
  intake: string;
  campusName: string;
  tuitionFee: string;
  courseStartDate: string;
  courseEndDate: string;
  issueDate: string;
  invoiceId: string;
  invoiceDate: string;
  adminFee: string;
  programName: string;
  courseDate: string;
  universityName: string;
  totalAmount: string;
}

export interface HtmlAdmissionPackageData {
  studentId: string;
  studentName: string;
  applicationType: string;
  email: string;
  phone: string;
  dateOfBirth: string;
  passport: string;
  nid: string;
  presentAddress: string;
  permanentAddress: string;
  preferredSubject: string[];
  preferredCountry: string[];
  issueDate: string;
  fatherName: string;
  motherName: string;
  gender: string;
  maritalStatus: string;
  sponsor: string;
  emergencyContact: string;
}

export interface HtmlAdmDocumentData {
  studentId: string;
  studentName: string;
  applicationType: string;
  email: string;
  phone: string;
  dateOfBirth: string;
  passport: string;
  nid: string;
  presentAddress: string;
  permanentAddress: string;
  preferredSubject: string[];
  preferredCountry: string[];
  issueDate: string;
  fatherName: string;
  motherName: string;
  gender: string;
  maritalStatus: string;
  sponsor: string;
  emergencyContact: string;
  admNumber: string;
  admDate: string;
  universityName: string;
  courseName: string;
  intakeName: string;
  campusName: string;
  tuitionFee: string;
  courseStartDate: string;
  courseEndDate: string;
  // I-20 document URL for merging as last pages
  i20Url?: string;
  // Student I-20 document URL for merging as last pages
  studentI20Url?: string;
}

export interface HtmlAdmissionPortfolioData {
  studentId: string;
  studentName: string;
  applicationType: string;
  email: string;
  phone: string;
  dateOfBirth: string;
  passport: string;
  nid: string;
  presentAddress: string;
  permanentAddress: string;
  preferredSubject: string[];
  preferredCountry: string[];
  issueDate: string;
  fatherName: string;
  motherName: string;
  gender: string;
  maritalStatus: string;
  sponsor: string;
  emergencyContact: string;
  portfolioNumber: string;
  portfolioDate: string;
  universityName: string;
  courseName: string;
  intakeName: string;
  campusName: string;
  tuitionFee: string;
  courseStartDate: string;
  courseEndDate: string;
  requirementsDocuments: any;
  i20Url?: string;
  i94Url?: string;
  i797cUrl?: string;
  // ✅ NEW: Add sponsor and student personal info for NID comparison
  sponsorInfo?: {
    sponsor?: any;
    studentPersonalInfo?: any;
  };
}

export interface HtmlTemplateOptions {
  includeAllPages?: boolean;
  pageNumbers?: number[];
}

export interface HtmlGenerationResult {
  success: boolean;
  documentContent?: Buffer;
  contentType?: string;
  error?: string;
}

@Injectable()
export class HtmlTemplateService {
  private readonly logger = new Logger(HtmlTemplateService.name);

  // ✅ PERFORMANCE OPTIMIZATION: Browser instance reuse
  private static browserInstance: puppeteer.Browser | null = null;
  private static pagePool: puppeteer.Page[] = [];
  private static readonly MAX_PAGES = 5;
  private templateCache = new Map<string, string>();

  // ✅ PERFORMANCE OPTIMIZATION: Browser instance management
  private async getBrowser(): Promise<puppeteer.Browser> {
    if (!HtmlTemplateService.browserInstance) {
      HtmlTemplateService.browserInstance = await puppeteer.launch({
        headless: 'new',
        executablePath:
          process.env['PUPPETEER_EXECUTABLE_PATH'] ||
          '/usr/bin/chromium-browser',
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu',
          '--disable-web-security',
          '--disable-features=VizDisplayCompositor',
          '--memory-pressure-off',
          '--max_old_space_size=4096',
          '--disable-background-timer-throttling',
          '--disable-backgrounding-occluded-windows',
          '--disable-renderer-backgrounding',
          '--disable-extensions',
          '--disable-plugins',
          '--disable-default-apps'
        ]
      });
    }
    return HtmlTemplateService.browserInstance;
  }

  // ✅ PERFORMANCE OPTIMIZATION: Page pool for reuse
  private async getPage(): Promise<puppeteer.Page> {
    if (HtmlTemplateService.pagePool.length > 0) {
      const page = HtmlTemplateService.pagePool.pop()!;
      // Clear page content for reuse
      await page.goto('about:blank');
      return page;
    }

    const browser = await this.getBrowser();
    const page = await browser.newPage();

    // Pre-configure page for optimal performance
    await page.setViewport({
      width: 794,
      height: 1123,
      deviceScaleFactor: 1
    });

    return page;
  }

  private async returnPage(page: puppeteer.Page): Promise<void> {
    if (HtmlTemplateService.pagePool.length < HtmlTemplateService.MAX_PAGES) {
      HtmlTemplateService.pagePool.push(page);
    } else {
      await page.close();
    }
  }

  // ✅ PERFORMANCE OPTIMIZATION: Template caching
  private async getTemplateContent(templatePath: string): Promise<string> {
    if (this.templateCache.has(templatePath)) {
      return this.templateCache.get(templatePath)!;
    }

    const content = await fs.promises.readFile(templatePath, 'utf-8');
    this.templateCache.set(templatePath, content);
    return content;
  }

  // ✅ PERFORMANCE OPTIMIZATION: Array chunking utility
  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }

  // ✅ PERFORMANCE OPTIMIZATION: I-20 fetching logic
  private shouldFetchI20(templateData: HtmlAdmDocumentData): boolean {
    return (
      templateData.applicationType === 'Change of Status' &&
      !!templateData.studentI20Url &&
      templateData.studentI20Url.trim().length > 0
    );
  }

  async generateCalDocument(
    templateData: HtmlCalDocumentData,
    options: HtmlTemplateOptions = {}
  ): Promise<HtmlGenerationResult> {
    try {
      this.logger.log(
        `Generating CAL document for application ${templateData.applicationId}`
      );

      // Get available HTML templates
      const templates = await this.getAvailableHtmlTemplates();
      if (templates.length === 0) {
        throw new Error('No HTML templates found');
      }

      // Determine which pages to include
      const pagesToInclude = this.determinePagesToInclude(templates, options);
      this.logger.debug(
        `Including pages: ${pagesToInclude.map((p) => p.name).join(', ')}`
      );

      // ✅ PERFORMANCE OPTIMIZATION: Use shared browser instance
      const page = await this.getPage();

      try {
        // Generate PDF from HTML templates
        const pdfBuffer = await this.generatePdfFromHtmlTemplates(
          page,
          pagesToInclude,
          templateData
        );

        this.logger.log(
          `CAL document generated successfully, size: ${pdfBuffer.length} bytes`
        );

        return {
          success: true,
          documentContent: pdfBuffer,
          contentType: 'application/pdf'
        };
      } finally {
        await this.returnPage(page);
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(
        `Error generating CAL document: ${errorMessage}`,
        error instanceof Error ? error.stack : undefined
      );
      return {
        success: false,
        error: errorMessage
      };
    }
  }
  async generateAdmDocument(
    templateData: any,
    options: HtmlTemplateOptions = {}
  ): Promise<HtmlGenerationResult> {
    try {
      this.logger.log(
        `Generating ADM document for student ${templateData.studentId}`
      );

      // Get available HTML templates for ADM document
      const templates = await this.getAvailableAdmDocumentTemplates(
        templateData.applicationType
      );
      if (templates.length === 0) {
        throw new Error(
          `No HTML templates found for application type: ${templateData.applicationType}`
        );
      }

      // ✅ PERFORMANCE OPTIMIZATION: Use shared browser instance
      const page = await this.getPage();

      try {
        // Generate PDF from HTML templates
        const pdfBuffer = await this.generateAdmDocumentPdfFromHtmlTemplates(
          page,
          templates,
          templateData
        );

        this.logger.log(
          `ADM document generated successfully, size: ${pdfBuffer.length} bytes`
        );

        return {
          success: true,
          documentContent: pdfBuffer,
          contentType: 'application/pdf'
        };
      } finally {
        await this.returnPage(page);
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(
        `Error generating ADM document: ${errorMessage}`,
        error instanceof Error ? error.stack : undefined
      );
      return {
        success: false,
        error: errorMessage
      };
    }
  }

  async generateAdmissionPortfolio(
    templateData: HtmlAdmissionPortfolioData,
    options: HtmlTemplateOptions = {}
  ): Promise<HtmlGenerationResult> {
    try {
      this.logger.log(
        `Generating admission portfolio for student ${templateData.studentId}`
      );

      // Get available HTML templates for admission portfolio
      const templates = await this.getAvailableAdmissionPortfolioTemplates(
        templateData.applicationType,
        templateData.sponsorInfo
      );
      if (templates.length === 0) {
        throw new Error(
          `No HTML templates found for application type: ${templateData.applicationType}`
        );
      }

      // ✅ PERFORMANCE OPTIMIZATION: Use shared browser instance
      const page = await this.getPage();

      try {
        // Generate PDF from HTML templates
        const pdfBuffer =
          await this.generateAdmissionPortfolioPdfFromHtmlTemplates(
            page,
            templates,
            templateData
          );

        this.logger.log(
          `Admission portfolio generated successfully, size: ${pdfBuffer.length} bytes`
        );

        return {
          success: true,
          documentContent: pdfBuffer,
          contentType: 'application/pdf'
        };
      } finally {
        await this.returnPage(page);
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(
        `Error generating admission portfolio: ${errorMessage}`,
        error instanceof Error ? error.stack : undefined
      );
      return {
        success: false,
        error: errorMessage
      };
    }
  }

  private async getAvailableHtmlTemplates(): Promise<
    Array<{
      name: string;
      path: string;
      pageNumber: number;
    }>
  > {
    try {
      let templatesPath = '';

      if (process.env['NODE_ENV'] === 'development') {
        templatesPath = path.join(
          process.cwd(),
          'apps',
          'services',
          'students-service',
          'src',
          'assets',
          'html-templates',
          'CAL'
        );
      } else {
        templatesPath = path.join(process.cwd(), 'assets/html-templates/CAL');
      }

      // Check if directory exists
      const dirExists = await fs.promises
        .access(templatesPath)
        .then(() => true)
        .catch(() => false);
      if (!dirExists) {
        this.logger.error(
          `HTML templates directory not found: ${templatesPath}`
        );
        throw new Error(`HTML templates directory not found: ${templatesPath}`);
      }

      const files = await fs.promises.readdir(templatesPath);
      const templateDirs = files.filter((file) => {
        const fullPath = path.join(templatesPath, file);
        return (
          fs.statSync(fullPath).isDirectory() &&
          file.toLowerCase().includes('cal page')
        );
      });

      this.logger.debug(
        `Found HTML template directories: ${templateDirs.join(', ')}`
      );

      const templates = templateDirs.map((dir) => {
        let pageNumber = 1;
        // Extract page number from directory name
        const pageMatch = dir.match(/page\s*(\d+)/i);
        if (pageMatch) {
          pageNumber = parseInt(pageMatch[1], 10);
        }

        this.logger.debug(
          `Template ${dir} assigned page number: ${pageNumber}`
        );
        return {
          name: dir,
          path: path.join(templatesPath, dir),
          pageNumber
        };
      });

      templates.sort((a, b) => a.pageNumber - b.pageNumber);
      this.logger.debug(
        `Found ${templates.length} HTML templates: ${templates
          .map((t) => `${t.name} (page ${t.pageNumber})`)
          .join(', ')}`
      );
      return templates;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(`Error reading HTML templates: ${errorMessage}`);
      throw error;
    }
  }

  private determinePagesToInclude(
    templates: Array<{ name: string; path: string; pageNumber: number }>,
    options: HtmlTemplateOptions
  ) {
    if (options.includeAllPages) {
      return templates;
    }

    if (options.pageNumbers && options.pageNumbers.length > 0) {
      return templates.filter((template) =>
        options.pageNumbers!.includes(template.pageNumber)
      );
    }

    // Default: include all pages
    return templates;
  }

  private async generatePdfFromHtmlTemplates(
    page: puppeteer.Page,
    templates: Array<{ name: string; path: string; pageNumber: number }>,
    templateData: HtmlCalDocumentData
  ): Promise<Buffer> {
    const pdfPages: Buffer[] = [];

    for (const template of templates) {
      this.logger.debug(`Processing template: ${template.name}`);

      try {
        const htmlContent = await this.processHtmlTemplate(
          template.path,
          templateData
        );
        const pdfBuffer = await this.convertHtmlToPdf(
          page,
          htmlContent,
          template.path,
          'cal'
        );
        pdfPages.push(pdfBuffer);

        this.logger.debug(`Successfully processed template: ${template.name}`);
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Unknown error occurred';
        this.logger.error(
          `Error processing template ${template.name}: ${errorMessage}`
        );
        throw error;
      }
    }

    // Combine all PDF pages into a single document
    return this.combinePdfPages(pdfPages);
  }

  private async processHtmlTemplate(
    templatePath: string,
    templateData: HtmlCalDocumentData
  ): Promise<string> {
    try {
      const htmlFilePath = path.join(templatePath, 'index.html');
      const cssFilePath = path.join(templatePath, 'style.css');
      const varsFilePath = path.join(templatePath, 'vars.css');

      // Read HTML content
      let htmlContent = await fs.promises.readFile(htmlFilePath, 'utf-8');

      // Read CSS content
      let cssContent = '';
      if (
        await fs.promises
          .access(cssFilePath)
          .then(() => true)
          .catch(() => false)
      ) {
        cssContent = await fs.promises.readFile(cssFilePath, 'utf-8');
      }

      let varsContent = '';
      if (
        await fs.promises
          .access(varsFilePath)
          .then(() => true)
          .catch(() => false)
      ) {
        varsContent = await fs.promises.readFile(varsFilePath, 'utf-8');
      }

      // Convert relative image paths to base64 data URLs
      htmlContent = await this.convertImagePathsToBase64(
        htmlContent,
        templatePath
      );

      // Compile Handlebars template
      const template = Handlebars.compile(htmlContent);

      // Prepare data for template
      const data = {
        name: templateData.studentName,
        studentName: templateData.studentName,
        idNumber: templateData.idNumber,
        passport: templateData.passport,
        dateOfBirth: templateData.dateOfBirth,
        courseName: templateData.courseName,
        intake: templateData.intake,
        campusName: templateData.campusName,
        tuitionFee: templateData.tuitionFee,
        courseStartDate: templateData.courseStartDate,
        courseEndDate: templateData.courseEndDate,
        issueDate: templateData.issueDate,
        applicationId: templateData.applicationId,
        programName: templateData.programName,
        courseDate: templateData.courseDate,
        invoiceId: templateData.invoiceId,
        invoiceDate: templateData.invoiceDate,
        adminFee: templateData.adminFee,
        universityName: templateData.universityName,
        totalAmount: templateData.totalAmount
      };

      // Render template with data
      const renderedHtml = template(data);

      // Create complete HTML document with embedded CSS
      const completeHtml = `
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta http-equiv="X-UA-Compatible" content="IE=edge">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <style>
            ${varsContent}
            ${cssContent}
            body {
              margin: 0;
              padding: 0;
              font-family: Arial, sans-serif;
              width: 595px;
              height: 842px;
              overflow: hidden;
            }
            @media print {
              body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                width: 595px;
                height: 842px;
                margin: 0;
                padding: 0;
              }
              @page {
                size: A4;
                margin: 0;
              }
            }
            /* Ensure each page takes full A4 dimensions */
            .cover-page {
              width: 595px !important;
              height: 842px !important;
              margin: 0 !important;
              padding: 0 !important;
              overflow: hidden !important;
              position: relative !important;
              background: #ffffff !important;
            }
            /* Fix image sizing and positioning for A4 */
            img {
              max-width: 100%;
              height: auto;
              display: block;
            }
            .layer-1 {
              width: 100% !important;
              height: auto !important;
              max-width: 278px !important;
              max-height: 63px !important;
            }
            .university-name-vertical {
              width: auto !important;
              height: auto !important;
              max-width: 100% !important;
              max-height: 100% !important;
            }
            .icon {
              width: 400px !important;
              height: 400px !important;
              object-fit: contain !important;
              max-width: 400px !important;
              max-height: 400px !important;
            }
            /* Ensure proper positioning within A4 bounds */
            .iau-3 {
              width: 278px !important;
              height: 63px !important;
              max-width: 278px !important;
              max-height: 63px !important;
            }
            .university-name {
              width: 24px !important;
              height: 842px !important;
              max-width: 24px !important;
              max-height: 842px !important;
            }
          </style>
          <title>CAL Document</title>
        </head>
        <body>
          ${renderedHtml}
        </body>
        </html>
      `;

      return completeHtml;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(`Error processing HTML template: ${errorMessage}`);
      throw error;
    }
  }

  private async convertImagePathsToBase64(
    htmlContent: string,
    templatePath: string
  ): Promise<string> {
    try {
      // Find all img tags with src attributes
      const imgRegex = /<img[^>]+src=["']([^"']+)["'][^>]*>/gi;
      let match;
      let processedHtml = htmlContent;

      while ((match = imgRegex.exec(htmlContent)) !== null) {
        const fullMatch = match[0];
        const imagePath = match[1];

        // Skip if already a data URL or absolute URL
        if (
          imagePath.startsWith('data:') ||
          imagePath.startsWith('http://') ||
          imagePath.startsWith('https://')
        ) {
          continue;
        }

        try {
          // Resolve the image path relative to the template directory
          const imageFilePath = path.resolve(templatePath, imagePath);

          // Check if file exists
          if (
            await fs.promises
              .access(imageFilePath)
              .then(() => true)
              .catch(() => false)
          ) {
            // Read the image file
            const imageBuffer = await fs.promises.readFile(imageFilePath);

            // Determine MIME type based on file extension
            const ext = path.extname(imagePath).toLowerCase();
            let mimeType = 'image/png'; // default

            if (ext === '.jpg' || ext === '.jpeg') {
              mimeType = 'image/jpeg';
            } else if (ext === '.svg') {
              mimeType = 'image/svg+xml';
            } else if (ext === '.gif') {
              mimeType = 'image/gif';
            } else if (ext === '.webp') {
              mimeType = 'image/webp';
            }

            // Convert to base64
            const base64Data = imageBuffer.toString('base64');
            const dataUrl = `data:${mimeType};base64,${base64Data}`;

            // Replace the src attribute
            const newImgTag = fullMatch.replace(
              /src=["']([^"']+)["']/i,
              `src="${dataUrl}"`
            );

            processedHtml = processedHtml.replace(fullMatch, newImgTag);

            this.logger.debug(
              `Successfully converted image: ${imagePath} -> base64 data URL (${imageBuffer.length} bytes)`
            );
          } else {
            this.logger.warn(`Image file not found: ${imageFilePath}`);
          }
        } catch (error) {
          this.logger.error(
            `Error converting image ${imagePath}: ${
              error instanceof Error ? error.message : 'Unknown error'
            }`
          );
        }
      }

      return processedHtml;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(
        `Error converting image paths to base64: ${errorMessage}`
      );
      return htmlContent; // Return original content if conversion fails
    }
  }

  // ✅ PERFORMANCE OPTIMIZATION: Optimized PDF conversion
  private async convertHtmlToPdf(
    page: puppeteer.Page,
    htmlContent: string,
    templatePath: string,
    documentType?: string
  ): Promise<Buffer> {
    try {
      // Set content with minimal wait for faster processing
      await page.setContent(htmlContent, {
        waitUntil: 'domcontentloaded', // Faster than networkidle0
        timeout: 30000
      });

      // Minimal wait for rendering (reduced from 5000ms + 2000ms)
      await page.waitForTimeout(500);

      // Generate PDF with optimized settings
      const pdfBuffer = await page.pdf({
        format: 'A4',
        printBackground: true,
        margin: {
          top: '0',
          right: '0',
          bottom: '0',
          left: '0'
        },
        preferCSSPageSize: true,
        displayHeaderFooter: false,
        scale: 1,
        omitBackground: false,
        timeout: 30000 // Add timeout for PDF generation
      });

      return pdfBuffer;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(`Error converting HTML to PDF: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Convert R2 Cloudflare URL to CDN URL
   * Converts URLs like:
   * - https://0e3a85987d1b06514e83779c3527c246.r2.cloudflarestorage.com/applygoal-files/files/xxx.pdf
   * - https://0e3a85987d1b06514e83779c3527c246.r2.cloudflarestorage.com/applygoal-files/images/xxx.jpg
   * To:
   * - https://cdn.applygoal.com/files/xxx.pdf
   * - https://cdn.applygoal.com/images/xxx.jpg
   * 
   * This method handles both PDF and image URLs for AP and ADM document generation
   */
  private convertToCdnUrl(r2Url: string): string {
    if (!r2Url || r2Url.includes('cdn.applygoal.com')) {
      return r2Url;
    }
    
    // ✅ Only convert to CDN format in production environment
    if (process.env['NODE_ENV'] !== 'production') {
      this.logger.debug(`Non-production environment: keeping original URL: ${r2Url}`);
      return r2Url;
    }
    
    // Extract path after 'applygoal-files/'
    const match = r2Url.match(/applygoal-files\/(.+)$/);
    this.logger.debug(`URL conversion match for ${r2Url}:`, match);
    
    if (!match) {
      this.logger.debug(`No conversion needed for URL: ${r2Url}`);
      return r2Url;
    }
    
    const cdnUrl = `https://cdn.applygoal.com/${match[1]}`;
    this.logger.log(`🔄 URL Converted (Production): ${r2Url} → ${cdnUrl}`);
    return cdnUrl;
  }

  /**
   * Fetch PDF from public HTTPS URL
   */
  private async fetchPdfFromUrl(url: string): Promise<Buffer> {
    try {
      // Convert R2 URL to CDN URL before fetching
      const convertedUrl = this.convertToCdnUrl(url);
      this.logger.debug(`Fetching PDF from URL: ${convertedUrl}`);

      const response = await fetch(convertedUrl, {
        method: 'GET',
        headers: {
          'User-Agent': 'Mozilla/5.0 (compatible; PDFDownloader/1.0)'
        }
      });

      if (!response.ok) {
        throw new Error(
          `Failed to fetch PDF: ${response.status} ${response.statusText}`
        );
      }

      const contentType = response.headers.get('content-type');
      if (contentType && !contentType.includes('application/pdf')) {
        this.logger.warn(
          `Warning: Content-Type is ${contentType}, expected application/pdf`
        );
      }

      const arrayBuffer = await response.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);

      this.logger.debug(
        `Successfully fetched PDF from URL, size: ${buffer.length} bytes`
      );
      return buffer;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(`Error fetching PDF from URL ${url}: ${errorMessage}`);
      throw error;
    }
  }

  // ✅ Document generation for AP : Parallel template processing
  private async getAvailableAdmissionPortfolioTemplates(
    applicationType: string,
    sponsorInfo?: { sponsor?: any; studentPersonalInfo?: any }
  ): Promise<
    Array<{
      name: string;
      path: string;
      content: string;
    }>
  > {
    try {
      // Map application type to folder name (handle actual database values)
      const folderMapping: { [key: string]: string } = {
        'F-1 Initial': 'F-1 Initial',
        'F-1 Intial': 'F-1 Initial', // Handle typo in database
        Transfer: 'F-1 Transfer', // Handle "Transfer" from database
        'F-1 Transfer': 'F-1 Transfer', // Also handle full name
        'F-1 Reinstatement': 'F-1 Reinstatement',
        'New Program': 'New Program',
        'Change of Status': 'Change of status',
        'Non-F-1': 'Non F1'
      };

      const folderName = folderMapping[applicationType] || 'F-1 Initial';
      let templatesDir = '';

      if (process.env['NODE_ENV'] === 'development') {
        templatesDir = path.join(
          process.cwd(),
          'apps',
          'services',
          'students-service',
          'src',
          'assets',
          'html-templates',
          'Admission Portfolio',
          folderName
        );
      } else {
        templatesDir = path.join(
          process.cwd(),
          'assets',
          'html-templates',
          'Admission Portfolio',
          folderName
        );
      }

      if (!fs.existsSync(templatesDir)) {
        this.logger.warn(
          `Admission portfolio templates directory not found: ${templatesDir}`
        );
        return [];
      }
      console.log('Templates directory++++++++++++++++++++>:', templatesDir);

      // Look for subdirectories that contain HTML files
      // const subdirs = fs
      //   .readdirSync(templatesDir)
      //   .filter((item) => {
      //     const itemPath = path.join(templatesDir, item);
      //     return fs.statSync(itemPath).isDirectory();
      //   });
      let subdirs = fs
        .readdirSync(templatesDir)
        .filter((item) => {
          const itemPath = path.join(templatesDir, item);
          return fs.statSync(itemPath).isDirectory();
        })
        // only match folders that look like "ap-<number>"
        .filter((item) => /^ap-\d+$/.test(item))
        // sort by number after "ap-"
        .sort((a, b) => {
          const numA = parseInt(a.split('-')[1], 10);
          const numB = parseInt(b.split('-')[1], 10);
          return numA - numB;
        });

      // ✅ NEW: Check if sponsor NID matches student NID (self sponsor)
      const isSelfSponsor = this.isSelfSponsor(sponsorInfo?.sponsor, sponsorInfo?.studentPersonalInfo);
      
      if (isSelfSponsor) {
        this.logger.log('Self sponsor detected - excluding affidavit template (ap-12) from Admission Portfolio generation');
        // Filter out affidavit template (ap-12) for self sponsors
        subdirs = subdirs.filter(subdir => subdir !== 'ap-12');
      } else {
        this.logger.log('Non-self sponsor detected - including affidavit template (ap-12) in Admission Portfolio generation');
      }

      this.logger.log(
        `Found ${
          subdirs.length
        } subdirectories for admission portfolio (${applicationType}), Sub-directories: ${subdirs.join(
          ', '
        )}`
      );

      const templates = [];
      for (const subdir of subdirs) {
        const subdirPath = path.join(templatesDir, subdir);
        const htmlFiles = fs
          .readdirSync(subdirPath)
          .filter((file) => file.endsWith('.html'))
          .sort();

        for (const htmlFile of htmlFiles) {
          const templatePath = path.join(subdirPath, htmlFile);
          // ✅ PERFORMANCE OPTIMIZATION: Use template cache
          const content = await this.getTemplateContent(templatePath);

          templates.push({
            name: `${subdir}-${htmlFile.replace('.html', '')}`,
            path: templatePath,
            content: content
          });
        }
      }

      this.logger.log(
        `Found ${templates.length} HTML templates for admission portfolio (${applicationType})`
      );
      return templates;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(
        `Error getting available admission portfolio templates: ${errorMessage}`
      );
      return [];
    }
  }

  private async generateAdmissionPortfolioPdfFromHtmlTemplates(
    page: puppeteer.Page,
    templates: Array<{ name: string; path: string; content: string }>,
    templateData: HtmlAdmissionPortfolioData
  ): Promise<Buffer> {
    const pdfPages: Buffer[] = [];
    // ✅ STEP 1: Merge Personal Documents (I-20, I-94, I-797C) at the Middle
    for (const template of templates) {
      this.logger.debug(
        `Processing admission portfolio template: ${template.name}`
      );
      console.log('Template content++++++++++++++++++++>:', template.path);

      try {
        const htmlContent = await this.processAdmissionPortfolioHtmlTemplate(
          template.content,
          templateData,
          template.path
        );
        const pdfBuffer = await this.convertHtmlToPdf(
          page,
          htmlContent,
          template.path,
          'ap'
        );
        pdfPages.push(pdfBuffer);

        this.logger.debug(
          `Successfully processed admission portfolio template: ${template.name}`
        );
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Unknown error occurred';
        this.logger.error(
          `Error processing admission portfolio template ${template.name}: ${errorMessage}`
        );
        throw error;
      }
    }
    let finalPdf = await this.combinePdfPages(pdfPages);
    this.logger.log(`Template pages combined: ${finalPdf.length} bytes`);

    // ✅ STEP 2: Merge Personal Documents
    const urlPdfBuffers: Buffer[] = [];

    try {
      const requirements = templateData.requirementsDocuments;
      this.logger.debug(`Requirements structure:`, JSON.stringify(requirements, null, 2));

      if (requirements) {
        // ✅ Support both data structures for backward compatibility
        const documentCategories = [
          { docs: requirements.identityDocuments || requirements.identity, name: 'identity' },
          { docs: requirements.academicDocuments || requirements.academic, name: 'academic' },
          { docs: requirements.proficiencyDocuments || requirements.proficiency, name: 'proficiency' },
          { docs: requirements.otherDocuments || requirements.others, name: 'others' }
        ];

        this.logger.debug(`Document categories prepared:`, documentCategories.map(cat => ({
          name: cat.name,
          count: cat.docs ? cat.docs.length : 0,
          hasValidUrls: cat.docs ? cat.docs.filter((doc: { url: string; }) => doc.url && doc.url !== 'storage-disabled').length : 0
        })));

        for (const category of documentCategories) {
          if (category.docs && category.docs.length > 0) {
            this.logger.log(
              `Collecting ${category.docs.length} ${category.name} documents`
            );

            // ✅ Debug: Log all documents in this category
            category.docs.forEach((doc:any, index:any) => {
              this.logger.debug(`${category.name}[${index}]: ${doc.documentName} - URL: ${doc.url} - Status: ${doc.documentStatus}`);
            });

            for (const doc of category.docs) {
              if (doc.url && doc.url.trim() !== '' && doc.url !== 'storage-disabled') {
                try {
                  this.logger.debug(
                    `Fetching ${category.name} document: ${doc.documentName} from URL: ${doc.url}`
                  );

                  // ✅ Validate URL format
                  let isValidUrl = false;
                  try {
                    new URL(doc.url);
                    isValidUrl = true;
                  } catch {
                    this.logger.warn(`Invalid URL format for ${doc.documentName}: ${doc.url}`);
                    continue; // Skip this document
                  }

                  if (isValidUrl) {
                    let urlPdfBuffer: Buffer;
                    if (this.isImageUrl(doc.url)) {
                      this.logger.log(
                        `🖼️ Converting image to PDF: ${doc.documentName} from ${doc.url}`
                      );
                      urlPdfBuffer = await this.convertImageToPdf(
                        page,
                        doc.url,
                        doc.documentName
                      );
                    } else {
                      this.logger.log(
                        `📄 Fetching PDF document: ${doc.documentName} from ${doc.url}`
                      );
                      urlPdfBuffer = await this.fetchPdfFromUrl(doc.url);
                    }
                    urlPdfBuffers.push(urlPdfBuffer);
                    this.logger.debug(
                      `Successfully collected ${category.name} document: ${doc.documentName}`
                    );
                  }
                } catch (error: any) {
                  this.logger.error(
                    `Failed to fetch ${category.name} document ${doc.documentName}: ${error.message}`
                  );
                }
              } else {
                this.logger.debug(
                  `Skipping ${category.name} document ${doc.documentName}: URL is empty, null, or storage-disabled`
                );
              }
            }
          }
        }
      }
      if (urlPdfBuffers.length > 0) {
        this.logger.log(
          `Merging ${urlPdfBuffers.length} URL PDFs with template PDF`
        );

        for (let i = 0; i < urlPdfBuffers.length; i++) {
          try {
            finalPdf = await this.mergePdfBuffers(finalPdf, urlPdfBuffers[i]);
            this.logger.debug(
              `Successfully merged URL PDF ${i + 1}/${urlPdfBuffers.length}`
            );
          } catch (error: any) {
            this.logger.error(
              `Failed to merge URL PDF ${i + 1}: ${error.message}`
            );
            // Continue with other PDFs
          }
        }

        this.logger.log(`Successfully merged all URL PDFs with template PDF`);
      } else {
        this.logger.log(`No URL PDFs to merge`);
      }
    } catch (error: any) {
      this.logger.warn(`Failed to merge uploaded documents: ${error.message}`);
    }

    // ✅ STEP 3: Merge Visa Documents (I-20, I-94, I-797C) at the end
    try {
      const visaDocuments = await this.fetchVisaDocuments(templateData);
      if (visaDocuments.length > 0) {
        this.logger.log(
          `Merging ${visaDocuments.length} visa documents at the end`
        );

        for (let i = 0; i < visaDocuments.length; i++) {
          try {
            finalPdf = await this.mergePdfBuffers(finalPdf, visaDocuments[i]);
            this.logger.debug(
              `Successfully merged visa document ${i + 1}/${
                visaDocuments.length
              }`
            );
          } catch (error: any) {
            this.logger.error(
              `Failed to merge visa document ${i + 1}: ${error.message}`
            );
          }
        }

        this.logger.log(`Successfully merged all visa documents`);
      }
    } catch (error: any) {
      this.logger.warn(`Failed to merge visa documents: ${error.message}`);
    }

    return finalPdf;
  }

  private async processAdmissionPortfolioHtmlTemplate(
    templateContent: string,
    templateData: HtmlAdmissionPortfolioData,
    templatePath?: string
  ): Promise<string> {
    try {
      // Compile the Handlebars template
      const template = Handlebars.compile(templateContent);

      // Register custom helpers for admission portfolio
      Handlebars.registerHelper('formatDate', function (date: string) {
        if (!date || date === 'N/A') return 'N/A';
        try {
          const d = new Date(date);
          return d.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          });
        } catch {
          return date;
        }
      });

      Handlebars.registerHelper(
        'joinArray',
        function (array: string[], separator = ',') {
          if (!array || !Array.isArray(array)) return 'N/A';
          return array.join(separator);
        }
      );

      // Generate the HTML content with the template data
      const processedContent = template(templateData);

      this.logger.debug(
        `Admission portfolio HTML template processed successfully`
      );
      return processedContent;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(
        `Error processing admission portfolio HTML template: ${errorMessage}`
      );
      throw error;
    }
  }

  private async getAvailableAdmDocumentTemplates(
    applicationType: string
  ): Promise<
    Array<{
      name: string;
      path: string;
      content: string;
    }>
  > {
    try {
      // Map application type to folder name (handle actual database values)
      const folderMapping: { [key: string]: string } = {
        'F-1 Intial': 'F-1 Initial', // Your DB value → Folder name
        Reinstatement: 'F-1 Reinstatement', // Your DB value → Folder name
        'New Program': 'New Program', // Your DB value → Folder name
        'Change of Status': 'Change of status', // Your DB value → Folder name
        'Non-F-1': 'Non F1', // Your DB value → Folder name
        Transfer: 'F-1 Transfer', // Your DB value → Folder name

        // Keep backward compatibility with any existing variations
        'F-1 Initial': 'F-1 Initial',
        'F-1 Reinstatement': 'F-1 Reinstatement',
        'F-1 Transfer': 'F-1 Transfer'
      };

      const folderName = folderMapping[applicationType] || 'F-1 Initial';
      this.logger.log(
        `ADM document: Application type "${applicationType}" mapped to folder "${folderName}"`
      );

      let templatesDir = '';

      if (process.env['NODE_ENV'] === 'development') {
        // Development environment - use full path
        templatesDir = path.join(
          process.cwd(),
          'apps',
          'services',
          'students-service',
          'src',
          'assets',
          'html-templates',
          'Admission Package',
          folderName
        );
      } else {
        // Production environment - use relative path
        templatesDir = path.join(
          process.cwd(),
          'assets',
          'html-templates',
          'Admission Package',
          folderName
        );
      }

      this.logger.log(
        `ADM document: Looking for templates in directory: ${templatesDir}`
      );

      if (!fs.existsSync(templatesDir)) {
        this.logger.warn(
          `ADM document templates directory not found: ${templatesDir}`
        );
        return [];
      }

      // Recursively search for HTML files in all subdirectories
      const findHtmlFilesRecursively = async (
        dirPath: string,
        depth = 0
      ): Promise<
        Array<{ path: string; content: string; relativePath: string }>
      > => {
        const results: Array<{
          path: string;
          content: string;
          relativePath: string;
        }> = [];

        try {
          const items = fs.readdirSync(dirPath);
          this.logger.log('Total directory list', items.length);

          for (const item of items) {
            const itemPath = path.join(dirPath, item);
            const stat = fs.statSync(itemPath);

            if (stat.isDirectory()) {
              // Recursively search subdirectories
              const subResults = await findHtmlFilesRecursively(
                itemPath,
                depth + 1
              );
              results.push(...subResults);
            } else if (item.endsWith('.html')) {
              // Found an HTML file
              // ✅ PERFORMANCE OPTIMIZATION: Use template cache
              const content = await this.getTemplateContent(itemPath);
              const relativePath = path.relative(templatesDir, itemPath);
              results.push({
                path: itemPath,
                content: content,
                relativePath: relativePath
              });
            }
          }
        } catch (error) {
          this.logger.warn(`Error reading directory ${dirPath}: ${error}`);
        }

        return results;
      };

      this.logger.log(
        `Recursively searching for HTML templates in: ${templatesDir}`
      );
      const htmlFiles = await findHtmlFilesRecursively(templatesDir);

      this.logger.log(
        `Found ${htmlFiles.length} HTML files recursively in ADM document templates`
      );

      const templates = [];
      for (const htmlFile of htmlFiles) {
        templates.push({
          // name: htmlFile.relativePath
          //   .replace(/[\\/]/g, '-')
          //   .replace('.html', ''),
          name: path
            .dirname(htmlFile.relativePath) // "F-1 Initial-5"
            .replace(/[\\/]/g, '-'),
          path: htmlFile.path,
          content: htmlFile.content
        });
      }

      this.logger.log(
        `Found ${templates.length} HTML templates for ADM document (${applicationType})`
      );
      return templates;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(
        `Error getting available ADM document templates: ${errorMessage}`
      );
      return [];
    }
  }

  // ✅ PERFORMANCE OPTIMIZATION  for ADM : Parallel template processing
  private async generateAdmDocumentPdfFromHtmlTemplates(
    page: puppeteer.Page,
    templates: Array<{ name: string; path: string; content: string }>,
    templateData: HtmlAdmDocumentData
  ): Promise<Buffer> {
    // Start I-20 fetching in parallel with template processing
    const i20Promise = this.shouldFetchI20(templateData)
      ? this.fetchPdfFromUrl(templateData.studentI20Url!)
      : Promise.resolve(null);

    // Process templates in parallel (limit concurrency)
    const concurrency = Math.min(3, templates.length);
    const chunks = this.chunkArray(templates, concurrency);
    const pdfPages: Buffer[] = [];

    for (const chunk of chunks) {
      const promises = chunk.map(async (template) => {
        const page = await this.getPage();
        try {
          this.logger.debug(
            `Processing ADM document template: ${template.name}`
          );

          const htmlContent = await this.processAdmDocumentHtmlTemplate(
            template.content,
            template.path,
            templateData
          );
          const pdfBuffer = await this.convertHtmlToPdf(
            page,
            htmlContent,
            template.path,
            'adm'
          );

          this.logger.debug(
            `Successfully processed ADM document template: ${template.name}`
          );
          return pdfBuffer;
        } catch (error) {
          const errorMessage =
            error instanceof Error ? error.message : 'Unknown error occurred';
          this.logger.error(
            `Error processing ADM document template ${template.name}: ${errorMessage}`
          );
          throw error;
        } finally {
          await this.returnPage(page);
        }
      });

      const results = await Promise.all(promises);
      pdfPages.push(...results);
    }

    // Step 2: Combine all ADM template pages (first page only from each template)
    let combinedAdmPdf = await this.combinePdfPages(pdfPages);
    this.logger.log(`ADM templates combined: ${combinedAdmPdf.length} bytes`);

    // ✅ PERFORMANCE OPTIMIZATION: Merge I-20 PDF if available (async)
    try {
      const i20Pdf = await i20Promise;
      if (i20Pdf) {
        combinedAdmPdf = await this.mergePdfBuffers(combinedAdmPdf, i20Pdf);
        this.logger.log(
          'Successfully merged I-20 PDF with ALL pages as last pages for Change of Status application'
        );
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(
        `Failed to fetch I-20 PDF, continuing without it: ${errorMessage}`
      );
      // Continue without I-20 PDF rather than failing the entire generation
    }

    // Combine all PDF pages into a single document
    // return await this.combinePdfPages(pdfPages);
    return combinedAdmPdf;
  }

  private async processAdmDocumentHtmlTemplate(
    templateContent: string,
    templatePath: string,
    templateData: HtmlAdmDocumentData
  ): Promise<string> {
    try {
      const templateDir = path.dirname(templatePath);
      // const cssFilePath = path.join(templateDir, 'style.css');
      // const varsFilePath = path.join(templateDir, 'vars.css');

      // // Read CSS content
      // let cssContent = '';
      // if (
      //   await fs.promises
      //     .access(cssFilePath)
      //     .then(() => true)
      //     .catch(() => false)
      // ) {
      //   cssContent = await fs.promises.readFile(cssFilePath, 'utf-8');
      //   this.logger.debug(`Loaded CSS file: ${cssFilePath}`);
      // } else {
      //   this.logger.warn(`CSS file not found: ${cssFilePath}`);
      // }

      // let varsContent = '';
      // if (
      //   await fs.promises
      //     .access(varsFilePath)
      //     .then(() => true)
      //     .catch(() => false)
      // ) {
      //   varsContent = await fs.promises.readFile(varsFilePath, 'utf-8');
      //   this.logger.debug(`Loaded vars file: ${varsFilePath}`);
      // } else {
      //   this.logger.warn(`Vars file not found: ${varsFilePath}`);
      // }

      // Convert relative image paths to base64 data URLs
      const htmlContent = await this.convertImagePathsToBase64(
        templateContent,
        templateDir
      );

      // Compile the Handlebars template
      const template = Handlebars.compile(htmlContent);

      // Register custom helpers for ADM document
      Handlebars.registerHelper('formatDate', function (date: string) {
        if (!date || date === 'N/A') return 'N/A';
        try {
          const d = new Date(date);
          return d.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          });
        } catch {
          return date;
        }
      });

      Handlebars.registerHelper(
        'joinArray',
        function (array: string[], separator = ', ') {
          if (!array || !Array.isArray(array)) return 'N/A';
          return array.join(separator);
        }
      );

      // Generate the HTML content with the template data
      const renderedHtml = template(templateData);

      // Create complete HTML document with embedded CSS for A4 styling
      // const completeHtml = `
      //   <!DOCTYPE html>
      //   <html lang="en">
      //   <head>
      //     <meta charset="UTF-8">
      //     <meta http-equiv="X-UA-Compatible" content="IE=edge">
      //     <meta name="viewport" content="width=device-width, initial-scale=1.0">
      //     <title>ADM Document</title>
      //   </head>
      //   <body>
      //     ${renderedHtml}
      //   </body>
      //   </html>
      // `;

      this.logger.debug(
        `ADM document HTML template processed successfully with A4 styling and CSS files`
      );
      return renderedHtml;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(
        `Error processing ADM document HTML template: ${errorMessage}`
      );
      throw error;
    }
  }
  // ✅ Helper functions
  private async combinePdfPages(pdfPages: Buffer[]): Promise<Buffer> {
    try {
      if (pdfPages.length === 0) {
        throw new Error('No PDF pages to combine');
      }

      if (pdfPages.length === 1) {
        return pdfPages[0];
      }

      this.logger.log(
        `Combining ${pdfPages.length} PDF pages into a single document`
      );

      // Create a new PDF document
      const mergedPdf = await PDFDocument.create();

      // Add each page to the merged document
      for (let i = 0; i < pdfPages.length; i++) {
        try {
          const pdfDoc = await PDFDocument.load(new Uint8Array(pdfPages[i]));
          const pageCount = pdfDoc.getPageCount();
          this.logger.debug(`Page ${i + 1} has ${pageCount} pages`);

          if (pageCount > 1) {
            this.logger.warn(
              `Template ${i + 1} generated ${pageCount} pages instead of 1`
            );
            // ADD THIS: Check page dimensions
            const pages = pdfDoc.getPages();
            pages.forEach((page, pageIndex) => {
              const { width, height } = page.getSize();
              this.logger.debug(
                `  Sub-page ${pageIndex + 1} dimensions: ${width}x${height}`
              );
            });
          }

          // If a template produced more than one page (e.g., an extra blank page),
          // only take the first page to keep CAL at exactly one page per template.
          const indicesToCopy = pageCount > 1 ? [0] : pdfDoc.getPageIndices();
          // const indicesToCopy = pdfDoc.getPageIndices();
          const pages = await mergedPdf.copyPages(pdfDoc, indicesToCopy);
          pages.forEach((page) => mergedPdf.addPage(page));
          this.logger.debug(
            `Successfully added ${indicesToCopy.length} page(s) from template ${
              i + 1
            }`
          );
        } catch (error) {
          this.logger.error(
            `Error processing page ${i + 1}: ${
              error instanceof Error ? error.message : 'Unknown error'
            }`
          );
          throw error;
        }
      }

      // Save the merged PDF
      const mergedPdfBytes = await mergedPdf.save();
      const mergedPdfBuffer = Buffer.from(mergedPdfBytes);

      this.logger.log(
        `Successfully created merged PDF with ${mergedPdf.getPageCount()} pages, size: ${
          mergedPdfBuffer.length
        } bytes`
      );

      return mergedPdfBuffer;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(`Error combining PDF pages: ${errorMessage}`);
      throw error;
    }
  }
  private async mergePdfBuffers(
    primaryPdf: Buffer,
    secondaryPdf: Buffer
  ): Promise<Buffer> {
    try {
      this.logger.debug(
        `Merging PDFs: primary=${primaryPdf.length} bytes, secondary=${secondaryPdf.length} bytes`
      );

      // Create a new PDF document
      const mergedPdf = await PDFDocument.create();

      // Load the primary PDF and copy ALL its pages
      const primaryPdfDoc = await PDFDocument.load(new Uint8Array(primaryPdf));
      const primaryPageCount = primaryPdfDoc.getPageCount();
      const primaryPages = await mergedPdf.copyPages(
        primaryPdfDoc,
        primaryPdfDoc.getPageIndices()
      );
      primaryPages.forEach((page) => mergedPdf.addPage(page));

      this.logger.debug(`Added ALL ${primaryPageCount} pages from primary PDF`);

      // Load the secondary PDF and copy ALL its pages
      const secondaryPdfDoc = await PDFDocument.load(
        new Uint8Array(secondaryPdf)
      );
      const secondaryPageCount = secondaryPdfDoc.getPageCount();
      const secondaryPages = await mergedPdf.copyPages(
        secondaryPdfDoc,
        secondaryPdfDoc.getPageIndices()
      );
      secondaryPages.forEach((page) => mergedPdf.addPage(page));

      this.logger.debug(
        `Added ALL ${secondaryPageCount} pages from secondary PDF (I-20)`
      );

      // Save the merged PDF
      const mergedPdfBytes = await mergedPdf.save();
      const mergedPdfBuffer = Buffer.from(mergedPdfBytes);

      this.logger.log(
        `Successfully merged PDFs: ${
          primaryPageCount + secondaryPageCount
        } total pages, size: ${mergedPdfBuffer.length} bytes`
      );

      return mergedPdfBuffer;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(`Error merging PDF buffers: ${errorMessage}`);
      throw error;
    }
  }
  private async fetchVisaDocuments(
    templateData: HtmlAdmissionPortfolioData
  ): Promise<Buffer[]> {
    const visaDocumentBuffers: Buffer[] = [];

    try {
      // Get visa document URLs from the original comprehensive data
      // const comprehensiveData = templateData.comprehensiveData; // You'll need to pass this

      // ✅ IMPORTANT: Only use filtered visa documents, NEVER studentI20Url
      const visaDocuments = [
        { url: templateData.i20Url, name: 'I-20' }, // Use i20Url ONLY
        { url: templateData.i94Url, name: 'I-94' },
        { url: templateData.i797cUrl, name: 'I-797C' }
      ];

      // ✅ Debug log to ensure no studentI20Url is being processed
      this.logger.debug(`🔍 DEBUG - Visa documents to be fetched:`, {
        i20Url: templateData.i20Url ? 'Will fetch' : 'Not required',
        studentI20Url: (templateData as any).studentI20Url ? 'EXCLUDED (should not be present)' : 'Not present (good)',
        i94Url: templateData.i94Url ? 'Will fetch' : 'Not required',
        i797cUrl: templateData.i797cUrl ? 'Will fetch' : 'Not required'
      });

      for (const doc of visaDocuments) {
        if (doc.url && doc.url.trim() !== '') {
          try {
            this.logger.debug(
              `Fetching visa document: ${doc.name} from ${doc.url}`
            );
            const pdfBuffer = await this.fetchPdfFromUrl(doc.url);
            visaDocumentBuffers.push(pdfBuffer);
            this.logger.debug(
              `Successfully fetched visa document: ${doc.name}`
            );
          } catch (error: any) {
            this.logger.error(
              `Failed to fetch document ${doc.name}: ${error.message}`
            );
          }
        }
      }

      this.logger.log(
        `Successfully fetched ${visaDocumentBuffers.length} visa documents`
      );
      return visaDocumentBuffers;
    } catch (error: any) {
      this.logger.error(`Error fetching visa documents: ${error.message}`);
      throw error;
    }
  }
  private isImageUrl(url: string): boolean {
    if (!url) return false;
    const imageExtensions = [
      '.png',
      '.jpg',
      '.jpeg',
      '.gif',
      '.bmp',
      '.webp',
      '.svg'
    ];
    const lowerUrl = url.toLowerCase();
    return (
      imageExtensions.some((ext) => lowerUrl.includes(ext)) ||
      lowerUrl.includes('image/')
    );
  }
  private async convertImageToPdf(
    page: puppeteer.Page,
    imageUrl: string,
    documentName: string
  ): Promise<Buffer> {
    try {
      // Convert R2 URL to CDN URL before using in HTML
      const convertedImageUrl = this.convertToCdnUrl(imageUrl);
      this.logger.debug(`Converting image to PDF: ${documentName} from ${convertedImageUrl}`);
      
      const htmlContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="UTF-8">
          <style>
            body { margin: 0; padding: 0; display: flex; justify-content: center; align-items: center; min-height: 100vh; background: white; }
            .image-container { max-width: 100%; max-height: 100vh; display: flex; justify-content: center; align-items: center; }
            img { max-width: 100%; max-height: 100vh; object-fit: contain; display: block; }
          </style>
        </head>
        <body>
          <div class="image-container">
            <img src="${convertedImageUrl}" alt="${documentName}" />
          </div>
        </body>
      </html>
    `;

      await page.setContent(htmlContent, { waitUntil: 'networkidle0' });
      await page.waitForFunction(
        () => {
          const img = document.querySelector('img');
          return img && img.complete && img.naturalHeight !== 0;
        },
        { timeout: 10000 }
      );

      return await page.pdf({
        format: 'A4',
        printBackground: true,
        margin: { top: '0.5in', bottom: '0.5in', left: '0.5in', right: '0.5in' }
      });
    } catch (error: any) {
      this.logger.error(
        `Error converting image to PDF (${documentName}): ${error.message}`
      );
      throw error;
    }
  }

  // ✅ Helper method to check if sponsor is self sponsor
  private isSelfSponsor(sponsor: any, studentPersonalInfo: any): boolean {
    if (!sponsor) {
      this.logger.log('No sponsor information found - treating as self sponsor');
      return true;
    }

    // ✅ Check if sponsor NID matches student NID
    const sponsorNid = (sponsor.nid || '').toString().trim();
    const studentNid = (studentPersonalInfo?.nid || '').toString().trim();
    
    const isSelfSponsor = sponsorNid && studentNid && sponsorNid === studentNid;
    
    this.logger.log(`Sponsor NID comparison:`, {
      sponsorNid,
      studentNid,
      isSelfSponsor
    });

    return isSelfSponsor;
  }
}
