import { Injectable, Logger } from '@nestjs/common';
import { PDFDocument, PDFPage, rgb, StandardFonts } from 'pdf-lib';
import * as fs from 'fs';
import * as path from 'path';

export interface CalDocumentData {
  applicationId: number;
  name?: string;
  studentName: string;
  idNumber: string;
  passport: string;
  dateOfBirth: string;
  courseName: string;
  intake: string;
  campusName: string;
  tuitionFee: string;
  courseStartDate: string;
  courseEndDate: string;
  issueDate: string;
  invoiceId: string;
  invoiceDate: string;
  adminFee: string;
  programName: string;
  courseDate: string;
  universityName: string;
  totalAmount: string;
  
}

export interface AdmissionPackageData {
  studentId: string;
  studentName: string;
  applicationType: string;
  email: string;
  phone: string;
  dateOfBirth: string;
  passport: string;
  nid: string;
  presentAddress: string;
  permanentAddress: string;
  preferredSubject: string[];
  preferredCountry: string[];
  issueDate: string;
  fatherName: string;
  motherName: string;
  gender: string;
  maritalStatus: string;
  sponsor: string;
  emergencyContact: string;
}

export interface AdmDocumentData {
  studentId: string;
  studentName: string;
  applicationType: string;
  email: string;
  phone: string;
  dateOfBirth: string;
  passport: string;
  nid: string;
  presentAddress: string;
  permanentAddress: string;
  preferredSubject: string[];
  preferredCountry: string[];
  issueDate: string;
  fatherName: string;
  motherName: string;
  gender: string;
  maritalStatus: string;
  sponsor: string;
  emergencyContact: string;
  admNumber: string;
  admDate: string;
  universityName: string;
  courseName: string;
  intakeName: string;
  campusName: string;
  tuitionFee: string;
  courseStartDate: string;
  courseEndDate: string;
}

export interface AdmissionPortfolioData {
  studentId: string;
  studentName: string;
  applicationType: string;
  email: string;
  phone: string;
  dateOfBirth: string;
  passport: string;
  nid: string;
  presentAddress: string;
  permanentAddress: string;
  preferredSubject: string[];
  preferredCountry: string[];
  issueDate: string;
  fatherName: string;
  motherName: string;
  gender: string;
  maritalStatus: string;
  sponsor: string;
  emergencyContact: string;
  portfolioNumber: string;
  portfolioDate: string;
  universityName: string;
  courseName: string;
  intakeName: string;
  campusName: string;
  tuitionFee: string;
  courseStartDate: string;
  courseEndDate: string;
}

export interface PdfTemplateOptions {
  includeAllPages?: boolean;
  pageNumbers?: number[];
}

export interface PdfGenerationResult {
  success: boolean;
  documentContent?: Buffer;
  contentType?: string;
  error?: string;
}

@Injectable()
export class PdfTemplateService {
  private readonly logger = new Logger(PdfTemplateService.name);

  /**
   * Generate CAL document using PDF templates
   */
  async generateCalDocument(
    templateData: CalDocumentData,
    options: PdfTemplateOptions = {}
  ): Promise<PdfGenerationResult> {
    try {
      this.logger.log(`Generating CAL document for application ${templateData.applicationId}`);

      // Get available PDF templates
      const templates = await this.getAvailablePdfTemplates();
      if (templates.length === 0) {
        throw new Error('No PDF templates found');
      }

      // Determine which pages to include
      const pagesToInclude = this.determinePagesToInclude(templates, options);
      this.logger.debug(`Including pages: ${pagesToInclude.join(', ')}`);

      // Create the combined PDF document
      const pdfDoc = await PDFDocument.create();

      // Process each template page
      for (const templateInfo of pagesToInclude) {
        await this.processTemplatePage(pdfDoc, templateInfo, templateData);
      }

      // Generate the final PDF buffer
      const pdfBytes = await pdfDoc.save();

      this.logger.log(`CAL document generated successfully, size: ${pdfBytes.length} bytes`);

      return {
        success: true,
        documentContent: Buffer.from(pdfBytes),
        contentType: 'application/pdf'
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(`Error generating CAL document: ${errorMessage}`, errorStack);
      return {
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * Generate admission package using PDF templates
   */
  // async generateAdmissionPackage(
  //   templateData: AdmissionPackageData,
  //   options: PdfTemplateOptions = {}
  // ): Promise<PdfGenerationResult> {
  //   try {
  //     this.logger.log(`Generating admission package for student ${templateData.studentId}`);

  //     // Get available PDF templates for admission package
  //     const templates = await this.getAvailableAdmissionPackagePdfTemplates(templateData.applicationType);
  //     if (templates.length === 0) {
  //       throw new Error(`No PDF templates found for application type: ${templateData.applicationType}`);
  //     }

  //     // Create the combined PDF document
  //     const pdfDoc = await PDFDocument.create();

  //     // Process each template page
  //     for (const templateInfo of templates) {
  //       await this.processAdmissionPackageTemplatePage(pdfDoc, templateInfo, templateData);
  //     }

  //     // Generate the final PDF buffer
  //     const pdfBytes = await pdfDoc.save();

  //     this.logger.log(`Admission package generated successfully, size: ${pdfBytes.length} bytes`);

  //     return {
  //       success: true,
  //       documentContent: Buffer.from(pdfBytes),
  //       contentType: 'application/pdf'
  //     };

  //   } catch (error) {
  //     const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
  //     const errorStack = error instanceof Error ? error.stack : undefined;
  //     this.logger.error(`Error generating admission package: ${errorMessage}`, errorStack);
  //     return {
  //       success: false,
  //       error: errorMessage
  //     };
  //   }
  // }

  /**
   * Generate ADM document using PDF templates
   */
  async generateAdmDocument(
    templateData: AdmDocumentData,
    options: PdfTemplateOptions = {}
  ): Promise<PdfGenerationResult> {
    try {
      this.logger.log(`Generating ADM document for student ${templateData.studentId}`);

      // Get available PDF templates for ADM document
      const templates = await this.getAvailableAdmDocumentPdfTemplates(templateData.applicationType);
      if (templates.length === 0) {
        throw new Error(`No PDF templates found for application type: ${templateData.applicationType}`);
      }

      // Create the combined PDF document
      const pdfDoc = await PDFDocument.create();

      // Process each template page
      for (const templateInfo of templates) {
        await this.processAdmDocumentTemplatePage(pdfDoc, templateInfo, templateData);
      }

      // Generate the final PDF buffer
      const pdfBytes = await pdfDoc.save();

      this.logger.log(`ADM document generated successfully, size: ${pdfBytes.length} bytes`);

      return {
        success: true,
        documentContent: Buffer.from(pdfBytes),
        contentType: 'application/pdf'
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(`Error generating ADM document: ${errorMessage}`, errorStack);
      return {
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * Generate admission portfolio using PDF templates
   */
  async generateAdmissionPortfolio(
    templateData: AdmissionPortfolioData,
    options: PdfTemplateOptions = {}
  ): Promise<PdfGenerationResult> {
    try {
      this.logger.log(`Generating admission portfolio for student ${templateData.studentId}`);

      // Get available PDF templates for admission portfolio
      const templates = await this.getAvailableAdmissionPortfolioPdfTemplates(templateData.applicationType);
      if (templates.length === 0) {
        throw new Error(`No PDF templates found for application type: ${templateData.applicationType}`);
      }

      // Create the combined PDF document
      const pdfDoc = await PDFDocument.create();

      // Process each template page
      for (const templateInfo of templates) {
        await this.processAdmissionPortfolioTemplatePage(pdfDoc, templateInfo, templateData);
      }

      // Generate the final PDF buffer
      const pdfBytes = await pdfDoc.save();

      this.logger.log(`Admission portfolio generated successfully, size: ${pdfBytes.length} bytes`);

      return {
        success: true,
        documentContent: Buffer.from(pdfBytes),
        contentType: 'application/pdf'
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(`Error generating admission portfolio: ${errorMessage}`, errorStack);
      return {
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * Get available PDF templates from the assets directory
   */
  private async getAvailablePdfTemplates(): Promise<Array<{
    name: string;
    path: string;
    pageNumber: number;
  }>> {
    try {
      const templatesPath = path.join(
        process.cwd(),
        'apps/services/students-service/src/assets/pdf-templates/CAL'
      );
      
      // Check if directory exists
      const dirExists = await fs.promises.access(templatesPath).then(() => true).catch(() => false);
      if (!dirExists) {
        this.logger.error(`PDF templates directory not found: ${templatesPath}`);
        throw new Error(`PDF templates directory not found: ${templatesPath}`);
      }

      const files = await fs.promises.readdir(templatesPath);
      const pdfFiles = files.filter(file => file.toLowerCase().endsWith('.pdf'));
      this.logger.debug(`Found PDF files: ${pdfFiles.join(', ')}`);

      const templates = pdfFiles.map((file) => {
        let pageNumber = 1;
        // Extract page number from filename with improved pattern matching
        const pageMatch = file.match(/page\s*(\d+)/i) ||
                         file.match(/(\d+)/) ||
                         file.match(/cover\s*page/i) ||
                         file.match(/page\s*01/i);

        if (pageMatch) {
          if (pageMatch[1]) {
            pageNumber = parseInt(pageMatch[1], 10);
          } else if (file.toLowerCase().includes('cover') || file.toLowerCase().includes('page 01')) {
            pageNumber = 1;
          }
        }
        // Handle specific naming patterns
        if (file.toLowerCase().includes('cover page') || file.toLowerCase().includes('cal page 01')) {
          pageNumber = 1;
        } else if (file.toLowerCase().includes('cal page 02')) {
          pageNumber = 2;
        } else if (file.toLowerCase().includes('cal page 03')) {
          pageNumber = 3;
        } else if (file.toLowerCase().includes('cal page 04')) {
          pageNumber = 4;
        }
        this.logger.debug(`Template ${file} assigned page number: ${pageNumber}`);
        return {
          name: file,
          path: path.join(templatesPath, file),
          pageNumber
        };
      });
      templates.sort((a, b) => a.pageNumber - b.pageNumber);
      this.logger.debug(`Found ${templates.length} PDF templates: ${templates.map(t => `${t.name} (page ${t.pageNumber})`).join(', ')}`);
      return templates;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(`Error reading PDF templates: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Determine which pages to include based on options
   */
  private determinePagesToInclude(
    templates: Array<{ name: string; path: string; pageNumber: number }>,
    options: PdfTemplateOptions
  ) {
    if (options.pageNumbers && options.pageNumbers.length > 0) {
      return templates.filter(t => options.pageNumbers!.includes(t.pageNumber));
    }

    if (options.includeAllPages) {
      return templates;
    }

    // Default: include all templates
    return templates;
  }

  /**
   * Process a single template page and add it to the document
   */
  private async processTemplatePage(
    pdfDoc: PDFDocument,
    templateInfo: { name: string; path: string; pageNumber: number },
    templateData: CalDocumentData
  ): Promise<void> {
    try {
      this.logger.debug(`Processing template: ${templateInfo.name}`);

      // Analyze template fields for debugging
      await this.analyzeTemplateFields(templateInfo.path);

      // Read the template PDF
      const templateBytes = await fs.promises.readFile(templateInfo.path);
      const templateDoc = await PDFDocument.load(templateBytes as any);

      // Copy pages from template to main document
      const pages = await pdfDoc.copyPages(templateDoc, templateDoc.getPageIndices());
      
      for (const page of pages) {
        pdfDoc.addPage(page);
        
        // Fill in the template fields based on page type
        await this.fillTemplateFields(page, templateInfo, templateData);
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(`Error processing template ${templateInfo.name}: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Fill template fields with actual data
   */
  private async fillTemplateFields(
    page: PDFPage,
    templateInfo: { name: string; path: string; pageNumber: number },
    templateData: CalDocumentData
  ): Promise<void> {
    try {
      // Get page dimensions
      const { width, height } = page.getSize();
      this.logger.debug(`Page size: ${width} x ${height}`);

      // Check if the page has form fields
      const form = page.doc.getForm();
      const fields = form.getFields();
      
      this.logger.debug(`Template ${templateInfo.name} has ${fields.length} form fields:`);
      
      if (fields.length > 0) {
        // Log all available form fields for debugging
        for (const field of fields) {
          const fieldName = field.getName();
          const fieldType = field.constructor.name;
          this.logger.debug(`  - Field: ${fieldName} (Type: ${fieldType})`);
        }
        
        // Fill form fields with dynamic data
        await this.fillFormFields(form, templateData);
      } else {
        this.logger.debug(`No form fields found in template: ${templateInfo.name} - using static template`);
        
        // Only fill the issue date on cover page if needed (minimal manual filling)
        if (templateInfo.name.toLowerCase().includes('cover') || templateInfo.pageNumber === 1) {
          const font = await page.doc.embedFont(StandardFonts.Helvetica);
          page.drawText(templateData.issueDate, {
            x: 50,
            y: 50,
            size: 10,
            font: font,
            color: rgb(0, 0, 0),
          });
        }
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(`Error filling template fields: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Fill PDF form fields with dynamic data
   */
  private async fillFormFields(form: any, templateData: CalDocumentData): Promise<void> {
    try {
      const fields = form.getFields();
      let filledFieldsCount = 0;
      let skippedFieldsCount = 0;
      
      this.logger.debug(`Starting to fill ${fields.length} form fields with data`);
      
      for (const field of fields) {
        const fieldName = field.getName();
        const fieldType = field.constructor.name;
        
        this.logger.debug(`Processing form field: ${fieldName} (Type: ${fieldType})`);
        
        // Map field names to template data
        const fieldValue = this.getFieldValue(fieldName, templateData);
        
        if (fieldValue !== null) {
          try {
            // Fill the form field with the value based on field type
            if (fieldType === 'PDFTextField') {
              field.setText(fieldValue);
              this.logger.debug(`✅ Filled text field '${fieldName}' with value: ${fieldValue}`);
              filledFieldsCount++;
            } else if (fieldType === 'PDFCheckBox') {
              // Handle checkbox fields - check if value is truthy
              if (fieldValue.toLowerCase() === 'true' || fieldValue.toLowerCase() === 'yes' || fieldValue === '1') {
                field.check();
                this.logger.debug(`✅ Checked checkbox field '${fieldName}'`);
              } else {
                field.uncheck();
                this.logger.debug(`✅ Unchecked checkbox field '${fieldName}'`);
              }
              filledFieldsCount++;
            } else if (fieldType === 'PDFDropdown') {
              // Handle dropdown fields
              field.select(fieldValue);
              this.logger.debug(`✅ Selected dropdown field '${fieldName}' with value: ${fieldValue}`);
              filledFieldsCount++;
            } else if (fieldType === 'PDFRadioGroup') {
              // Handle radio button groups
              field.select(fieldValue);
              this.logger.debug(`✅ Selected radio field '${fieldName}' with value: ${fieldValue}`);
              filledFieldsCount++;
            } else {
              this.logger.warn(`⚠️ Unknown field type '${fieldType}' for field '${fieldName}' - skipping`);
              skippedFieldsCount++;
            }
          } catch (fieldError) {
            const fieldErrorMessage = fieldError instanceof Error ? fieldError.message : 'Unknown field error';
            this.logger.error(`❌ Error filling field '${fieldName}': ${fieldErrorMessage}`);
            skippedFieldsCount++;
          }
        } else {
          this.logger.debug(`⏭️ No value found for field '${fieldName}' - skipping`);
          skippedFieldsCount++;
        }
      }
      
      this.logger.debug(`Form field filling completed: ${filledFieldsCount} filled, ${skippedFieldsCount} skipped`);
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(`Error filling form fields: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Map form field names to template data values
   */
  private getFieldValue(fieldName: string, templateData: CalDocumentData): string | null {
    const fieldMappings: Record<string, string> = {
      // Student Information
      'studentName': templateData.studentName,
      'student_name': templateData.studentName,
      'name': templateData.studentName,
      'applicantName': templateData.studentName,
      'fullName': templateData.studentName,
      'student_full_name': templateData.studentName,
      'applicant_name': templateData.studentName,
      
      // Application Information
      'applicationId': templateData.applicationId.toString(),
      'application_id': templateData.applicationId.toString(),
      'appId': templateData.applicationId.toString(),
      'idNumber': templateData.idNumber,
      'id_number': templateData.idNumber,
      'application_number': templateData.applicationId.toString(),
      'app_number': templateData.applicationId.toString(),
      
      // Personal Information
      'passportNumber': templateData.passport,
      'passport_number': templateData.passport,
      'passport': templateData.passport,
      'passport_num': templateData.passport,
      'dateOfBirth': templateData.dateOfBirth,
      'date_of_birth': templateData.dateOfBirth,
      'birthday': templateData.dateOfBirth,
      'dob': templateData.dateOfBirth,
      'birth_date': templateData.dateOfBirth,
      'birthday_date': templateData.dateOfBirth,
      
      // Program Information
      'programName': templateData.courseName,
      'program_name': templateData.courseName,
      'courseName': templateData.courseName,
      'course_name': templateData.courseName,
      'program': templateData.courseName,
      'course': templateData.courseName,
      'program_title': templateData.courseName,
      'course_title': templateData.courseName,
      'study_program': templateData.courseName,
      
      // Academic Information
      'intake': templateData.intake,
      'session': templateData.intake,
      'intake_name': templateData.intake,
      'session_name': templateData.intake,
      'academic_session': templateData.intake,
      'campusName': templateData.campusName,
      'campus_name': templateData.campusName,
      'campus': templateData.campusName,
      'campus_location': templateData.campusName,
      'university_campus': templateData.campusName,
      
      // Financial Information
      'tuitionFee': templateData.tuitionFee,
      'tuition_fee': templateData.tuitionFee,
      'tuition': templateData.tuitionFee,
      'fee': templateData.tuitionFee,
      'tuition_amount': templateData.tuitionFee,
      'course_fee': templateData.tuitionFee,
      'program_fee': templateData.tuitionFee,
      
      // Dates
      'courseStartDate': templateData.courseStartDate,
      'course_start_date': templateData.courseStartDate,
      'startDate': templateData.courseStartDate,
      'start_date': templateData.courseStartDate,
      'program_start': templateData.courseStartDate,
      'courseEndDate': templateData.courseEndDate,
      'course_end_date': templateData.courseEndDate,
      'endDate': templateData.courseEndDate,
      'end_date': templateData.courseEndDate,
      'program_end': templateData.courseEndDate,
      'issueDate': templateData.issueDate,
      'issue_date': templateData.issueDate,
      'issued_date': templateData.issueDate,
      'document_date': templateData.issueDate,
      'generated_date': templateData.issueDate,
      
      // Additional mappings for common variations
      'student': templateData.studentName,
      'applicant': templateData.studentName,
      'id': templateData.idNumber,
      
      // Boolean fields (for checkboxes)
      'paymentStatus': 'true', // Assuming paid status
      'payment_status': 'true',
      'isPaid': 'true',
      'is_paid': 'true',
      'documentVerified': 'true',
      'document_verified': 'true',
      'termsAccepted': 'true',
      'terms_accepted': 'true',
      'agreementAccepted': 'true',
      'agreement_accepted': 'true',
    };
    
    // Try exact match first
    if (fieldMappings[fieldName]) {
      return fieldMappings[fieldName];
    }
    
    // Try case-insensitive match
    const lowerFieldName = fieldName.toLowerCase();
    for (const [key, value] of Object.entries(fieldMappings)) {
      if (key.toLowerCase() === lowerFieldName) {
        return value;
      }
    }
    
    // Try partial match for common patterns
    for (const [key, value] of Object.entries(fieldMappings)) {
      if (lowerFieldName.includes(key.toLowerCase()) || key.toLowerCase().includes(lowerFieldName)) {
        this.logger.debug(`Partial match found: '${fieldName}' matches '${key}'`);
        return value;
      }
    }
    
    // Try removing common prefixes/suffixes
    const cleanFieldName = fieldName
      .replace(/^(field_|txt_|fld_)/, '')
      .replace(/(_field|_txt|_fld)$/, '');
    
    if (fieldMappings[cleanFieldName]) {
      this.logger.debug(`Cleaned field name match: '${fieldName}' -> '${cleanFieldName}'`);
      return fieldMappings[cleanFieldName];
    }
    
    return null;
  }

  /**
   * Fill cover page fields
   */
  private async fillCoverPageFields(
    page: PDFPage,
    templateData: CalDocumentData,
    font: any,
    boldFont: any
  ): Promise<void> {
    const { width, height } = page.getSize();

    // Student name (centered, prominent position)
    page.drawText(templateData.studentName, {
      x: width / 2 - (templateData.studentName.length * 6), // Approximate centering
      y: height - 400,
      size: 22,
      font: boldFont,
      color: rgb(0, 0, 0),
    });

    // ID Number
    page.drawText(`ID: ${templateData.idNumber}`, {
      x: width / 2 - 50,
      y: height - 440,
      size: 14,
      font: font,
      color: rgb(0, 0, 0),
    });

    // Intake
    page.drawText(templateData.intake, {
      x: width / 2 - (templateData.intake.length * 4),
      y: height - 350,
      size: 18,
      font: boldFont,
      color: rgb(0.34, 0.34, 0.39), // #565964
    });

    // Issue date
    page.drawText(templateData.issueDate, {
      x: 50,
      y: 50,
      size: 10,
      font: font,
      color: rgb(0, 0, 0),
    });
  }

  /**
   * Fill page 02 fields
   */
  private async fillPage02Fields(
    page: PDFPage,
    templateData: CalDocumentData,
    font: any,
    boldFont: any
  ): Promise<void> {
    // Student information fields
    const fields = [
      { label: 'Student Name:', value: templateData.studentName, y: 650 },
      { label: 'Application ID:', value: templateData.applicationId.toString(), y: 620 },
      { label: 'Passport Number:', value: templateData.passport, y: 590 },
      { label: 'Date of Birth:', value: templateData.dateOfBirth, y: 560 },
      { label: 'Program:', value: templateData.courseName, y: 530 },
      { label: 'Intake:', value: templateData.intake, y: 500 },
      { label: 'Campus:', value: templateData.campusName, y: 470 },
      { label: 'Tuition Fee:', value: templateData.tuitionFee, y: 440 },
      { label: 'Course Start Date:', value: templateData.courseStartDate, y: 410 },
    ];

    fields.forEach(field => {
      // Label
      page.drawText(field.label, {
        x: 100,
        y: field.y,
        size: 10,
        font: boldFont,
        color: rgb(0, 0, 0),
      });

      // Value
      page.drawText(field.value, {
        x: 250,
        y: field.y,
        size: 10,
        font: font,
        color: rgb(0, 0, 0),
      });
    });
  }

  /**
   * Fill page 03 fields
   */
  private async fillPage03Fields(
    page: PDFPage,
    templateData: CalDocumentData,
    font: any,
    boldFont: any
  ): Promise<void> {
    // Additional information or terms and conditions
    // This can be customized based on the actual template content
    
    page.drawText('Terms and Conditions', {
      x: 100,
      y: 700,
      size: 16,
      font: boldFont,
      color: rgb(0, 0, 0),
    });

    // Add any additional fields specific to page 3
  }

  /**
   * Get template field positions based on template name
   */
  private getTemplateFieldPositions(templateName: string): Record<string, { x: number; y: number; size?: number; font?: string }> {
    const lowerName = templateName.toLowerCase();

    if (lowerName.includes('cover')) {
      return {
        studentName: { x: 297, y: 450, size: 22, font: 'bold' },
        idNumber: { x: 297, y: 420, size: 14 },
        intake: { x: 297, y: 480, size: 18, font: 'bold' },
        issueDate: { x: 50, y: 50, size: 10 }
      };
    } else if (lowerName.includes('page 02') || lowerName.includes('page-02')) {
      return {
        studentName: { x: 250, y: 650, size: 10 },
        applicationId: { x: 250, y: 620, size: 10 },
        passport: { x: 250, y: 590, size: 10 },
        dateOfBirth: { x: 250, y: 560, size: 10 },
        courseName: { x: 250, y: 530, size: 10 },
        intake: { x: 250, y: 500, size: 10 },
        campusName: { x: 250, y: 470, size: 10 },
        tuitionFee: { x: 250, y: 440, size: 10 },
        courseStartDate: { x: 250, y: 410, size: 10 }
      };
    }

    return {};
  }

  /**
   * Analyze PDF template to extract form fields (if any)
   */
  async analyzeTemplateFields(templatePath: string): Promise<void> {
    try {
      const templateBytes = await fs.promises.readFile(templatePath);
      const templateDoc = await PDFDocument.load(templateBytes as any);

      const form = templateDoc.getForm();
      const fields = form.getFields();

      this.logger.debug(`Template ${path.basename(templatePath)} has ${fields.length} form fields:`);

      fields.forEach((field, index) => {
        const fieldName = field.getName();
        this.logger.debug(`  ${index + 1}. ${fieldName} (${field.constructor.name})`);
      });

      // Get page dimensions for reference
      const pages = templateDoc.getPages();
      pages.forEach((page, pageIndex) => {
        const { width, height } = page.getSize();
        this.logger.debug(`  Page ${pageIndex + 1} dimensions: ${width} x ${height}`);
      });

          } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        this.logger.debug(`Could not analyze template fields: ${errorMessage}`);
      }
  }

  /**
   * Enhanced field filling with precise positioning
   */
  private async fillFieldsWithPrecisePositioning(
    page: PDFPage,
    templateName: string,
    templateData: CalDocumentData,
    font: any,
    boldFont: any
  ): Promise<void> {
    const fieldPositions = this.getTemplateFieldPositions(templateName);

    for (const [fieldName, position] of Object.entries(fieldPositions)) {
      let value = '';

      // Map field names to data
      switch (fieldName) {
        case 'studentName':
          value = templateData.studentName;
          break;
        case 'idNumber':
          value = templateData.idNumber;
          break;
        case 'applicationId':
          value = templateData.applicationId.toString();
          break;
        case 'passport':
          value = templateData.passport;
          break;
        case 'dateOfBirth':
          value = templateData.dateOfBirth;
          break;
        case 'courseName':
          value = templateData.courseName;
          break;
        case 'intake':
          value = templateData.intake;
          break;
        case 'campusName':
          value = templateData.campusName;
          break;
        case 'tuitionFee':
          value = templateData.tuitionFee;
          break;
        case 'courseStartDate':
          value = templateData.courseStartDate;
          break;
        case 'issueDate':
          value = templateData.issueDate;
          break;
      }

      if (value) {
        const selectedFont = position.font === 'bold' ? boldFont : font;
        const fontSize = position.size || 12;

        page.drawText(value, {
          x: position.x,
          y: position.y,
          size: fontSize,
          font: selectedFont,
          color: rgb(0, 0, 0),
        });

        this.logger.debug(`Filled field ${fieldName} with value "${value}" at position (${position.x}, ${position.y})`);
      }
    }
  }

  /**
   * Get available PDF templates for admission package
   */
  private async getAvailableAdmissionPackagePdfTemplates(applicationType: string): Promise<Array<{
    name: string;
    path: string;
    pageNumber: number;
  }>> {
    try {
      // Map application type to folder name
      const folderMapping: { [key: string]: string } = {
        'F-1 Initial': 'F-1 Initial',
        'F-1 Transfer': 'F-1 Transfer (Admission Package ADM)',
        'F-1 Reinstatement': 'F-1 Reinstatement',
        'New Program': 'New Program',
        'Change of Status': 'Change of status',
        'Non-F-1': 'Non F1'
      };

      const folderName = folderMapping[applicationType] || 'F-1 Initial';
      const templatesPath = path.join(
        process.cwd(),
        'apps/services/students-service/src/assets/pdf-templates/Admission Package',
        folderName
      );
      
      // Check if directory exists
      const dirExists = await fs.promises.access(templatesPath).then(() => true).catch(() => false);
      if (!dirExists) {
        this.logger.warn(`Admission package PDF templates directory not found: ${templatesPath}`);
        return [];
      }

      const files = await fs.promises.readdir(templatesPath);
      const templateFiles = files.filter(file => file.toLowerCase().endsWith('.pdf'));

      this.logger.debug(`Found admission package PDF templates: ${templateFiles.join(', ')}`);

      const templates = templateFiles.map((file, index) => {
        return {
          name: file.replace('.pdf', ''),
          path: path.join(templatesPath, file),
          pageNumber: index + 1
        };
      });

      this.logger.debug(`Found ${templates.length} admission package PDF templates for ${applicationType}`);
      return templates;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(`Error reading admission package PDF templates: ${errorMessage}`);
      return [];
    }
  }

  /**
   * Get available PDF templates for admission portfolio
   */
  private async getAvailableAdmissionPortfolioPdfTemplates(applicationType: string): Promise<Array<{
    name: string;
    path: string;
    pageNumber: number;
  }>> {
    try {
      // Map application type to folder name
      const folderMapping: { [key: string]: string } = {
        'F-1 Initial': 'Initial Student Admission Portfolio',
        'F-1 Transfer': 'Transfer Student Admission Portfolio',
        'F-1 Reinstatement': 'F-1 Reinstatement Admission Portfolio',
        'New Program': 'New Program Admission Portfolio',
        'Change of Status': 'Change of Status Admission Portfolio',
        'Non-F-1': 'Non F1 Student Admission Portfolio'
      };

      const folderName = folderMapping[applicationType] || 'Initial Student Admission Portfolio';
      const templatesPath = path.join(
        process.cwd(),
        'apps/services/students-service/src/assets/pdf-templates/Admission Portfolio',
        folderName
      );
      
      // Check if directory exists
      const dirExists = await fs.promises.access(templatesPath).then(() => true).catch(() => false);
      if (!dirExists) {
        this.logger.warn(`Admission portfolio PDF templates directory not found: ${templatesPath}`);
        return [];
      }

      const files = await fs.promises.readdir(templatesPath);
      const templateFiles = files.filter(file => file.toLowerCase().endsWith('.pdf'));

      this.logger.debug(`Found admission portfolio PDF templates: ${templateFiles.join(', ')}`);

      const templates = templateFiles.map((file, index) => {
        return {
          name: file.replace('.pdf', ''),
          path: path.join(templatesPath, file),
          pageNumber: index + 1
        };
      });

      this.logger.debug(`Found ${templates.length} admission portfolio PDF templates for ${applicationType}`);
      return templates;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(`Error reading admission portfolio PDF templates: ${errorMessage}`);
      return [];
    }
  }

  /**
   * Process admission package template page
   */
  private async processAdmissionPackageTemplatePage(
    pdfDoc: PDFDocument,
    templateInfo: { name: string; path: string; pageNumber: number },
    templateData: AdmissionPackageData
  ): Promise<void> {
    try {
      this.logger.debug(`Processing admission package template: ${templateInfo.name}`);

      // Load the template PDF
      const templateBytes = await fs.promises.readFile(templateInfo.path);
      const templateDoc = await PDFDocument.load(templateBytes as any);

      // Copy pages from template to the main document
      const pages = await pdfDoc.copyPages(templateDoc, templateDoc.getPageIndices());
      
      for (const page of pages) {
        pdfDoc.addPage(page);
        
        // Fill form fields if any
        await this.fillAdmissionPackageFields(page, templateInfo.name, templateData);
      }

      this.logger.debug(`Successfully processed admission package template: ${templateInfo.name}`);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(`Error processing admission package template ${templateInfo.name}: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Process admission portfolio template page
   */
  private async processAdmissionPortfolioTemplatePage(
    pdfDoc: PDFDocument,
    templateInfo: { name: string; path: string; pageNumber: number },
    templateData: AdmissionPortfolioData
  ): Promise<void> {
    try {
      this.logger.debug(`Processing admission portfolio template: ${templateInfo.name}`);

      // Load the template PDF
      const templateBytes = await fs.promises.readFile(templateInfo.path);
      const templateDoc = await PDFDocument.load(templateBytes as any);

      // Copy pages from template to the main document
      const pages = await pdfDoc.copyPages(templateDoc, templateDoc.getPageIndices());
      
      for (const page of pages) {
        pdfDoc.addPage(page);
        
        // Fill form fields if any
        await this.fillAdmissionPortfolioFields(page, templateInfo.name, templateData);
      }

      this.logger.debug(`Successfully processed admission portfolio template: ${templateInfo.name}`);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(`Error processing admission portfolio template ${templateInfo.name}: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Fill admission portfolio form fields
   */
  private async fillAdmissionPortfolioFields(
    page: PDFPage,
    templateName: string,
    templateData: AdmissionPortfolioData
  ): Promise<void> {
    try {
      const font = await page.doc.embedFont(StandardFonts.Helvetica);
      const boldFont = await page.doc.embedFont(StandardFonts.HelveticaBold);

      // Define field positions for admission portfolio templates
      const fieldPositions = this.getAdmissionPortfolioFieldPositions(templateName);

      for (const [fieldName, position] of Object.entries(fieldPositions)) {
        let value = '';

        // Map field names to data
        switch (fieldName) {
          case 'studentName':
            value = templateData.studentName;
            break;
          case 'studentId':
            value = templateData.studentId;
            break;
          case 'email':
            value = templateData.email;
            break;
          case 'phone':
            value = templateData.phone;
            break;
          case 'dateOfBirth':
            value = templateData.dateOfBirth;
            break;
          case 'passport':
            value = templateData.passport;
            break;
          case 'nid':
            value = templateData.nid;
            break;
          case 'presentAddress':
            value = templateData.presentAddress;
            break;
          case 'permanentAddress':
            value = templateData.permanentAddress;
            break;
          case 'fatherName':
            value = templateData.fatherName;
            break;
          case 'motherName':
            value = templateData.motherName;
            break;
          case 'gender':
            value = templateData.gender;
            break;
          case 'maritalStatus':
            value = templateData.maritalStatus;
            break;
          case 'sponsor':
            value = templateData.sponsor;
            break;
          case 'emergencyContact':
            value = templateData.emergencyContact;
            break;
          case 'portfolioNumber':
            value = templateData.portfolioNumber;
            break;
          case 'portfolioDate':
            value = templateData.portfolioDate;
            break;
          case 'universityName':
            value = templateData.universityName;
            break;
          case 'courseName':
            value = templateData.courseName;
            break;
          case 'intakeName':
            value = templateData.intakeName;
            break;
          case 'campusName':
            value = templateData.campusName;
            break;
          case 'tuitionFee':
            value = templateData.tuitionFee;
            break;
          case 'courseStartDate':
            value = templateData.courseStartDate;
            break;
          case 'courseEndDate':
            value = templateData.courseEndDate;
            break;
          case 'issueDate':
            value = templateData.issueDate;
            break;
        }

        if (value) {
          const selectedFont = position.font === 'bold' ? boldFont : font;
          const fontSize = position.size || 12;

          page.drawText(value, {
            x: position.x,
            y: position.y,
            size: fontSize,
            font: selectedFont,
            color: rgb(0, 0, 0),
          });

          this.logger.debug(`Filled admission portfolio field ${fieldName} with value "${value}" at position (${position.x}, ${position.y})`);
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(`Error filling admission portfolio fields: ${errorMessage}`);
    }
  }

  /**
   * Get field positions for admission portfolio templates
   */
  private getAdmissionPortfolioFieldPositions(templateName: string): { [key: string]: { x: number; y: number; size?: number; font?: string } } {
    // Default positions - these would need to be customized based on actual template layouts
    const defaultPositions = {
      studentName: { x: 100, y: 700, size: 12, font: 'bold' },
      studentId: { x: 100, y: 680, size: 10 },
      email: { x: 100, y: 660, size: 10 },
      phone: { x: 100, y: 640, size: 10 },
      dateOfBirth: { x: 100, y: 620, size: 10 },
      passport: { x: 100, y: 600, size: 10 },
      nid: { x: 100, y: 580, size: 10 },
      presentAddress: { x: 100, y: 560, size: 10 },
      permanentAddress: { x: 100, y: 540, size: 10 },
      fatherName: { x: 100, y: 520, size: 10 },
      motherName: { x: 100, y: 500, size: 10 },
      gender: { x: 100, y: 480, size: 10 },
      maritalStatus: { x: 100, y: 460, size: 10 },
      sponsor: { x: 100, y: 440, size: 10 },
      emergencyContact: { x: 100, y: 420, size: 10 },
      portfolioNumber: { x: 100, y: 400, size: 12, font: 'bold' },
      portfolioDate: { x: 100, y: 380, size: 10 },
      universityName: { x: 100, y: 360, size: 10 },
      courseName: { x: 100, y: 340, size: 10 },
      intakeName: { x: 100, y: 320, size: 10 },
      campusName: { x: 100, y: 300, size: 10 },
      tuitionFee: { x: 100, y: 280, size: 10 },
      courseStartDate: { x: 100, y: 260, size: 10 },
      courseEndDate: { x: 100, y: 240, size: 10 },
      issueDate: { x: 100, y: 220, size: 10 }
    };

    // Return default positions for now - can be customized per template later
    return defaultPositions;
  }

  /**
   * Fill admission package form fields
   */
  private async fillAdmissionPackageFields(
    page: PDFPage,
    templateName: string,
    templateData: AdmissionPackageData
  ): Promise<void> {
    try {
      const font = await page.doc.embedFont(StandardFonts.Helvetica);
      const boldFont = await page.doc.embedFont(StandardFonts.HelveticaBold);

      // Define field positions for admission package templates
      const fieldPositions = this.getAdmissionPackageFieldPositions(templateName);

      for (const [fieldName, position] of Object.entries(fieldPositions)) {
        let value = '';

        // Map field names to data
        switch (fieldName) {
          case 'studentName':
            value = templateData.studentName;
            break;
          case 'studentId':
            value = templateData.studentId;
            break;
          case 'email':
            value = templateData.email;
            break;
          case 'phone':
            value = templateData.phone;
            break;
          case 'dateOfBirth':
            value = templateData.dateOfBirth;
            break;
          case 'passport':
            value = templateData.passport;
            break;
          case 'nid':
            value = templateData.nid;
            break;
          case 'presentAddress':
            value = templateData.presentAddress;
            break;
          case 'permanentAddress':
            value = templateData.permanentAddress;
            break;
          case 'fatherName':
            value = templateData.fatherName;
            break;
          case 'motherName':
            value = templateData.motherName;
            break;
          case 'gender':
            value = templateData.gender;
            break;
          case 'maritalStatus':
            value = templateData.maritalStatus;
            break;
          case 'sponsor':
            value = templateData.sponsor;
            break;
          case 'emergencyContact':
            value = templateData.emergencyContact;
            break;
          case 'issueDate':
            value = templateData.issueDate;
            break;
        }

        if (value) {
          const selectedFont = position.font === 'bold' ? boldFont : font;
          const fontSize = position.size || 12;

          page.drawText(value, {
            x: position.x,
            y: position.y,
            size: fontSize,
            font: selectedFont,
            color: rgb(0, 0, 0),
          });

          this.logger.debug(`Filled admission package field ${fieldName} with value "${value}" at position (${position.x}, ${position.y})`);
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(`Error filling admission package fields: ${errorMessage}`);
    }
  }

  /**
   * Get field positions for admission package templates
   */
  private getAdmissionPackageFieldPositions(templateName: string): { [key: string]: { x: number; y: number; size?: number; font?: string } } {
    // Default positions - these would need to be customized based on actual template layouts
    const defaultPositions = {
      studentName: { x: 100, y: 700, size: 12, font: 'bold' },
      studentId: { x: 100, y: 680, size: 10 },
      email: { x: 100, y: 660, size: 10 },
      phone: { x: 100, y: 640, size: 10 },
      dateOfBirth: { x: 100, y: 620, size: 10 },
      passport: { x: 100, y: 600, size: 10 },
      nid: { x: 100, y: 580, size: 10 },
      presentAddress: { x: 100, y: 560, size: 10 },
      permanentAddress: { x: 100, y: 540, size: 10 },
      fatherName: { x: 100, y: 520, size: 10 },
      motherName: { x: 100, y: 500, size: 10 },
      gender: { x: 100, y: 480, size: 10 },
      maritalStatus: { x: 100, y: 460, size: 10 },
      sponsor: { x: 100, y: 440, size: 10 },
      emergencyContact: { x: 100, y: 420, size: 10 },
      issueDate: { x: 100, y: 400, size: 10 }
    };

    // Template-specific positions can be added here
    const templatePositions: { [key: string]: { [key: string]: { x: number; y: number; size?: number; font?: string } } } = {
      // Add specific template positions here
    };

    return templatePositions[templateName] || defaultPositions;
  }

  /**
   * Get available PDF templates for ADM document
   */
  private async getAvailableAdmDocumentPdfTemplates(applicationType: string): Promise<Array<{
    name: string;
    path: string;
    pageNumber: number;
  }>> {
    try {
      // Map application type to folder name (handle actual database values)
      const folderMapping: { [key: string]: string } = {
        'F-1 Intial': 'F-1 Initial',        // Your DB value → Folder name
        'Reinstatement': 'F-1 Reinstatement', // Your DB value → Folder name
        'New Program': 'New Program',         // Your DB value → Folder name
        'Change of Status': 'Change of status', // Your DB value → Folder name
        'Non-F-1': 'Non F1',                // Your DB value → Folder name
        'Transfer': 'F-1 Transfer (Admission Package ADM)', // Your DB value → Folder name
        
        // Keep backward compatibility with any existing variations
        'F-1 Initial': 'F-1 Initial',
        'F-1 Reinstatement': 'F-1 Reinstatement',
        'F-1 Transfer': 'F-1 Transfer (Admission Package ADM)'
      };

      const folderName = folderMapping[applicationType] || 'F-1 Initial';
      const templatesPath = path.join(
        process.cwd(),
        'apps/services/students-service/src/assets/pdf-templates/Admission Package',
        folderName
      );
      
      // Check if directory exists
      const dirExists = await fs.promises.access(templatesPath).then(() => true).catch(() => false);
      if (!dirExists) {
        this.logger.warn(`ADM document PDF templates directory not found: ${templatesPath}`);
        return [];
      }

      const files = await fs.promises.readdir(templatesPath);
      const templateFiles = files.filter(file => file.toLowerCase().endsWith('.pdf'));

      this.logger.debug(`Found ADM document PDF templates: ${templateFiles.join(', ')}`);

      const templates = templateFiles.map((file, index) => {
        return {
          name: file.replace('.pdf', ''),
          path: path.join(templatesPath, file),
          pageNumber: index + 1
        };
      });

      this.logger.debug(`Found ${templates.length} ADM document PDF templates for ${applicationType}`);
      return templates;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(`Error reading ADM document PDF templates: ${errorMessage}`);
      return [];
    }
  }

  /**
   * Process ADM document template page
   */
  private async processAdmDocumentTemplatePage(
    pdfDoc: PDFDocument,
    templateInfo: { name: string; path: string; pageNumber: number },
    templateData: AdmDocumentData
  ): Promise<void> {
    try {
      this.logger.debug(`Processing ADM document template: ${templateInfo.name}`);

      // Load the template PDF
      const templateBytes = await fs.promises.readFile(templateInfo.path);
      const templateDoc = await PDFDocument.load(templateBytes as any);

      // Copy pages from template to the main document
      const pages = await pdfDoc.copyPages(templateDoc, templateDoc.getPageIndices());
      
      for (const page of pages) {
        pdfDoc.addPage(page);
        
        // Fill form fields if any
        await this.fillAdmDocumentFields(page, templateInfo.name, templateData);
      }

      this.logger.debug(`Successfully processed ADM document template: ${templateInfo.name}`);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(`Error processing ADM document template ${templateInfo.name}: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Fill ADM document form fields
   */
  private async fillAdmDocumentFields(
    page: PDFPage,
    templateName: string,
    templateData: AdmDocumentData
  ): Promise<void> {
    try {
      const font = await page.doc.embedFont(StandardFonts.Helvetica);
      const boldFont = await page.doc.embedFont(StandardFonts.HelveticaBold);

      // Define field positions for ADM document templates
      const fieldPositions = this.getAdmDocumentFieldPositions(templateName);

      for (const [fieldName, position] of Object.entries(fieldPositions)) {
        let value = '';

        // Map field names to data
        switch (fieldName) {
          case 'studentName':
            value = templateData.studentName;
            break;
          case 'studentId':
            value = templateData.studentId;
            break;
          case 'email':
            value = templateData.email;
            break;
          case 'phone':
            value = templateData.phone;
            break;
          case 'dateOfBirth':
            value = templateData.dateOfBirth;
            break;
          case 'passport':
            value = templateData.passport;
            break;
          case 'nid':
            value = templateData.nid;
            break;
          case 'presentAddress':
            value = templateData.presentAddress;
            break;
          case 'permanentAddress':
            value = templateData.permanentAddress;
            break;
          case 'fatherName':
            value = templateData.fatherName;
            break;
          case 'motherName':
            value = templateData.motherName;
            break;
          case 'gender':
            value = templateData.gender;
            break;
          case 'maritalStatus':
            value = templateData.maritalStatus;
            break;
          case 'sponsor':
            value = templateData.sponsor;
            break;
          case 'emergencyContact':
            value = templateData.emergencyContact;
            break;
          case 'admNumber':
            value = templateData.admNumber;
            break;
          case 'admDate':
            value = templateData.admDate;
            break;
          case 'universityName':
            value = templateData.universityName;
            break;
          case 'courseName':
            value = templateData.courseName;
            break;
          case 'intakeName':
            value = templateData.intakeName;
            break;
          case 'campusName':
            value = templateData.campusName;
            break;
          case 'tuitionFee':
            value = templateData.tuitionFee;
            break;
          case 'courseStartDate':
            value = templateData.courseStartDate;
            break;
          case 'courseEndDate':
            value = templateData.courseEndDate;
            break;
          case 'issueDate':
            value = templateData.issueDate;
            break;
        }

        if (value) {
          const selectedFont = position.font === 'bold' ? boldFont : font;
          const fontSize = position.size || 12;

          page.drawText(value, {
            x: position.x,
            y: position.y,
            size: fontSize,
            font: selectedFont,
            color: rgb(0, 0, 0),
          });

          this.logger.debug(`Filled ADM document field ${fieldName} with value "${value}" at position (${position.x}, ${position.y})`);
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(`Error filling ADM document fields: ${errorMessage}`);
    }
  }

  /**
   * Get field positions for ADM document templates
   */
  private getAdmDocumentFieldPositions(templateName: string): { [key: string]: { x: number; y: number; size?: number; font?: string } } {
    // Default positions - these would need to be customized based on actual template layouts
    const defaultPositions = {
      studentName: { x: 100, y: 700, size: 12, font: 'bold' },
      studentId: { x: 100, y: 680, size: 10 },
      email: { x: 100, y: 660, size: 10 },
      phone: { x: 100, y: 640, size: 10 },
      dateOfBirth: { x: 100, y: 620, size: 10 },
      passport: { x: 100, y: 600, size: 10 },
      nid: { x: 100, y: 580, size: 10 },
      presentAddress: { x: 100, y: 560, size: 10 },
      permanentAddress: { x: 100, y: 540, size: 10 },
      fatherName: { x: 100, y: 520, size: 10 },
      motherName: { x: 100, y: 500, size: 10 },
      gender: { x: 100, y: 480, size: 10 },
      maritalStatus: { x: 100, y: 460, size: 10 },
      sponsor: { x: 100, y: 440, size: 10 },
      emergencyContact: { x: 100, y: 420, size: 10 },
      admNumber: { x: 100, y: 400, size: 12, font: 'bold' },
      admDate: { x: 100, y: 380, size: 10 },
      universityName: { x: 100, y: 360, size: 10 },
      courseName: { x: 100, y: 340, size: 10 },
      intakeName: { x: 100, y: 320, size: 10 },
      campusName: { x: 100, y: 300, size: 10 },
      tuitionFee: { x: 100, y: 280, size: 10 },
      courseStartDate: { x: 100, y: 260, size: 10 },
      courseEndDate: { x: 100, y: 240, size: 10 },
      issueDate: { x: 100, y: 220, size: 10 }
    };

    // Template-specific positions can be added here
    const templatePositions: { [key: string]: { [key: string]: { x: number; y: number; size?: number; font?: string } } } = {
      // Add specific template positions here
    };

    return templatePositions[templateName] || defaultPositions;
  }
}
