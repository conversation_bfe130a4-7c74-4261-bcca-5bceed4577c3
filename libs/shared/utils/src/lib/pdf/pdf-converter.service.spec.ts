import { Test, TestingModule } from '@nestjs/testing';
import { PdfConverterService } from './pdf-converter.service';
import * as fs from 'fs';
import * as path from 'path';

describe('PdfConverterService', () => {
  let service: PdfConverterService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [PdfConverterService],
    }).compile();

    service = module.get<PdfConverterService>(PdfConverterService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('convertHtmlStringToPdf', () => {
    it('should process HTML string with Handlebars template', async () => {
      const htmlContent = `
        <html>
          <head>
            <title>{{title}}</title>
          </head>
          <body>
            <h1>{{title}}</h1>
            <p>{{content}}</p>
            {{#if showExtra}}
              <div>{{extraContent}}</div>
            {{/if}}
          </body>
        </html>
      `;

      const data = {
        title: 'Test Document',
        content: 'This is test content',
        showExtra: true,
        extraContent: 'Extra information'
      };

      const result = await service.convertHtmlStringToPdf(htmlContent, data);
      
      expect(result).toBeDefined();
      expect(Buffer.isBuffer(result)).toBe(true);
    });

    it('should handle empty data', async () => {
      const htmlContent = '<html><body><h1>{{title}}</h1></body></html>';
      const result = await service.convertHtmlStringToPdf(htmlContent, {});
      
      expect(result).toBeDefined();
      expect(Buffer.isBuffer(result)).toBe(true);
    });
  });

  describe('processHtmlTemplate', () => {
    it('should process HTML template with data', async () => {
      // Create a temporary test template
      const testTemplatePath = path.join(__dirname, 'test-template.html');
      const testHtml = `
        <html>
          <body>
            <h1>{{studentName}}</h1>
            <p>Program: {{program}}</p>
            <p>University: {{universityName}}</p>
          </body>
        </html>
      `;

      try {
        await fs.promises.writeFile(testTemplatePath, testHtml);

        const data = {
          studentName: 'John Doe',
          program: 'Computer Science',
          universityName: 'Test University'
        };

        const result = await service.processHtmlTemplate(testTemplatePath, data);
        
        expect(result).toContain('John Doe');
        expect(result).toContain('Computer Science');
        expect(result).toContain('Test University');
        expect(result).toContain('<h1>John Doe</h1>');
      } finally {
        // Clean up
        if (fs.existsSync(testTemplatePath)) {
          await fs.promises.unlink(testTemplatePath);
        }
      }
    });

    it('should throw error for non-existent template', async () => {
      const nonExistentPath = '/path/to/non-existent/template.html';
      
      await expect(
        service.processHtmlTemplate(nonExistentPath, {})
      ).rejects.toThrow('Failed to read HTML template');
    });
  });

  describe('getAvailableTemplates', () => {
    it('should return available templates', async () => {
      const templates = await service.getAvailableTemplates();
      
      expect(Array.isArray(templates)).toBe(true);
      // Should include the existing CAL-Page-4 template
      expect(templates).toContain('CAL-Page-4');
    });
  });

  describe('convertStudentsTemplateToPdf', () => {
    it('should convert students template to PDF', async () => {
      const data = {
        studentName: 'John Doe',
        universityName: 'International American University',
        program: 'Computer Science'
      };

      const result = await service.convertStudentsTemplateToPdf(
        'CAL-Page-4',
        data
      );
      
      expect(result).toBeDefined();
      expect(Buffer.isBuffer(result)).toBe(true);
    });

    it('should save PDF to file when outputPath is provided', async () => {
      const outputPath = path.join(__dirname, 'test-output.pdf');
      
      try {
        const data = {
          studentName: 'Jane Smith',
          universityName: 'Test University'
        };

        const result = await service.convertStudentsTemplateToPdf(
          'CAL-Page-4',
          data,
          outputPath
        );
        
        expect(result).toBeDefined();
        expect(Buffer.isBuffer(result)).toBe(true);
        
        // Check if file was created
        expect(fs.existsSync(outputPath)).toBe(true);
      } finally {
        // Clean up
        if (fs.existsSync(outputPath)) {
          await fs.promises.unlink(outputPath);
        }
      }
    });
  });

  describe('processStudentsTemplate', () => {
    it('should process students template with data', async () => {
      const data = {
        studentName: 'John Doe',
        universityName: 'International American University'
      };

      const result = await service.processStudentsTemplate('CAL-Page-4', data);
      
      expect(typeof result).toBe('string');
      expect(result).toContain('International American University');
    });
  });

  describe('convertHtmlToPdf', () => {
    it('should convert HTML template to PDF', async () => {
      // Create a temporary test template
      const testTemplatePath = path.join(__dirname, 'test-template.html');
      const testHtml = `
        <html>
          <body>
            <h1>{{title}}</h1>
            <p>{{content}}</p>
          </body>
        </html>
      `;

      try {
        await fs.promises.writeFile(testTemplatePath, testHtml);

        const config = {
          templatePath: testTemplatePath,
          data: {
            title: 'Test Document',
            content: 'Test content'
          },
          options: {
            format: 'A4' as const,
            printBackground: true
          }
        };

        const result = await service.convertHtmlToPdf(config);
        
        expect(result).toBeDefined();
        expect(Buffer.isBuffer(result)).toBe(true);
      } finally {
        // Clean up
        if (fs.existsSync(testTemplatePath)) {
          await fs.promises.unlink(testTemplatePath);
        }
      }
    });
  });
}); 