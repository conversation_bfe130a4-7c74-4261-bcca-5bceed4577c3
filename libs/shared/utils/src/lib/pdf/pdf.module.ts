import { Module } from '@nestjs/common';
import { PdfConverterService } from './pdf-converter.service';
import { PdfTemplateService } from './pdf-template.service';
import { HtmlTemplateService } from './html-template.service';

@Module({
  providers: [PdfConverterService, PdfTemplateService, HtmlTemplateService],
  exports: [PdfConverterService, PdfTemplateService, HtmlTemplateService],
})
export class PdfModule {}